import { test, expect } from '@playwright/test'

test.describe('Add Contact Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the inbox/case page with a test case
    await page.goto('/inbox/cases/test-case-id')
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle')
  })

  test('should display Add Contact button in contacts section', async ({ page }) => {
    // Expand the contacts accordion section
    await page.click('text=Contacts')
    
    // Wait for the section to expand
    await page.waitForSelector('button:has-text("Add Contact")', { timeout: 5000 })
    
    // Verify the Add Contact button is visible
    const addContactButton = page.locator('button:has-text("Add Contact")')
    await expect(addContactButton).toBeVisible()
  })

  test('should open Add Contact modal when button is clicked', async ({ page }) => {
    // Expand the contacts accordion section
    await page.click('text=Contacts')
    
    // Click the Add Contact button
    await page.click('button:has-text("Add Contact")')
    
    // Verify the modal opens
    await expect(page.locator('text=Add Contact').first()).toBeVisible()
    await expect(page.locator('[data-testid="first-name-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="last-name-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="phone-input"]')).toBeVisible()
  })

  test('should validate required fields', async ({ page }) => {
    // Open the Add Contact modal
    await page.click('text=Contacts')
    await page.click('button:has-text("Add Contact")')
    
    // Try to submit without filling required fields
    const createButton = page.locator('[data-testid="create-contact-btn"]')
    await expect(createButton).toBeDisabled()
    
    // Fill in first name only
    await page.fill('[data-testid="first-name-input"]', 'John')
    await expect(createButton).toBeDisabled()
    
    // Fill in last name
    await page.fill('[data-testid="last-name-input"]', 'Doe')
    await expect(createButton).toBeDisabled()
    
    // Fill in email (all required fields now filled)
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await expect(createButton).toBeEnabled()
  })

  test('should successfully create a contact', async ({ page }) => {
    // Open the Add Contact modal
    await page.click('text=Contacts')
    await page.click('button:has-text("Add Contact")')
    
    // Fill in the form
    await page.fill('[data-testid="first-name-input"]', 'Jane')
    await page.fill('[data-testid="last-name-input"]', 'Smith')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="phone-input"]', '(*************')
    
    // Submit the form
    await page.click('[data-testid="create-contact-btn"]')
    
    // Wait for the modal to close (indicating success)
    await expect(page.locator('text=Add Contact').first()).not.toBeVisible()
    
    // Verify the new contact appears in the contacts list
    // Note: This depends on the mock API returning the new contact
    await expect(page.locator('text=Jane Smith')).toBeVisible()
  })

  test('should close modal when cancel is clicked', async ({ page }) => {
    // Open the Add Contact modal
    await page.click('text=Contacts')
    await page.click('button:has-text("Add Contact")')
    
    // Fill in some data
    await page.fill('[data-testid="first-name-input"]', 'Test')
    
    // Click cancel
    await page.click('[data-testid="cancel-contact-btn"]')
    
    // Verify the modal closes
    await expect(page.locator('text=Add Contact').first()).not.toBeVisible()
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock an API error response
    await page.route('**/admin/v4/members/**', route => {
      if (route.request().method() === 'POST') {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            message: 'Internal server error'
          })
        })
      } else {
        route.continue()
      }
    })
    
    // Open the Add Contact modal and fill the form
    await page.click('text=Contacts')
    await page.click('button:has-text("Add Contact")')
    
    await page.fill('[data-testid="first-name-input"]', 'Error')
    await page.fill('[data-testid="last-name-input"]', 'Test')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    
    // Submit the form
    await page.click('[data-testid="create-contact-btn"]')
    
    // The modal should remain open and show an error
    // (The exact error handling depends on the implementation)
    await expect(page.locator('[data-testid="create-contact-btn"]')).toBeVisible()
  })
}) 