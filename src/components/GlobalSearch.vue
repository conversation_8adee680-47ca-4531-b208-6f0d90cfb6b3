<template>
  <BravoDialog
    :visible="globalSearchStore.isModalVisible"
    @update:visible="(value) => value ? globalSearchStore.openModal() : globalSearchStore.closeModal()"
    header="Global Search"
    :modal="true"
    :style="{ width: '800px', maxHeight: '80vh' }"
    :closable="true"
    :closeOnEscape="true"
    :dismissableMask="true"
    class="global-search-modal"
  >
    <div class="global-search-content">
      <!-- Search Input -->
      <div class="search-input-section">
        <div class="search-input-wrapper">
          <BravoInputText
            ref="searchInputRef"
            v-model="globalSearchStore.searchQuery"
            placeholder="Search cases, customers, journeys, and more..."
            class="search-input"
            :disabled="globalSearchStore.isSearching"
            @keyup.enter="handleSearch"
            @input="handleInputChange"
            autofocus
          />
          <BravoButton
            v-if="globalSearchStore.searchQuery.trim()"
            icon="pi pi-times"
            text
            severity="secondary"
            size="small"
            @click="handleClearSearch"
            class="clear-button"
            aria-label="Clear search"
            :disabled="globalSearchStore.isSearching"
          />
          <BravoButton
            icon="pi pi-search"
            :loading="globalSearchStore.isSearching"
            @click="handleSearch"
            class="search-button"
            :disabled="!globalSearchStore.searchQuery.trim()"
          />
        </div>
        
        <!-- Search History and Recent Pages -->
        <div v-if="!globalSearchStore.hasResults" class="zero-state-content">
          <!-- Quick Actions -->
          <div class="quick-actions">
            <div class="history-header">
              <h4>Actions</h4>
            </div>
            <div v-if="filteredActions.length > 0" class="history-items">
              <div
                v-for="(action, index) in filteredActions"
                :key="action.id"
                class="history-item action-item"
                :class="{ 'action-hidden': !globalSearchStore.searchQuery.trim() && index >= 4 }"
                @click="action.action()"
              >
                <i :class="action.icon" :style="{ color: action.type === 'create' ? 'var(--green-500)' : 'var(--primary-color)' }"></i>
                <div class="action-content">
                  <span class="action-title">{{ action.title }}</span>
                  <span v-if="action.subtitle" class="action-subtitle">{{ action.subtitle }}</span>
                </div>
                <i class="pi pi-chevron-right action-arrow"></i>
              </div>
            </div>
            <div v-else class="empty-state">
              <span class="empty-text">{{ globalSearchStore.searchQuery.trim() ? 'No matching actions' : 'No actions available' }}</span>
            </div>
          </div>

          <!-- Search History -->
          <div class="search-history">
            <div class="history-header">
              <h4>Recent Searches</h4>
              <BravoButton
                v-if="globalSearchStore.hasHistory && !globalSearchStore.searchQuery.trim()"
                label="Clear"
                text
                size="small"
                @click="globalSearchStore.clearHistory"
              />
            </div>
            <div v-if="filteredSearchHistory.length > 0" class="history-items">
              <div
                v-for="(historyItem, index) in filteredSearchHistory"
                :key="historyItem.query"
                class="history-item"
                :class="{ 'history-hidden': !globalSearchStore.searchQuery.trim() && index >= 5 }"
                @click="selectHistoryItem(historyItem)"
              >
                <i class="pi pi-history"></i>
                <span class="history-query">{{ historyItem.query }}</span>
                <span class="history-time">{{ formatTime(historyItem.timestamp) }}</span>
              </div>
            </div>
            <div v-else class="empty-state">
              <span class="empty-text">{{ globalSearchStore.searchQuery.trim() ? 'No matching searches' : 'No recent searches' }}</span>
            </div>
          </div>

          <!-- Recent Page Visits -->
          <div class="recent-pages">
            <div class="history-header">
              <h4>Recent Pages</h4>
              <BravoButton
                v-if="recentPages.length > 0 && !globalSearchStore.searchQuery.trim()"
                label="Clear"
                text
                size="small"
                @click="clearRecentPages"
              />
            </div>
            <div v-if="filteredRecentPages.length > 0" class="history-items">
              <div
                v-for="(page, index) in filteredRecentPages"
                :key="page.id"
                class="history-item page-item"
                :class="{ 'history-hidden': !globalSearchStore.searchQuery.trim() && index >= 5 }"
                @click="navigateToPage(page)"
              >
                <i :class="getPageIcon(page)"></i>
                <div class="page-content">
                  <span class="page-title">{{ getPageTitle(page) }}</span>
                  <div class="page-subtitle-row">
                    <span class="page-path">{{ getPageSubtitle(page) }}</span>
                    <BravoBadge
                      v-if="getPageStatus(page)"
                      :value="getPageStatus(page)"
                      :severity="getStatusSeverity(page)"
                      size="small"
                      class="status-chip"
                    />
                  </div>
                </div>
                <span class="history-time">{{ formatTime(page.timestamp) }}</span>
              </div>
            </div>
            <div v-else class="empty-state">
              <span class="empty-text">{{ globalSearchStore.searchQuery.trim() ? 'No matching pages' : 'No recent pages' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="globalSearchStore.error" class="error-section">
        <BravoMessage severity="error" :closable="false">
          {{ globalSearchStore.error }}
        </BravoMessage>
      </div>

      <!-- Loading State -->
      <div v-if="globalSearchStore.isSearching" class="loading-section">
        <div class="loading-content">
          <BravoProgressSpinner size="small" />
          <span>Searching...</span>
        </div>
      </div>

      <!-- Search Results -->
      <div v-if="globalSearchStore.hasResults && !globalSearchStore.isSearching" class="results-section">
        <div class="results-header">
          <h4>Search Results</h4>
          <span class="results-count">{{ globalSearchStore.getTotalResultsCount() }} total results</span>
        </div>

        <!-- Result Tabs -->
        <div class="result-tabs">
          <BravoButton
            :label="`All (${globalSearchStore.getTotalResultsCount()})`"
            :severity="activeTab === 'all' ? 'primary' : 'secondary'"
            :outlined="activeTab !== 'all'"
            size="small"
            @click="activeTab = 'all'"
            class="tab-button"
          />
          <BravoButton
            v-for="scopeType in allScopeTypes"
            :key="scopeType"
            :label="`${globalSearchStore.getDisplayLabel(scopeType)} (${getResultCountForType(scopeType)})`"
            :severity="activeTab === scopeType ? 'primary' : 'secondary'"
            :outlined="activeTab !== scopeType"
            size="small"
            @click="activeTab = scopeType"
            class="tab-button"
          />
        </div>

        <!-- All Results Tab Content -->
        <div v-if="activeTab === 'all'" class="results-content">
          <div
            v-for="result in globalSearchStore.searchResults"
            :key="result.type"
            class="result-group"
          >
            <div class="result-group-header">
              <i :class="globalSearchStore.getDisplayIcon(result.type)"></i>
              <h5>{{ globalSearchStore.getDisplayLabel(result.type) }}</h5>
              <BravoBadge :value="result.totalCount.toString()" />
            </div>

            <div class="result-items">
              <div
                v-for="(item, index) in result.items.slice(0, 3)"
                :key="index"
                class="result-item"
                @click="handleResultClick(result.type, item)"
              >
                <div class="result-item-content">
                  <div class="result-item-title">
                    {{ getItemTitle(result.type, item) }}
                  </div>
                  <div class="result-item-subtitle">
                    {{ getItemSubtitle(result.type, item) }}
                  </div>
                </div>
                <i class="pi pi-chevron-right result-item-arrow"></i>
              </div>

              <!-- Show More Button -->
              <div v-if="result.totalCount > 3" class="show-more">
                <BravoButton
                  :label="`View all ${result.totalCount} ${globalSearchStore.getDisplayLabel(result.type).toLowerCase()}`"
                  text
                  size="small"
                  @click="handleShowMore(result.type)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Individual Result Type Tab Content -->
        <div
          v-for="scopeType in allScopeTypes"
          :key="`tab-${scopeType}`"
          v-show="activeTab === scopeType"
          class="results-content single-type"
        >
          <div class="result-type-header">
            <i :class="globalSearchStore.getDisplayIcon(scopeType)"></i>
            <h5>{{ globalSearchStore.getDisplayLabel(scopeType) }}</h5>
            <span class="result-count">{{ getResultCountForType(scopeType) }} results</span>
          </div>

          <!-- Show results if they exist -->
          <div v-if="getResultsForType(scopeType)" class="result-items">
            <div
              v-for="(item, index) in getResultsForType(scopeType)!.items"
              :key="index"
              class="result-item"
              @click="handleResultClick(scopeType, item)"
            >
              <div class="result-item-content">
                <div class="result-item-title">
                  {{ getItemTitle(scopeType, item) }}
                </div>
                <div class="result-item-subtitle">
                  {{ getItemSubtitle(scopeType, item) }}
                </div>
                <div v-if="getItemExtraInfo(scopeType, item)" class="result-item-extra">
                  {{ getItemExtraInfo(scopeType, item) }}
                </div>
              </div>
              <i class="pi pi-chevron-right result-item-arrow"></i>
            </div>

            <!-- Show More Button for paginated results -->
            <div v-if="getResultsForType(scopeType)!.totalCount > getResultsForType(scopeType)!.items.length" class="show-more">
              <BravoButton
                :label="`Load ${Math.min(getResultsForType(scopeType)!.totalCount - getResultsForType(scopeType)!.items.length, 10)} more results`"
                text
                size="small"
                @click="handleLoadMore(scopeType)"
              />
            </div>
          </div>

          <!-- Show empty state if no results -->
          <div v-else class="no-results-for-type">
            <div class="no-results-content">
              <i :class="globalSearchStore.getDisplayIcon(scopeType)" class="no-results-icon"></i>
              <h4>No {{ globalSearchStore.getDisplayLabel(scopeType).toLowerCase() }} found</h4>
              <p>Try adjusting your search terms or search in other categories.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results State -->
      <div v-if="!globalSearchStore.hasResults && !globalSearchStore.isSearching && globalSearchStore.searchQuery.trim()" class="no-results-section">
        <div class="no-results-content">
          <i class="pi pi-search no-results-icon"></i>
          <h4>No results found</h4>
          <p>Try adjusting your search terms or check for typos.</p>
        </div>
      </div>

      <!-- Empty State - Hidden for now since we have actions and other content -->
      <!-- 
      <div v-if="!globalSearchStore.searchQuery.trim() && !globalSearchStore.hasHistory" class="empty-state-section">
        <div class="empty-state-content">
          <i class="pi pi-search empty-state-icon"></i>
          <h4>Search Everything</h4>
          <p>Search across knowledge base articles, cases, customer locations, and users all in one place.</p>
        </div>
      </div>
      -->
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Close"
          severity="secondary"
          @click="globalSearchStore.closeModal"
        />
      </div>
    </template>
  </BravoDialog>

  <!-- Create Case Modal -->
  <CreateCaseModal
    v-model:visible="showCreateCaseModal"
    @case-created="handleCaseCreated"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useGlobalSearchStore, type SearchHistoryItem } from '@/stores/globalSearch'
import { usePageHistoryStore, PageType, type PageHistoryItem } from '@/stores/pageHistory'
import type { SearchScope } from '@/composables/services/useSearchAPI'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoMessage from '@services/ui-component-library/components/BravoMessage.vue'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'
import CreateCaseModal from '@/modules/inbox/components/CreateCaseModal.vue'

const globalSearchStore = useGlobalSearchStore()
const pageHistoryStore = usePageHistoryStore()
const router = useRouter()
const route = useRoute()

// Template refs
const searchInputRef = ref<any>(null)

// Debounce timer for search input
const searchDebounceTimer = ref<NodeJS.Timeout | null>(null)

// Active tab state - defaults to 'all', but doesn't change on subsequent searches
const activeTab = ref<'all' | SearchScope>('all')
const hasSearchedBefore = ref(false)

// Modal states
const showCreateCaseModal = ref(false)

// Keyboard navigation state
const focusedIndex = ref(-1) // -1 means search input is focused
const navigableItems = ref<HTMLElement[]>([])

// All possible search scope types to always show tabs for (ordered as requested)
const allScopeTypes: SearchScope[] = ['issues', 'members_locations', 'members_users', 'kb']

// Recent pages from page history store
const recentPages = computed(() => {
  // Access the reactive pageHistory array directly to ensure reactivity
  const allPages = pageHistoryStore.pageHistory
  const recentPages = allPages.slice(0, 10) // Keep all pages including replaced ones
  return recentPages
})

// Watch for search results to reset tab to 'all' only on first search
watch(() => globalSearchStore.hasResults, (hasResults) => {
  if (hasResults && !hasSearchedBefore.value) {
    activeTab.value = 'all'
    hasSearchedBefore.value = true
  }
})

// Watch for page history changes
watch(() => pageHistoryStore.pageHistory.length, () => {
  // Page history updated
})

// Watch for modal visibility changes to reset focus
watch(() => globalSearchStore.isModalVisible, (isVisible) => {
  if (isVisible) {
    focusedIndex.value = -1
    // Update navigable items after modal opens and DOM updates
    nextTick(() => {
      updateNavigableItems()
    })
  } else {
    focusedIndex.value = -1
    navigableItems.value = []
  }
})

// Watch for search results changes to update navigable items
watch(() => globalSearchStore.hasResults, () => {
  nextTick(() => {
    updateNavigableItems()
  })
})

// Watch for search query changes to reset focus and update navigable items
watch(() => globalSearchStore.searchQuery, () => {
  // Reset focus to search input when query changes
  focusedIndex.value = -1
  clearItemFocus()
  nextTick(() => {
    updateNavigableItems()
  })
})

// Auto-focus watcher removed - using simple autofocus attribute instead

// Page history tracking is now handled globally by the page history tracker

// Filtered search history based on current input
const filteredSearchHistory = computed(() => {
  const query = globalSearchStore.searchQuery.trim().toLowerCase()
  if (!query) return globalSearchStore.recentSearches
  
  return globalSearchStore.recentSearches.filter(item => 
    item.query.toLowerCase().includes(query)
  )
})

// Filtered recent pages based on current input
const filteredRecentPages = computed(() => {
  const query = globalSearchStore.searchQuery.trim().toLowerCase()
  if (!query) return recentPages.value
  
  return recentPages.value.filter(page => {
    const title = getPageTitle(page).toLowerCase()
    const subtitle = getPageSubtitle(page).toLowerCase()
    const path = page.path.toLowerCase()
    
    return title.includes(query) || subtitle.includes(query) || path.includes(query)
  })
})

// Define available actions
interface QuickAction {
  id: string
  title: string
  subtitle: string
  icon: string
  keywords: string[]
  action: () => void
  type: 'navigation' | 'create'
}

// Get current page context
const getCurrentPageContext = () => {
  const fullUrl = `${window.location.origin}${route.fullPath}`
  const pageName = getPageNameFromPath(route.path)
  return { fullUrl, pageName }
}

// Helper to get a friendly page name from path
const getPageNameFromPath = (path: string): string => {
  if (path.startsWith('/inbox/cases/')) return 'Current Case'
  if (path.startsWith('/inbox/tasks/')) return 'Current Task'
  if (path.startsWith('/inbox')) return 'Inbox'
  if (path.startsWith('/knowledge/articles/')) return 'Knowledge Article'
  if (path.startsWith('/knowledge')) return 'Knowledge Base'
  if (path.startsWith('/customers/locations/')) return 'Customer Location'
  if (path.startsWith('/customers/users/')) return 'Customer User'
  if (path.startsWith('/customers')) return 'Customers'
  if (path.startsWith('/analytics')) return 'Analytics'
  if (path.startsWith('/settings')) return 'Settings'
  if (path.startsWith('/aistudio')) return 'AI Studio'
  if (path.startsWith('/journeys')) return 'Journeys'
  return 'Current Page'
}

const quickActions: QuickAction[] = [
  // Current page action (always first)
  {
    id: 'copy-current-url',
    title: 'Copy Current Page URL',
    subtitle: '',
    icon: 'pi pi-copy',
    keywords: ['copy', 'url', 'link', 'current', 'page', 'share'],
    type: 'create',
    action: () => copyCurrentPageUrl()
  },
  // Navigation actions
  {
    id: 'nav-inbox',
    title: 'Go to Inbox',
    subtitle: '',
    icon: 'pi pi-arrow-right',
    keywords: ['inbox', 'cases', 'tasks', 'tickets'],
    type: 'navigation',
    action: () => navigateAndClose('/inbox')
  },
  {
    id: 'nav-knowledge',
    title: 'Go to Knowledge',
    subtitle: '',
    icon: 'pi pi-arrow-right',
    keywords: ['knowledge', 'articles', 'kb', 'docs', 'documentation'],
    type: 'navigation',
    action: () => navigateAndClose('/knowledge')
  },
  {
    id: 'create-case',
    title: 'Create New Case',
    subtitle: '',
    icon: 'pi pi-plus-circle',
    keywords: ['create', 'new', 'case', 'ticket', 'issue', 'support'],
    type: 'create',
    action: () => createCase()
  },
  {
    id: 'create-customer',
    title: 'Create New Customer',
    subtitle: '',
    icon: 'pi pi-user-plus',
    keywords: ['create', 'new', 'customer', 'client', 'contact', 'add'],
    type: 'create',
    action: () => createCustomer()
  },
  // Additional actions (hidden by default)
  {
    id: 'nav-customers',
    title: 'Go to Customers',
    subtitle: '',
    icon: 'pi pi-arrow-right',
    keywords: ['customers', 'clients', 'contacts', 'users'],
    type: 'navigation',
    action: () => navigateAndClose('/customers')
  },
  {
    id: 'nav-analytics',
    title: 'Go to Analytics',
    subtitle: '',
    icon: 'pi pi-arrow-right',
    keywords: ['analytics', 'reports', 'metrics', 'dashboard', 'stats'],
    type: 'navigation',
    action: () => navigateAndClose('/analytics')
  },
  {
    id: 'nav-settings',
    title: 'Go to Settings',
    subtitle: '',
    icon: 'pi pi-arrow-right',
    keywords: ['settings', 'configuration', 'preferences', 'admin'],
    type: 'navigation',
    action: () => navigateAndClose('/settings')
  },
  {
    id: 'nav-aistudio',
    title: 'Go to AI Studio',
    subtitle: '',
    icon: 'pi pi-arrow-right',
    keywords: ['ai', 'studio', 'artificial', 'intelligence', 'assistant'],
    type: 'navigation',
    action: () => navigateAndClose('/aistudio')
  },
  {
    id: 'nav-journeys',
    title: 'Go to Journeys',
    subtitle: '',
    icon: 'pi pi-arrow-right',
    keywords: ['journeys', 'workflows', 'automation'],
    type: 'navigation',
    action: () => navigateAndClose('/journeys')
  },
  {
    id: 'create-article',
    title: 'Create New Article',
    subtitle: '',
    icon: 'pi pi-file-plus',
    keywords: ['create', 'new', 'article', 'knowledge', 'write', 'document'],
    type: 'create',
    action: () => createArticle()
  }
]

// Filtered actions based on current input with dynamic subtitles
const filteredActions = computed(() => {
  const query = globalSearchStore.searchQuery.trim().toLowerCase()
  
  // Update the copy URL action with current page context
  const actionsWithContext = quickActions.map(action => {
    if (action.id === 'copy-current-url') {
      const { pageName } = getCurrentPageContext()
      return {
        ...action,
        subtitle: pageName
      }
    }
    return action
  })
  
  if (!query) return actionsWithContext
  
  return actionsWithContext.filter(action => {
    const titleMatch = action.title.toLowerCase().includes(query)
    const subtitleMatch = action.subtitle.toLowerCase().includes(query)
    const keywordMatch = action.keywords.some(keyword => keyword.includes(query))
    
    return titleMatch || subtitleMatch || keywordMatch
  })
})

// Handle search input changes (live filtering only)
function handleInputChange() {
  // Clear any existing debounce timer - we don't auto-search anymore
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
  
  // If search query is cleared, clear the results
  if (!globalSearchStore.searchQuery.trim()) {
    globalSearchStore.clearSearch()
  }
  
  // Note: Focus reset is now handled by the search query watcher
  // Note: We don't trigger search automatically anymore
  // Live filtering happens through computed properties
}

// Handle immediate search (Enter key or search button)
function handleSearch() {
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
  
  // Only perform search if there's a query
  if (globalSearchStore.searchQuery.trim()) {
    globalSearchStore.performSearch()
  }
}

// Handle clearing the search input
function handleClearSearch() {
  globalSearchStore.clearSearch()
}

// Handle history item selection
function selectHistoryItem(historyItem: SearchHistoryItem) {
  globalSearchStore.selectHistoryItem(historyItem)
}

// Handle result item click
function handleResultClick(type: SearchScope, item: any) {
  // Navigate to the appropriate page based on the result type
  switch (type) {
    case 'kb':
      if (item.id) {
        router.push(`/knowledge/articles/${item.id}`)
        globalSearchStore.closeModal()
      }
      break
    case 'issues':
      if (item.id) {
        router.push(`/inbox/cases/${item.id}`)
        globalSearchStore.closeModal()
      }
      break
    case 'members_locations':
      if (item.id) {
        router.push(`/customers/locations/${item.id}`)
        globalSearchStore.closeModal()
      }
      break
    case 'members_users':
      if (item.id) {
        router.push(`/customers/users/${item.id}`)
        globalSearchStore.closeModal()
      }
      break
    // members_devices_dict removed - no longer needed
    default:
      console.warn('Unknown result type:', type)
  }
}

// Handle show more button click
function handleShowMore(type: SearchScope) {
  // Navigate to a filtered view of the specific type
  switch (type) {
    case 'kb':
      router.push({
        path: '/knowledge',
        query: { search: globalSearchStore.searchQuery }
      })
      break
    case 'issues':
      router.push({
        path: '/inbox',
        query: { search: globalSearchStore.searchQuery }
      })
      break
    case 'members_locations':
      router.push({
        path: '/customers',
        query: { search: globalSearchStore.searchQuery, type: 'locations' }
      })
      break
    case 'members_users':
      router.push({
        path: '/customers',
        query: { search: globalSearchStore.searchQuery, type: 'users' }
      })
      break
    // members_devices_dict removed - no longer needed
  }
  globalSearchStore.closeModal()
}

// Utility function to strip HTML tags from search results
function stripHtmlTags(text: any): string {
  if (!text) return ''
  if (typeof text !== 'string') return String(text)
  return text.replace(/<[^>]*>/g, '')
}

// Get display title for result items
function getItemTitle(type: SearchScope, item: any): string {
  let title = ''
  switch (type) {
    case 'kb':
      title = item.title || item.lbl || item.id
      break
    case 'issues':
      title = item.display_name || item.reference_num || `Case #${item.id}`
      break
    case 'members_locations':
      title = item.site_name || item.c__lbl || 'Location'
      break
    case 'members_users':
      title = item.full_name || item.name || item.email || 'User'
      break
    default:
      title = 'Unknown'
  }
  return stripHtmlTags(title)
}

// Get display subtitle for result items
function getItemSubtitle(type: SearchScope, item: any): string {
  let subtitle = ''
  switch (type) {
    case 'kb':
      subtitle = item.sub_title || item.body?.substring(0, 100) || ''
      break
    case 'issues':
      subtitle = item.c__name || item.status || ''
      break
    case 'members_locations':
      subtitle = item.c__name || `${item.city || ''} ${item.state_id || ''}`.trim() || ''
      break
    case 'members_users':
      subtitle = item.email || item.phone || ''
      break
    default:
      subtitle = ''
  }
  return stripHtmlTags(subtitle)
}

// Get additional info for result items (used in individual tab view)
function getItemExtraInfo(type: SearchScope, item: any): string {
  let extraInfo = ''
  switch (type) {
    case 'kb':
      extraInfo = item.category || item.tags?.join(', ') || ''
      break
    case 'issues':
      extraInfo = item.created_at ? `Created: ${new Date(item.created_at).toLocaleDateString()}` : ''
      break
    case 'members_locations':
      extraInfo = item.phone || item.contact || ''
      break
    case 'members_users':
      extraInfo = item.role || item.department || ''
      break
    default:
      extraInfo = ''
  }
  return stripHtmlTags(extraInfo)
}

// Handle load more results for individual tabs
function handleLoadMore(type: SearchScope) {
  // This would typically load more results from the API
  // For now, we'll just navigate to the full results page
  handleShowMore(type)
}

// Get result count for a specific type (returns 0 if no results)
function getResultCountForType(type: SearchScope): number {
  const result = globalSearchStore.getResultsByType(type)
  return result ? result.totalCount : 0
}

// Get results for a specific type (returns null if no results)
function getResultsForType(type: SearchScope) {
  return globalSearchStore.getResultsByType(type)
}

// Recent pages management
function clearRecentPages() {
  pageHistoryStore.clearHistory()
}

// Action handlers
function navigateAndClose(path: string) {
  router.push(path)
  globalSearchStore.closeModal()
}

function copyCurrentPageUrl() {
  const { fullUrl, pageName } = getCurrentPageContext()
  navigator.clipboard.writeText(fullUrl).then(() => {
    // Show success feedback - you could use a toast here if available
    console.log(`Copied ${pageName} URL to clipboard:`, fullUrl)
    // Close the search modal
    globalSearchStore.closeModal()
  }).catch(err => {
    console.error('Failed to copy URL to clipboard:', err)
    // Still close the modal even if copy failed
    globalSearchStore.closeModal()
  })
}

function createCase() {
  // Close global search modal and open create case modal
  globalSearchStore.closeModal()
  showCreateCaseModal.value = true
}

// Handle case creation completion
function handleCaseCreated(newCase: any) {
  // Close the create case modal
  showCreateCaseModal.value = false
  // Navigate to the new case if it has an ID
  if (newCase?.id) {
    router.push(`/inbox/cases/${newCase.id}`)
  }
}

function createArticle() {
  // Navigate to knowledge base and trigger article creation
  router.push('/knowledge')
  globalSearchStore.closeModal()
  // TODO: Trigger article creation modal after navigation
  console.log('TODO: Open article creation modal')
}

function createCustomer() {
  // Navigate to customers and trigger customer creation
  router.push('/customers')
  globalSearchStore.closeModal()
  // TODO: Trigger customer creation modal after navigation
  console.log('TODO: Open customer creation modal')
}

function navigateToPage(page: PageHistoryItem) {
  console.log('🔍 navigateToPage called with page:', page)
  const route: any = { path: page.path }
  if (page.query && Object.keys(page.query).length > 0) {
    route.query = page.query
  }
  console.log('🔍 Navigating to route:', route)
  router.push(route)
  globalSearchStore.closeModal()
}

function getPageIcon(page: PageHistoryItem): string {
  // Use string comparison to handle localStorage data
  switch (page.pageType) {
    case 'case':
    case PageType.CASE:
      return 'pi pi-briefcase'
    case 'task':
    case PageType.TASK:
      return 'pi pi-check-square'
    case 'kb_article':
    case PageType.KB_ARTICLE:
      return 'pi pi-book'
    case 'customer':
    case PageType.CUSTOMER:
      return 'pi pi-user'
    case 'list_view':
    case PageType.LIST_VIEW:
      if (page.path.startsWith('/inbox')) return 'pi pi-inbox'
      if (page.path.startsWith('/knowledge')) return 'pi pi-book'
      if (page.path.startsWith('/customers')) return 'pi pi-users'
      if (page.path.startsWith('/settings')) return 'pi pi-cog'
      return 'pi pi-list'
    case 'general':
    case PageType.GENERAL:
    default:
      if (page.path.startsWith('/journeys')) return 'pi pi-map-marker'
      if (page.path.startsWith('/analytics')) return 'pi pi-chart-bar'
      if (page.path.startsWith('/aistudio')) return 'pi pi-sparkles'
      if (page.path.startsWith('/knowledge')) return 'pi pi-book'
      if (page.path.startsWith('/customers')) return 'pi pi-users'
      if (page.path.startsWith('/inbox')) return 'pi pi-inbox'
      return 'pi pi-file'
  }
}

function getPageTitle(page: PageHistoryItem): string {
  console.log('🔍 getPageTitle called with page:', page)
  // Use string comparison to handle localStorage data
  switch (page.pageType) {
    case 'case':
    case PageType.CASE:
      const caseData = (page as any).caseData
      console.log('🔍 Case data in getPageTitle:', caseData)
      const caseName = caseData?.name || 'Unknown Case'
      const caseRef = caseData?.refId || 'Unknown'
      const caseTitle = `${caseName} (${caseRef})`
      console.log('🔍 Generated case title:', caseTitle)
      return caseTitle
    case 'task':
    case PageType.TASK:
      return (page as any).taskData?.name
    case 'kb_article':
    case PageType.KB_ARTICLE:
      return (page as any).articleData?.title
    case 'customer':
    case PageType.CUSTOMER:
      return (page as any).customerData?.name
    case 'list_view':
    case PageType.LIST_VIEW:
      return getListViewTitle(page as any)
    case 'general':
    case PageType.GENERAL:
    default:
      console.log('🔍 General page data:', (page as any).generalData)
      const title = (page as any).generalData?.title || getGeneralPageTitle(page.path)
      console.log('🔍 Computed title:', title)
      return title
  }
}

function getPageSubtitle(page: PageHistoryItem): string {
  console.log('🔍 getPageSubtitle called with page:', page)
  // Use string comparison to handle localStorage data
  switch (page.pageType) {
    case 'case':
    case PageType.CASE:
      const caseData = (page as any).caseData
      console.log('🔍 Case data in getPageSubtitle:', caseData)
      // For cases, show location (don't show status here since it's now a chip)
      const location = caseData?.location || 'No location'
      console.log('🔍 Generated case subtitle (location):', location)
      return location
    case 'task':
    case PageType.TASK:
      // For tasks, show task info (status will be in chip)
      return (page as any).taskData?.name || 'Task'
    case 'kb_article':
    case PageType.KB_ARTICLE:
      const articleData = (page as any).articleData
      // For articles, show subtitle if available (status will be in chip)
      return articleData?.subtitle || 'Knowledge Article'
    case 'customer':
    case PageType.CUSTOMER:
      // For customers, show customer info (status will be in chip)
      return (page as any).customerData?.name || 'Customer'
    case 'list_view':
    case PageType.LIST_VIEW:
      return getListViewSubtitle(page as any)
    case 'general':
    case PageType.GENERAL:
    default:
      // Use section if available, otherwise show clean path
      const generalData = (page as any).generalData
      const section = generalData?.section
      if (section && section !== generalData?.title) {
        return section
      }
      // Remove leading slash and show clean path
      const cleanPath = page.path.startsWith('/') ? page.path.slice(1) : page.path
      return cleanPath.replace(/\//g, ' › ')
  }
}

function getListViewTitle(page: PageHistoryItem & { pageType: PageType.LIST_VIEW }): string {
  const baseTitle = page.listData.viewType ? 
    page.listData.viewType.charAt(0).toUpperCase() + page.listData.viewType.slice(1) : 
    'List View'
  
  if (page.listData.query) {
    return `${baseTitle} - "${page.listData.query}"`
  }
  
  if (page.listData.totalResults !== undefined) {
    return `${baseTitle} (${page.listData.totalResults} results)`
  }
  
  return baseTitle
}

function getListViewSubtitle(page: PageHistoryItem & { pageType: PageType.LIST_VIEW }): string {
  const parts: string[] = []
  
  if (page.listData.page && page.listData.page > 1) {
    parts.push(`Page ${page.listData.page}`)
  }
  
  if (page.listData.filters && Object.keys(page.listData.filters).length > 0) {
    const filterCount = Object.keys(page.listData.filters).length
    parts.push(`${filterCount} filter${filterCount > 1 ? 's' : ''}`)
  }
  
  return parts.join(' • ') || 'All items'
}

function getGeneralPageTitle(path: string): string {
  if (path.startsWith('/journeys')) return 'Journeys'
  if (path.startsWith('/analytics')) return 'Analytics'
  if (path.startsWith('/settings')) return 'Settings'
  if (path.startsWith('/aistudio')) return 'AI Studio'
  if (path.startsWith('/knowledge')) return 'Knowledge'
  if (path.startsWith('/customers')) return 'Customers'
  if (path.startsWith('/inbox')) return 'Inbox'
  return 'Page'
}

function getPageStatus(page: PageHistoryItem): string | null {
  // Use string comparison to handle localStorage data
  switch (page.pageType) {
    case 'case':
    case PageType.CASE:
      return (page as any).caseData?.status || null
    case 'task':
    case PageType.TASK:
      return (page as any).taskData?.status || null
    case 'kb_article':
    case PageType.KB_ARTICLE:
      return (page as any).articleData?.status || null
    case 'customer':
    case PageType.CUSTOMER:
      return (page as any).customerData?.status || null
    case 'list_view':
    case PageType.LIST_VIEW:
    case 'general':
    case PageType.GENERAL:
    default:
      return null
  }
}

function getStatusSeverity(page: PageHistoryItem): 'success' | 'info' | 'warn' | 'danger' | 'secondary' {
  const status = getPageStatus(page)
  if (!status) return 'secondary'
  
  const statusLower = status.toLowerCase()
  
  // Define status color mappings
  if (statusLower.includes('complete') || statusLower.includes('resolved') || statusLower.includes('closed')) {
    return 'success'
  }
  if (statusLower.includes('progress') || statusLower.includes('active') || statusLower.includes('open')) {
    return 'info'
  }
  if (statusLower.includes('pending') || statusLower.includes('waiting') || statusLower.includes('review')) {
    return 'warn'
  }
  if (statusLower.includes('error') || statusLower.includes('failed') || statusLower.includes('urgent')) {
    return 'danger'
  }
  
  return 'secondary'
}

// Page history persistence is now handled by the page history store

// Format timestamp for history items
function formatTime(timestamp: Date): string {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  if (hours < 24) return `${hours}h ago`
  if (days < 7) return `${days}d ago`
  return timestamp.toLocaleDateString()
}

// Update navigable items list
function updateNavigableItems() {
  navigableItems.value = []
  
  if (!globalSearchStore.isModalVisible) return
  
  // Get all clickable items in the modal
  const modal = document.querySelector('.global-search-modal')
  if (!modal) return
  
  // Add all navigable items in the order they appear in the DOM
  // This ensures proper tab order across all sections
  
  // 1. Action items (visible ones only)
  const actionItems = modal.querySelectorAll('.action-item:not(.action-hidden)')
  console.log('🔍 Found action items:', actionItems.length)
  actionItems.forEach(item => navigableItems.value.push(item as HTMLElement))
  
  // 2. Search history items (visible ones only)
  const searchHistoryItems = modal.querySelectorAll('.search-history .history-item:not(.history-hidden)')
  searchHistoryItems.forEach(item => navigableItems.value.push(item as HTMLElement))
  
  // 3. Recent page items (visible ones only)
  const recentPageItems = modal.querySelectorAll('.recent-pages .history-item:not(.history-hidden)')
  recentPageItems.forEach(item => navigableItems.value.push(item as HTMLElement))
  
  // 4. Result items if they exist
  const resultItems = modal.querySelectorAll('.result-item')
  console.log('🔍 Found result items:', resultItems.length)
  resultItems.forEach(item => navigableItems.value.push(item as HTMLElement))
  
  // 5. Show more buttons if they exist
  const showMoreButtons = modal.querySelectorAll('.show-more .p-button')
  showMoreButtons.forEach(item => navigableItems.value.push(item as HTMLElement))
  

}

// Handle keyboard navigation within the modal
function handleModalKeydown(event: KeyboardEvent) {
  if (!globalSearchStore.isModalVisible) return
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      updateNavigableItems()
      if (navigableItems.value.length > 0) {
        // If we're at the search input (focusedIndex === -1), move to first item
        if (focusedIndex.value === -1) {
          focusedIndex.value = 0
        } else {
          // Otherwise, move to next item (don't go past the last item)
          focusedIndex.value = Math.min(focusedIndex.value + 1, navigableItems.value.length - 1)
        }
        focusItem(focusedIndex.value)
      }
      break
      
    case 'ArrowUp':
      event.preventDefault()
      updateNavigableItems()
      
      if (focusedIndex.value === -1) {
        // Already at search input, do nothing
        return
      } else if (focusedIndex.value === 0) {
        // At first item, go back to search input
        focusedIndex.value = -1
        clearItemFocus()
        
        // Use nextTick to ensure DOM updates are complete before focusing
        nextTick(() => {
          focusSearchInput()
        })
      } else {
        // Move to previous item
        focusedIndex.value = Math.max(focusedIndex.value - 1, 0)
        focusItem(focusedIndex.value)
      }
      break
      
    case 'Enter':
      if (focusedIndex.value >= 0 && navigableItems.value[focusedIndex.value]) {
        event.preventDefault()
        navigableItems.value[focusedIndex.value].click()
      }
      break
      
    case 'Escape':
      event.preventDefault()
      globalSearchStore.closeModal()
      break
  }
}

// Clear focus from all items
function clearItemFocus() {
  navigableItems.value.forEach(item => {
    item.classList.remove('keyboard-focused')
    item.setAttribute('tabindex', '-1')
    item.blur() // Also blur the element to remove browser focus
  })
}

// Helper function to focus the search input
function focusSearchInput() {
  if (searchInputRef.value) {
    // Try different approaches to focus the input
    if (typeof searchInputRef.value.focus === 'function') {
      searchInputRef.value.focus()
      return
    }
    
    if (searchInputRef.value.$el) {
      const inputEl = searchInputRef.value.$el.querySelector('input') as HTMLInputElement
      if (inputEl) {
        inputEl.focus()
        return
      }
    }
    
    // Try to access the input element directly if it's a Vue component wrapper
    if (searchInputRef.value.$refs && searchInputRef.value.$refs.input) {
      searchInputRef.value.$refs.input.focus()
      return
    }
    
    // Fallback: try to find any input element in the search wrapper
    const modal = document.querySelector('.global-search-modal')
    const inputEl = modal?.querySelector('.search-input input, .search-input .p-inputtext, input[placeholder*="Search"]') as HTMLInputElement
    if (inputEl) {
      inputEl.focus()
      return
    }
  }
}

// Focus a specific item by index
function focusItem(index: number) {
  // Remove focus from all items first
  clearItemFocus()
  
  // Focus the target item
  if (index >= 0 && index < navigableItems.value.length) {
    const item = navigableItems.value[index]
    item.classList.add('keyboard-focused')
    item.setAttribute('tabindex', '0')
    item.focus()
  }
}

// Keyboard shortcuts
function handleKeyboardShortcuts(event: KeyboardEvent) {
  // Ctrl/Cmd + K to open search
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    globalSearchStore.openModal()
  }
}

// Setup keyboard shortcuts and modal navigation
onMounted(() => {
  document.addEventListener('keydown', handleKeyboardShortcuts)
  document.addEventListener('keydown', handleModalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcuts)
  document.removeEventListener('keydown', handleModalKeydown)
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
})
</script>

<style scoped>
.global-search-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-height: 300px;
}

.search-input-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-input-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  padding: 2px; /* Add padding to prevent focus outline clipping */
}

.search-input {
  flex: 1;
}

.clear-button {
  flex-shrink: 0;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.clear-button:hover {
  opacity: 1;
}

.search-button {
  flex-shrink: 0;
}

.zero-state-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.search-history,
.recent-pages {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-header h4 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--surface-700);
}

.history-items {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: var(--surface-100);
}

.history-item i {
  color: var(--surface-500);
  font-size: 0.875rem;
}

.history-query {
  flex: 1;
  font-size: 0.875rem;
  color: var(--surface-700);
}

.history-time {
  font-size: 0.75rem;
  color: var(--surface-500);
}

.page-item {
  align-items: flex-start;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  min-width: 0;
}

.page-title {
  font-size: 0.875rem;
  color: var(--surface-700);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-subtitle-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
}

.page-path {
  font-size: 0.75rem;
  color: var(--surface-500);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.status-chip {
  flex-shrink: 0;
}

.quick-actions {
  margin-bottom: 1.5rem;
}

.action-item {
  position: relative;
}

.action-item:hover {
  background-color: var(--surface-50);
  border-color: var(--primary-200);
}

.action-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  min-width: 0;
}

.action-title {
  font-size: 0.875rem;
  color: var(--surface-700);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-subtitle {
  font-size: 0.75rem;
  color: var(--surface-500);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-arrow {
  color: var(--surface-400);
  font-size: 0.75rem;
  flex-shrink: 0;
}

.action-hidden {
  display: none;
}

.history-hidden {
  display: none;
}

.error-section {
  margin: -0.5rem 0;
}

.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--surface-600);
  font-size: 0.875rem;
}

.results-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-200);
}

.results-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--surface-900);
}

.results-count {
  font-size: 0.875rem;
  color: var(--surface-600);
}

.result-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.tab-button {
  flex-shrink: 0;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: 400px;
  overflow-y: auto;
}

.results-content.single-type {
  max-height: 450px;
}

.result-type-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--surface-200);
}

.result-type-header h5 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--surface-900);
  flex: 1;
}

.result-type-header .result-count {
  font-size: 0.875rem;
  color: var(--surface-600);
}

.result-type-header i {
  color: var(--primary-color);
  font-size: 1.25rem;
}

.result-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.result-group-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.result-group-header i {
  color: var(--primary-color);
  font-size: 1rem;
}

.result-group-header h5 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--surface-800);
  flex: 1;
}

.result-items {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--surface-200);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.result-item:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-50);
}

.result-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.result-item-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--surface-900);
  line-height: 1.3;
}

.result-item-subtitle {
  font-size: 0.75rem;
  color: var(--surface-600);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-item-extra {
  font-size: 0.75rem;
  color: var(--surface-500);
  line-height: 1.3;
  font-style: italic;
}

.result-item-arrow {
  color: var(--surface-400);
  font-size: 0.75rem;
  flex-shrink: 0;
}

.show-more {
  padding: 0.5rem 0.75rem;
  text-align: center;
}

.no-results-section,
.empty-state-section,
.no-results-for-type {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 1rem;
}

.no-results-content,
.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
  max-width: 300px;
}

.no-results-icon,
.empty-state-icon {
  font-size: 3rem;
  color: var(--surface-400);
}

.no-results-content h4,
.empty-state-content h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--surface-700);
}

.no-results-content p,
.empty-state-content p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--surface-600);
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Empty state styles for history sections */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 1rem;
}

.empty-text {
  font-size: 0.875rem;
  color: var(--surface-500);
  font-style: italic;
}



/* Keyboard navigation focus styles */
.keyboard-focused {
  background-color: var(--primary-50) !important;
  border-color: var(--primary-200) !important;
  outline: 2px solid var(--primary-200);
  outline-offset: -2px;
}

.keyboard-focused .action-title,
.keyboard-focused .history-query,
.keyboard-focused .page-title,
.keyboard-focused .result-item-title {
  color: var(--primary-700) !important;
}

/* Custom scrollbar for results */
.results-content::-webkit-scrollbar {
  width: 6px;
}

.results-content::-webkit-scrollbar-track {
  background: var(--surface-100);
  border-radius: 3px;
}

.results-content::-webkit-scrollbar-thumb {
  background: var(--surface-300);
  border-radius: 3px;
}

.results-content::-webkit-scrollbar-thumb:hover {
  background: var(--surface-400);
}

/* Modal-specific styles */
:deep(.global-search-modal .p-dialog-header) {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
}

:deep(.global-search-modal .p-dialog-content) {
  padding: 0.25rem 1.5rem 1rem 1.5rem; /* Add top padding to prevent focus clipping */
}

:deep(.global-search-modal .p-dialog-footer) {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
}
</style> 