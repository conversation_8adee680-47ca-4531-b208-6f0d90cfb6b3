<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import Bravo<PERSON>conField from '@services/ui-component-library/components/BravoIconField.vue';
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue';
import Checkbox from 'primevue/checkbox';
// Utility functions moved from utils.ts
const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

const isImageFile = (filename: string): boolean => {
  const ext = getFileExtension(filename).toLowerCase();
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext);
};

const isVideoFile = (filename: string): boolean => {
  const ext = getFileExtension(filename).toLowerCase();
  return ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv'].includes(ext);
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileIconClass = (filename: string): string => {
  const ext = getFileExtension(filename).toLowerCase();

  // Image files
  if (isImageFile(filename)) {
    return 'pi-image';
  }

  // Video files
  if (isVideoFile(filename)) {
    return 'pi-video';
  }

  // Excel files
  if (['xls', 'xlsx', 'csv'].includes(ext)) {
    return 'pi-file-excel';
  }

  // PDF files
  if (ext === 'pdf') {
    return 'pi-file-pdf';
  }

  // Default file icon for other document types
  return 'pi-file';
};

const getThumbnailUrl = (file: any): string => {
  // Check for thumbnail property (from API)
  if (file.thumbnail) return file.thumbnail;

  // For image files, use the file URL directly
  if (isImageFile(file.name) && file.url) return file.url;

  // Return placeholder based on file type
  if (isImageFile(file.name)) {
    return '/placeholders/image.png';
  } else if (isVideoFile(file.name)) {
    return '/placeholders/video.png';
  } else {
    return '/placeholders/file.png';
  }
};

import FileUpload from 'primevue/fileupload';

// Define file item interface
interface FileItem {
  id: string | number;
  name: string;
  type?: string;
  url?: string;
  size: number;
  dateUploaded?: Date;
  [key: string]: any; // Allow additional properties
}

// Define props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedTab: {
    type: String,
    default: 'images' // 'images', 'videos', 'files'
  },
  pageSize: {
    type: Number,
    default: 12
  },
  totalItems: {
    type: Number,
    default: 0
  },
  currentPageProp: {
    type: Number,
    default: 1
  },
  items: {
    type: Array as () => FileItem[],
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

// Define emits
const emit = defineEmits(['update:visible', 'fileSelected', 'fileUploadSelected', 'upload', 'pageChange', 'tabChanged', 'fileDelete']);

// State
const searchQuery = ref('');
const activeTabIndex = ref(0);
const currentPage = ref(props.currentPageProp);
const totalPages = computed(() => Math.ceil(props.totalItems / props.pageSize) || 1);

// Delete confirmation dialog state
const deleteDialogVisible = ref(false);
const fileToDelete = ref<any>(null);
const removeFromArticles = ref(false);



const tabMap = ['images', 'videos', 'files'];

function onTabClick(index: number) {
  activeTabIndex.value = index;
  emit('tabChanged', tabMap[index]);
}

function onFileUpload(event: any) {
  const file = event.files?.[0];
  if (file) {
    emit('fileUploadSelected', file);
  }
}

// We no longer need to filter files locally since the API will handle this

//Computed properties for filtered files by type
const imageFiles = computed(() => {
  return props.items.filter(file => {
    const isImage = isImageFile(file.name) || (file.type && file.type.startsWith('image/'));
    return isImage;
  });
});

const videoFiles = computed(() => {
  return props.items.filter(file => {
    const isVideo = isVideoFile(file.name) || (file.type && file.type.startsWith('video/'));
    return isVideo;
  });
});

const documentFiles = computed(() => {
  return props.items.filter(file => {
    const isDocument = !isImageFile(file.name) && !isVideoFile(file.name) &&
                      !(file.type && (file.type.startsWith('image/') || file.type.startsWith('video/')));
    return isDocument;
  });
});

// const imageFiles = computed(() => props.items.filter(file => file.type?.startsWith('image/')));
// const videoFiles = computed(() => props.items.filter(file => file.type?.startsWith('video/')));
// const documentFiles = computed(() =>
//   props.items.filter(file => !file.type?.startsWith('image/') && !file.type?.startsWith('video/'))
// );

// Methods
const handleSearch = () => {
  // Reset to page 1 when searching and emit pageChange event
  currentPage.value = 1;
  emit('pageChange', {
    page: 1,
    pageSize: props.pageSize,
    activeTab: tabMap[activeTabIndex.value],
    searchQuery: searchQuery.value
  });
};

const handleSearchKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleSearch();
  }
};

const handleFileSelect = (file: any) => {
  emit('fileSelected', file);
  emit('update:visible', false);
};

const handleFileDelete = (file: any) => {
  // Show confirmation dialog
  fileToDelete.value = file;
  deleteDialogVisible.value = true;
};

const confirmDelete = () => {
  if (fileToDelete.value) {

    // Emit event for parent component to handle the deletion
    emit('fileDelete', {
      file: fileToDelete.value,
      removeFromArticles: removeFromArticles.value
    });

    // Close the dialog
    deleteDialogVisible.value = false;

    // Reset state
    fileToDelete.value = null;
    removeFromArticles.value = false;
  }
};

const cancelDelete = () => {
  // Close the dialog without deleting
  deleteDialogVisible.value = false;

  // Reset state
  fileToDelete.value = null;
  removeFromArticles.value = false;
};

// Upload is now handled by the FileUpload component

const handleClose = () => {
  emit('update:visible', false);
};

const goToPage = (page: number) => {
  currentPage.value = page;

  // Emit pageChange event with pagination data for the parent component to handle
  emit('pageChange', {
    page,
    pageSize: props.pageSize,
    activeTab: tabMap[activeTabIndex.value],
    searchQuery: searchQuery.value
  });
};

// Watch for changes in the currentPageProp
watch(() => props.currentPageProp, (newPage) => {
  currentPage.value = newPage;
});

// Watch for changes in the activeTabIndex
watch(() => activeTabIndex.value, () => {
  // Reset to page 1 when changing tabs and emit pageChange event
  currentPage.value = 1;
  emit('pageChange', {
    page: 1,
    pageSize: props.pageSize,
    activeTab: tabMap[activeTabIndex.value],
    searchQuery: searchQuery.value
  });
});

// Lifecycle hooks
onMounted(() => {

  // Set the active tab based on the selectedTab prop
  switch (props.selectedTab) {
    case 'images':
      activeTabIndex.value = 0;
      break;
    case 'videos':
      activeTabIndex.value = 1;
      break;
    case 'files':
      activeTabIndex.value = 2;
      break;
    default:
      activeTabIndex.value = 0;
  }

  // Emit initial pageChange event to load data
  emit('pageChange', {
    page: currentPage.value,
    pageSize: props.pageSize,
    activeTab: tabMap[activeTabIndex.value],
    searchQuery: searchQuery.value
  });
});

// Watch for changes in props.items
watch(() => props.items, (newItems) => {
  // Items changed - could add logic here if needed
}, { deep: true });
</script>

<template>
  <!-- Delete Confirmation Dialog -->
  <BravoDialog
    v-model:visible="deleteDialogVisible"
    header="Delete?"
    :modal="true"
    :closable="true"
    :style="{ width: '400px' }"
  >
    <div class="delete-confirmation-content">
      <p>Are you sure you want to remove this file from the File Manager?</p>
      <div class="mt-3">
        <div class="flex align-items-center gap-5">
          <Checkbox v-model="removeFromArticles" :binary="true" id="remove-from-articles" />
          <label for="remove-from-articles" class="ml-2">Remove from Articles</label>
        </div>
      </div>
    </div>
    <template #footer>
      <BravoButton label="No" severity="secondary" @click="cancelDelete" />
      <BravoButton label="Yes" severity="danger" @click="confirmDelete" class="ml-2" />
    </template>
  </BravoDialog>

  <!-- Main File Manager Dialog -->
  <BravoDialog
    :visible="props.visible"
    header="File Manager"
    :modal="true"
    :closable="true"
    :style="{ width: '800px', height: '600px' }"
    class="file-manager-dialog"
    :draggable="true"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <template #header>
      <div class="tabs">
        <div
          class="tab-item"
          :class="{ active: activeTabIndex === 0 }"
          @click="onTabClick(0)"
        >
          Images
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTabIndex === 1 }"
          @click="onTabClick(1)"
        >
          Videos
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTabIndex === 2 }"
          @click="onTabClick(2)"
        >
          Files
        </div>
      </div>

      <div class="search-container">
        <BravoIconField>
          <BravoInputIcon class="pi pi-search" />
          <BravoInputText
            v-model="searchQuery"
            @keydown="handleSearchKeydown"
            placeholder="Search for Files"
            :disabled="props.isLoading"
          />
        </BravoIconField>
        <BravoButton
          icon="pi pi-search"
          class="ml-2"
          @click="handleSearch"
          :disabled="props.isLoading"
        />
      </div>
    </template>

    <div class="file-manager">
      <div class="file-manager-content">
        <div v-if="props.isLoading" class="loading-state">
          <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
          <p>Loading files...</p>
        </div>

        <div v-else-if="activeTabIndex === 0 && imageFiles.length === 0" class="empty-state">
          <p>No images found.</p>
        </div>

        <div v-else-if="activeTabIndex === 1 && videoFiles.length === 0" class="empty-state">
          <p>No videos found.</p>
        </div>

        <div v-else-if="activeTabIndex === 2 && documentFiles.length === 0" class="empty-state">
          <p>No files found.</p>
        </div>

        <div v-else class="file-grid">
          <div
            v-for="file in activeTabIndex === 0 ? imageFiles : activeTabIndex === 1 ? videoFiles : documentFiles"
            :key="file.id"
            class="file-item"
            @click="handleFileSelect(file)"
          >
            <div class="file-thumbnail">
              <!-- Show thumbnail for images if available, otherwise show icon -->
              <img
                v-if="file.thumbnail || (isImageFile(file.name) && file.url)"
                :src="getThumbnailUrl(file)"
                :alt="file.name"
                class="file-image"
              />
              <i v-else :class="['pi', getFileIconClass(file.name), 'file-icon']"></i>
            </div>
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-details">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <div class="file-actions">
                <button
                  class="file-action-button"
                  @click.stop="handleFileDelete(file)"
                  v-tooltip.bottom="'Delete'"
                >
                  <i class="pi pi-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="pagination">
          <span>Page {{ currentPage }} of {{ totalPages }} ({{ props.pageSize }} per page)</span>
          <div class="pagination-controls">
            <button
              class="pagination-button"
              :disabled="currentPage === 1"
              @click="goToPage(currentPage - 1)"
            >
              <i class="pi pi-chevron-left"></i>
            </button>
            <button
              class="pagination-button"
              :disabled="currentPage === totalPages"
              @click="goToPage(currentPage + 1)"
            >
              <i class="pi pi-chevron-right"></i>
            </button>
          </div>
        </div>

        <div class="footer-actions">
          <BravoButton
            label="Close"
            severity="secondary"
            @click="handleClose"
          />
          <!-- <BravoButton
            label="Upload"
            icon="pi pi-upload"
            @click="handleUpload"
          /> -->
          <FileUpload
            mode="basic"
            name="demo[]"
            :maxFileSize="104857600"
            @select="onFileUpload"
            :auto="true"
            chooseLabel="Upload"
            chooseIcon="pi pi-upload"
          />
        </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
.file-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.file-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 1rem 0;
  border-bottom: 1px solid var(--surface-200, #e9ecef);
}

.tabs {
  display: flex;
  gap: 0.5rem;
}

.tab-item {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s;
}

.tab-item:hover {
  background-color: var(--surface-100, #f8f9fa);
}

.tab-item.active {
  background-color: var(--primary-color, #3B82F6);
  color: white;
}

.tab-content {
  padding: 0.5rem 0;
}

.tab-count {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6c757d);
}

.search-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.file-manager-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  min-height: 300px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  color: var(--text-color-secondary, #6c757d);
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  padding: 0.5rem;
}

.file-item {
  border: 1px solid var(--surface-200, #e9ecef);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.file-thumbnail {
  height: 100px;
  background-color: var(--surface-100, #f8f9fa);
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  width: auto;
  height: auto;
  border-radius: 2px;
}

.file-icon {
  font-size: 3rem;
}

/* Icon colors for different file types */
.pi-image {
  color: #42A5F5; /* Blue for images */
}

.pi-video {
  color: #EC407A; /* Pink for videos */
}

.pi-file-excel {
  color: #4CAF50; /* Green for Excel files */
}

.pi-file-pdf {
  color: #F44336; /* Red for PDF files */
}

.pi-file {
  color: #7E57C2; /* Purple for other files */
}

.file-info {
  padding: 0.5rem;
}

.file-name {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.25rem;
}

.file-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.file-size {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6c757d);
}

.file-actions {
  display: flex;
  justify-content: flex-end;
}

.file-action-button {
  background: none;
  border: none;
  color: var(--text-color-secondary, #6c757d);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
}

.file-action-button:hover {
  background-color: var(--surface-100, #f8f9fa);
  color: var(--text-color, #212529);
}

.file-manager-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-200, #e9ecef);
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-controls {
  display: flex;
  gap: 0.25rem;
}

.pagination-button {
  background: none;
  border: 1px solid var(--surface-200, #e9ecef);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.footer-actions {
  display: flex;
  gap: 0.5rem;
}
</style>
<style>
.file-manager-dialog .p-dialog-content {
  flex: 1 !important;
  overflow: hidden;
}
</style>