<template>
  <BravoDialog
    v-model:visible="isVisible"
    modal
    :style="{ width: '90vw', maxWidth: '1200px' }"
    :breakpoints="{ '960px': '75vw', '640px': '90vw' }"
    maximizable
    header="XMPP PubSub Event Monitor"
    class="xmpp-pubsub-modal"
  >
    <template #header>
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-3">
          <i class="pi pi-broadcast text-xl"></i>
          <span class="text-lg font-semibold">XMPP PubSub Event Monitor</span>
          <div class="flex items-center gap-2">
            <div 
              :class="[
                'w-3 h-3 rounded-full',
                connectionState.status === 'authenticated' ? 'bg-green-500' : 
                connectionState.status === 'connecting' ? 'bg-yellow-500' :
                connectionState.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
              ]"
            ></div>
            <span class="text-sm capitalize">{{ connectionState.status }}</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600">{{ events.length }} events</span>
        </div>
      </div>
    </template>

    <div class="space-y-4">
      <!-- Connection Status and Controls -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium">Status:</span>
              <BravoBadge 
                :value="connectionState.status" 
                :severity="getStatusSeverity(connectionState.status)"
              />
            </div>
            <div v-if="connectionState.lastConnected" class="text-sm text-gray-600">
              Connected: {{ formatDateTime(connectionState.lastConnected) }}
            </div>
            <div v-if="connectionState.reconnectAttempts > 0" class="text-sm text-orange-600">
              Reconnect attempts: {{ connectionState.reconnectAttempts }}
            </div>
          </div>
          
          <div class="flex items-center gap-2">
            <BravoButton
              @click="clearEvents"
              icon="pi pi-trash"
              size="small"
              severity="secondary"
              outlined
              :disabled="events.length === 0"
            >
              Clear Events
            </BravoButton>
            <BravoButton
              @click="toggleConnection"
              :icon="isConnected ? 'pi pi-stop' : 'pi pi-play'"
              :severity="isConnected ? 'danger' : 'success'"
              size="small"
              :loading="connectionState.status === 'connecting'"
            >
              {{ isConnected ? 'Disconnect' : connectionState.status === 'connecting' ? 'Connecting...' : 'Connect' }}
            </BravoButton>
          </div>
        </div>
        
        <div v-if="connectionState.error" class="text-red-600 text-sm">
          <i class="pi pi-exclamation-triangle mr-2"></i>
          {{ connectionState.error }}
        </div>
      </div>

      <!-- Event Filters -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <span class="text-sm font-medium">Filters:</span>
          <BravoButton
            @click="resetFilters"
            icon="pi pi-filter-slash"
            size="small"
            severity="secondary"
            text
          >
            Reset
          </BravoButton>
        </div>
        
        <div class="flex flex-wrap gap-3">
          <div class="flex items-center gap-2">
            <label class="text-sm">Event Type:</label>
            <Dropdown
              v-model="selectedEventType"
              :options="eventTypeOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="All Types"
              class="w-32"
            />
          </div>
          
          <div class="flex items-center gap-2">
            <label class="text-sm">Node:</label>
            <Dropdown
              v-model="selectedNode"
              :options="nodeOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="All Nodes"
              class="w-40"
            />
          </div>
          
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="autoScroll"
              inputId="autoScroll"
              binary
            />
            <label for="autoScroll" class="text-sm">Auto-scroll to new events</label>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="bg-blue-50 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <span class="text-sm font-medium">Notifications:</span>
          <div class="flex gap-2">
            <BravoButton
              @click="testNotificationSound"
              icon="pi pi-volume-up"
              size="small"
              severity="secondary"
              text
              tooltip="Test Sound"
            />
            <BravoButton
              @click="testBrowserNotification"
              icon="pi pi-bell"
              size="small"
              severity="secondary"
              text
              tooltip="Test Browser Notification"
            />
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-3">
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="notificationSettings.enabled"
              @change="updateNotificationSettings({ enabled: notificationSettings.enabled })"
              inputId="notificationsEnabled"
              binary
            />
            <label for="notificationsEnabled" class="text-sm">Enable notifications</label>
          </div>
          
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="notificationSettings.soundEnabled"
              @change="updateNotificationSettings({ soundEnabled: notificationSettings.soundEnabled })"
              inputId="soundEnabled"
              binary
              :disabled="!notificationSettings.enabled"
            />
            <label for="soundEnabled" class="text-sm">Sound alerts</label>
          </div>
          
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="notificationSettings.browserNotifications"
              @change="updateNotificationSettings({ browserNotifications: notificationSettings.browserNotifications })"
              inputId="browserNotifications"
              binary
              :disabled="!notificationSettings.enabled"
            />
            <label for="browserNotifications" class="text-sm">Browser notifications</label>
          </div>
          
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="notificationSettings.showPresenceUpdates"
              @change="updateNotificationSettings({ showPresenceUpdates: notificationSettings.showPresenceUpdates })"
              inputId="showPresenceUpdates"
              binary
              :disabled="!notificationSettings.enabled"
            />
            <label for="showPresenceUpdates" class="text-sm">Presence updates</label>
          </div>
          
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="notificationSettings.showTypingIndicators"
              @change="updateNotificationSettings({ showTypingIndicators: notificationSettings.showTypingIndicators })"
              inputId="showTypingIndicators"
              binary
              :disabled="!notificationSettings.enabled"
            />
            <label for="showTypingIndicators" class="text-sm">Typing indicators</label>
          </div>
        </div>
      </div>

      <!-- Events List -->
      <div class="border rounded-lg overflow-hidden">
        <div class="bg-gray-100 px-4 py-2 border-b">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Events ({{ filteredEvents.length }})</span>
            <div class="flex items-center gap-2">
              <BravoButton
                @click="expandAll"
                icon="pi pi-plus"
                size="small"
                severity="secondary"
                text
              >
                Expand All
              </BravoButton>
              <BravoButton
                @click="collapseAll"
                icon="pi pi-minus"
                size="small"
                severity="secondary"
                text
              >
                Collapse All
              </BravoButton>
            </div>
          </div>
        </div>
        
        <div 
          ref="eventsContainer"
          class="max-h-96 overflow-y-auto"
        >
          <div v-if="filteredEvents.length === 0" class="p-8 text-center text-gray-500">
            <i class="pi pi-inbox text-4xl mb-4 block"></i>
            <p>No events to display</p>
            <p class="text-sm mt-2">
              {{ events.length === 0 ? 'Connect to start receiving events' : 'Try adjusting your filters' }}
            </p>
          </div>
          
          <div v-else class="divide-y">
            <div
              v-for="event in filteredEvents"
              :key="event.id"
              class="p-4 hover:bg-gray-50"
            >
              <div class="flex items-start justify-between mb-2">
                <div class="flex items-center gap-3">
                  <BravoBadge 
                    :value="getEventTypeLabel(event.eventType)" 
                    :severity="getEventTypeSeverity(event.eventType)"
                    size="small"
                  />
                  <BravoBadge 
                    v-if="getActualEventType(event)"
                    :value="getActualEventType(event)" 
                    severity="info"
                    size="small"
                  />
                  <span class="font-medium text-sm">{{ event.node }}</span>
                  <span class="text-xs text-gray-500">{{ formatDateTime(event.timestamp) }}</span>
                  <BravoBadge 
                    v-if="event.rawStanza"
                    value="Raw Stanza Available" 
                    severity="secondary"
                    size="small"
                  />
                </div>
                
                <div class="flex items-center gap-2">
                  <BravoButton
                    v-if="event.rawStanza"
                    @click="copyStanzaToClipboard(event.rawStanza)"
                    icon="pi pi-copy"
                    size="small"
                    severity="secondary"
                    text
                    tooltip="Copy raw stanza to clipboard"
                  />
                  <BravoButton
                    @click="toggleEventExpansion(event.id)"
                    :icon="expandedEvents.has(event.id) ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
                    size="small"
                    severity="secondary"
                    text
                  />
                </div>
              </div>
              
              <div class="text-sm text-gray-600 mb-2">
                <div v-if="event.publisher" class="mb-1">
                  <span class="font-medium">Publisher:</span> {{ event.publisher }}
                </div>
                <div class="mb-1">
                  <span class="font-medium">Items:</span> {{ event.items.length }}
                </div>
                <div v-if="getIssueInfo(event)" class="mb-1">
                  <span class="font-medium">Issue:</span> 
                  <router-link 
                    :to="getIssueLink(getIssueInfo(event)!)"
                    class="text-blue-600 hover:text-blue-800 underline ml-1"
                    target="_blank"
                  >
                    {{ getIssueInfo(event)!.objectId }}
                    <i class="pi pi-external-link ml-1 text-xs"></i>
                  </router-link>
                </div>
              </div>
              
              <!-- Raw Stanza - Always visible for debugging -->
              <div v-if="event.rawStanza" class="mt-2">
                <div class="text-sm font-medium mb-2 flex items-center gap-2">
                  <span>Raw Stanza:</span>
                  <BravoButton
                    @click="copyStanzaToClipboard(event.rawStanza)"
                    icon="pi pi-copy"
                    size="small"
                    severity="secondary"
                    text
                    tooltip="Copy to clipboard"
                  />
                </div>
                <div class="bg-gray-800 text-green-400 p-3 rounded text-xs font-mono max-h-32 overflow-y-auto border">
                  <pre>{{ formatXml(event.rawStanza) }}</pre>
                </div>
              </div>

              <div v-if="expandedEvents.has(event.id)" class="mt-3 space-y-3">
                <!-- Event Items -->
                <div v-if="event.items.length > 0">
                  <div class="text-sm font-medium mb-2">Parsed Items:</div>
                  <div class="bg-blue-50 p-3 rounded text-xs font-mono max-h-40 overflow-y-auto border">
                    <pre>{{ JSON.stringify(event.items, null, 2) }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BravoDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useXmppPubsub, type PubsubEvent } from '@/composables/useXmppPubsub'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'
import Dropdown from 'primevue/dropdown'
import Checkbox from 'primevue/checkbox'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// Composables
const { 
  connectionState, 
  events, 
  isConnected, 
  connect, 
  disconnect, 
  clearEvents: clearPubsubEvents,
  notificationSettings,
  updateNotificationSettings,
  testNotificationSound,
  testBrowserNotification
} = useXmppPubsub()

// Watch events array for changes
watch(events, () => {
  // Events updated
}, { immediate: true })

// Local state
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const eventsContainer = ref<HTMLElement>()
const expandedEvents = ref(new Set<string>())
const autoScroll = ref(true)
const selectedEventType = ref<string>('')
const selectedNode = ref<string>('')

// Filter options
const eventTypeOptions = computed(() => [
  { label: 'All Types', value: '' },
  { label: 'Notifications', value: 'notification' },
  { label: 'Presence Updates', value: 'presence' },
  { label: 'Typing Indicators', value: 'typing' },
  { label: 'Other', value: 'other' }
])

const nodeOptions = computed(() => {
  const nodes = [...new Set(events.value.map(e => e.node))].sort()
  return [
    { label: 'All Nodes', value: '' },
    ...nodes.map(node => ({ 
      label: node === 'chat' ? 'Chat Messages' : 
             node === 'presence' ? 'Presence Updates' :
             node === 'iq-pubsub' ? 'Pubsub Responses' :
             node, 
      value: node 
    }))
  ]
})

// Filtered events
const filteredEvents = computed(() => {
  let filtered = events.value
  
  console.log('🔍 XMPP PubSub Modal: Filtering events', {
    totalEvents: events.value.length,
    selectedEventType: selectedEventType.value,
    selectedNode: selectedNode.value,
    eventTypes: [...new Set(events.value.map(e => e.eventType))],
    nodes: [...new Set(events.value.map(e => e.node))]
  })

  if (selectedEventType.value) {
    filtered = filtered.filter(e => e.eventType === selectedEventType.value)
    console.log('🔍 XMPP PubSub Modal: After event type filter:', filtered.length)
  }

  if (selectedNode.value) {
    filtered = filtered.filter(e => e.node === selectedNode.value)
    console.log('🔍 XMPP PubSub Modal: After node filter:', filtered.length)
  }

  console.log('🔍 XMPP PubSub Modal: Final filtered events:', filtered.length)
  return filtered
})

// Watch for new events and auto-scroll
watch(
  () => events.value.length,
  async () => {
    if (autoScroll.value && eventsContainer.value) {
      await nextTick()
      eventsContainer.value.scrollTop = 0 // Scroll to top since newest events are first
    }
  }
)

// Methods
const toggleConnection = async () => {
  if (isConnected.value) {
    await disconnect()
  } else {
    await connect()
  }
}

const clearEvents = () => {
  clearPubsubEvents()
  expandedEvents.value.clear()
}

const resetFilters = () => {
  selectedEventType.value = ''
  selectedNode.value = ''
}

const toggleEventExpansion = (eventId: string) => {
  if (expandedEvents.value.has(eventId)) {
    expandedEvents.value.delete(eventId)
  } else {
    expandedEvents.value.add(eventId)
  }
}

const expandAll = () => {
  filteredEvents.value.forEach(event => {
    expandedEvents.value.add(event.id)
  })
}

const collapseAll = () => {
  expandedEvents.value.clear()
}

const getStatusSeverity = (status: string) => {
  switch (status) {
    case 'authenticated': return 'success'
    case 'connecting': return 'warning'
    case 'error': return 'danger'
    default: return 'secondary'
  }
}

const getEventTypeSeverity = (eventType: string) => {
  switch (eventType) {
    case 'notification': return 'info'
    case 'presence': return 'success'
    case 'typing': return 'warning'
    default: return 'secondary'
  }
}

const getEventTypeLabel = (eventType: string) => {
  switch (eventType) {
    case 'notification': return 'Notification'
    case 'presence': return 'Presence'
    case 'typing': return 'Typing'
    default: return eventType
  }
}

// Extract the actual event type from JSON content
const getActualEventType = (event: PubsubEvent): string | null => {
  if (event.items && event.items.length > 0) {
    for (const item of event.items) {
      if (item.payload && item.payload.length > 0) {
        for (const payload of item.payload) {
          if (payload.name === 'json' && payload.children && payload.children.length > 0) {
            try {
              const jsonContent = payload.children[0]
              if (typeof jsonContent === 'string') {
                const parsedJson = JSON.parse(jsonContent)
                return parsedJson.event_type || null
              }
            } catch (error) {
              // Ignore parsing errors
            }
          }
        }
      }
    }
  }
  return null
}

// Extract issue information from JSON content
const getIssueInfo = (event: PubsubEvent): { object: string; objectId: string } | null => {
  if (event.items && event.items.length > 0) {
    for (const item of event.items) {
      if (item.payload && item.payload.length > 0) {
        for (const payload of item.payload) {
          if (payload.name === 'json' && payload.children && payload.children.length > 0) {
            try {
              const jsonContent = payload.children[0]
              if (typeof jsonContent === 'string') {
                const parsedJson = JSON.parse(jsonContent)
                if (parsedJson.object && parsedJson.object_id) {
                  return {
                    object: parsedJson.object,
                    objectId: parsedJson.object_id
                  }
                }
              }
            } catch (error) {
              // Ignore parsing errors
            }
          }
        }
      }
    }
  }
  return null
}

// Generate a link to the issue
const getIssueLink = (issueInfo: { object: string; objectId: string }): string => {
  if (issueInfo.object === 'issues') {
    return `/inbox/case/${issueInfo.objectId}`
  }
  return '#'
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('en-US', {
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

const formatXml = (xml: string) => {
  try {
    // Basic XML formatting - add line breaks and indentation
    return xml
      .replace(/></g, '>\n<')
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
  } catch {
    return xml
  }
}

const copyStanzaToClipboard = async (stanza: string) => {
  try {
    await navigator.clipboard.writeText(stanza)
    console.log('📋 XMPP PubSub: Raw stanza copied to clipboard')
    // You could add a toast notification here if desired
  } catch (error) {
    console.error('📋 XMPP PubSub: Failed to copy stanza to clipboard:', error)
    // Fallback: try to select the text for manual copy
    try {
      const textArea = document.createElement('textarea')
      textArea.value = stanza
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      console.log('📋 XMPP PubSub: Raw stanza copied to clipboard (fallback method)')
    } catch (fallbackError) {
      console.error('📋 XMPP PubSub: Fallback copy method also failed:', fallbackError)
    }
  }
}
</script>

<style scoped>
.xmpp-pubsub-modal :deep(.p-dialog-content) {
  padding: 1.5rem;
}

.xmpp-pubsub-modal :deep(.p-dialog-header) {
  padding: 1rem 1.5rem;
}

pre {
  white-space: pre-wrap;
  word-break: break-word;
}
</style> 