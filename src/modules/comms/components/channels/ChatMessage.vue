<script setup lang="ts">
import { computed } from 'vue';
import type { Message, XMPPNotificationObject, MessageAction } from '../../types';
import { tryParseMessageContent, shouldHideMessage, formatFileSize } from '../../types';
import { useCommunicationsStore } from '../../stores/communications';
import { getAvatarColor } from '../../utils/commHelpers';
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue';
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue';
import linkifyHtml from 'linkify-html';
import type { LinkPreview } from '@/composables/useLinkPreview';

const props = defineProps<{
  message: Message;
  previousMessage?: Message;
  participants: Array<{ id: string; name: string; avatar?: string }>;
  messagePreviews: Map<string, LinkPreview[]>;
  onActionClick: (action: MessageAction) => void;
  onImageClick: (url: string, alt: string) => void;
  onImageLoad?: () => void;
}>();

const store = useCommunicationsStore();

// Function to format message content with highlighted mentions and enhanced parsing
const formatMessageContent = (content: string, isFromBot = false) => {
  let formattedContent = content;

  // Replace @mentions with styled spans
  formattedContent = formattedContent.replace(/@(\w+)/g, '<span class="mention">@$1</span>');

  // Replace newline characters with br tags (both actual and escaped)
  formattedContent = formattedContent.replace(/(?:\\r\\n|\\r|\\n|\r\n|\r|\n)/g, '<br>');

  // Parse links in markdown format [text](url)
  const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  formattedContent = formattedContent.replace(markdownLinkRegex, '<a href="$2" target="_blank">$1</a>');

  // Handle all other links using linkify
  formattedContent = linkifyHtml(formattedContent, { target: '_blank' });

  return formattedContent;
};

// Enhanced message parsing function
const parseXMPPMessage = (message: Message): {
  parsedContent: XMPPNotificationObject | null;
  displayContent: string;
  shouldHide: boolean;
} => {
  const parseResult = tryParseMessageContent(message.content);
  
  if (!parseResult.isParsed) {
    // Plain text message
    return {
      parsedContent: null,
      displayContent: formatMessageContent(message.content),
      shouldHide: false
    };
  }

  const notificationObj = parseResult.data!;
  const shouldHide = shouldHideMessage(notificationObj);

  // Handle different message types
  let displayContent = '';
  
  if (notificationObj.message) {
    displayContent = formatMessageContent(
      decodeURIComponent(String(notificationObj.message).replace(/\+/g, ' '))
    );
  }

  return {
    parsedContent: notificationObj,
    displayContent,
    shouldHide
  };
};

// Check if message is from current user
const isMessageFromCurrentUser = (message: Message): boolean => {
  return store.isCurrentUser(message.senderId);
};

// Get sender details with enhanced information
const getSenderDetails = (message: Message) => {
  const participant = props.participants.find(p => p.id === message.senderId);
  const parseResult = parseXMPPMessage(message);
  
  // Use "Unknown Contact" instead of showing the user ID when no participant is found
  const fallbackName = participant?.name || 'Unknown Contact';
  const displayName = parseResult.parsedContent?.context?.from_name || fallbackName;
  
  return {
    name: displayName,
    avatar: parseResult.parsedContent?.context?.from_avatar || participant?.avatar,
    initials: displayName.charAt(0).toUpperCase(),
    serviceAvatar: getServiceAvatar(parseResult.parsedContent),
    viaServiceLabel: getViaServiceLabel(parseResult.parsedContent)
  };
};

// Get service avatar for external messages
const getServiceAvatar = (notificationObj: XMPPNotificationObject | null): string | undefined => {
  if (!notificationObj) return undefined;
  
  if (notificationObj.channel === 'chat' && notificationObj.service === 'slack') {
    return notificationObj.context?.from_avatar || '/resources/images/app/slack.png';
  } else if (notificationObj.channel === 'facebook') {
    return notificationObj.context?.from_avatar || '/resources/images/app/appIcon_facebook.png';
  }
  
  return notificationObj.context?.from_avatar;
};

// Get via service label for external messages
const getViaServiceLabel = (notificationObj: XMPPNotificationObject | null): string | undefined => {
  if (!notificationObj) return undefined;
  
  if (notificationObj.channel === 'chat' && notificationObj.service === 'slack') {
    return '(via&nbsp;Slack)';
  } else if (notificationObj.channel === 'facebook') {
    return '(via&nbsp;FB&nbsp;Messenger)';
  }
  
  return undefined;
};

// Handle action clicks (for bot interactions)
const handleActionClick = async (action: MessageAction) => {
  props.onActionClick(action);
};

// Check if we should show sender name/avatar (similar to original logic)
const shouldShowSenderInfo = (message: Message, previousMessage?: Message): boolean => {
  if (!previousMessage) return true;
  
  // Different sender
  if (message.senderId !== previousMessage.senderId) return true;
  
  // Time difference > 2 minutes
  const timeDiff = message.timestamp.getTime() - previousMessage.timestamp.getTime();
  if (timeDiff > 120000) return true; // 2 minutes
  
  return false;
};

// Helper function to safely extract hostname from URL
const getHostnameFromUrl = (url: string): string => {
  try {
    return new URL(url).hostname;
  } catch {
    return url;
  }
};

const shouldShow = computed(() => shouldShowSenderInfo(props.message, props.previousMessage));
const senderDetails = computed(() => getSenderDetails(props.message));
const parsedMessage = computed(() => parseXMPPMessage(props.message));
const isFromCurrentUser = computed(() => isMessageFromCurrentUser(props.message));
</script>

<template>
  <!-- System notifications -->
  <div 
    v-if="message.type === 'system'"
    class="flex justify-center"
  >
    <div class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm">
      {{ message.content }}
    </div>
  </div>

  <!-- Chat messages -->
  <div 
    v-else
    class="message"
    :class="{ 
      'align-right': isFromCurrentUser, 
      'align-left': !isFromCurrentUser,
      'mt-3': shouldShow
    }"
  >
    <!-- Avatar for all messages -->
    <div 
      v-if="shouldShow"
      class="avatar-wrapper"
    >
      <BravoAvatar
        :firstName="senderDetails.name.split(' ')[0]"
        :lastName="senderDetails.name.split(' ')[1] || ''"
        :backgroundColor="getAvatarColor(isFromCurrentUser ? message.senderId : senderDetails.name)"
        :style="{ color: '#ffffff' }"
        size="32"
      />
    </div>

    <!-- Sender name and timestamp -->
    <div 
      v-if="shouldShow"
      class="from-name"
    >
      {{ senderDetails.name }}
      <span 
        v-if="senderDetails.viaServiceLabel"
        class="text-xs text-gray-500 ml-1"
        v-html="senderDetails.viaServiceLabel"
      ></span>
      <BravoTimestamp 
        :datetime="message.timestamp.toISOString()"
        short
        class="timestamp ml-2"
      />
    </div>

    <!-- Message content -->
    <div 
      v-if="parsedMessage.parsedContent?.message || (!parsedMessage.parsedContent && message.content)"
      class="message-text"
      :class="{
        'align-right': isFromCurrentUser
      }"
    >
      <span v-html="parsedMessage.displayContent || formatMessageContent(message.content)"></span>
    </div>

    <!-- Link previews -->
    <div 
      v-if="messagePreviews.get(message.id)?.length"
      class="message-link-previews mt-2"
    >
      <div 
        v-for="preview in messagePreviews.get(message.id)?.filter(p => !p.isLoading)"
        :key="preview.url"
        class="link-preview"
      >
        <!-- Error state -->
        <div v-if="preview.error" class="link-preview-error">
          <div class="text-sm text-gray-500">
            <i class="pi pi-exclamation-triangle mr-2"></i>
            Failed to load preview for {{ getHostnameFromUrl(preview.url) }}
          </div>
        </div>
        
        <!-- Preview content -->
        <a
          v-else
          :href="preview.url"
          target="_blank"
          rel="noopener noreferrer"
          class="link-preview-content"
        >
          <!-- Preview image -->
          <div 
            v-if="preview.image"
            class="link-preview-image"
          >
            <img 
              :src="preview.image"
              :alt="preview.title || 'Link preview'"
              class="w-full h-full object-cover"
              @error="(e) => {
                console.warn('Failed to load preview image:', preview.image);
                (e.target as HTMLImageElement).style.display = 'none';
                (e.target as HTMLImageElement).parentElement?.classList.add('image-failed');
              }"
              @load="(e) => console.log('Successfully loaded preview image:', preview.image)"
            />
            <!-- Fallback content when image fails to load -->
            <div class="link-preview-image-fallback">
              <i class="pi pi-image text-3xl text-gray-400"></i>
              <div class="text-xs text-gray-500 mt-2">{{ getHostnameFromUrl(preview.url) }}</div>
            </div>
          </div>
          
          <!-- Preview text content -->
          <div class="link-preview-text">
            <div class="link-preview-site" v-if="preview.siteName">
              {{ preview.siteName }}
            </div>
            <div class="link-preview-title" v-if="preview.title">
              {{ preview.title }}
            </div>
          </div>
        </a>
      </div>
    </div>

    <!-- Message attachments -->
    <div class="message-attachments">
      <!-- Single attachment (backward compatibility) -->
      <template v-if="parsedMessage.parsedContent?.attachment && !parsedMessage.parsedContent?.attachments">
        <!-- Image attachment -->
        <div
          v-if="(parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.startsWith('image/')"
          class="image-wrapper"
          @click="onImageClick(
            (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.url || '',
            (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.name || 'Image'
          )"
        >
          <img 
            :src="(parsedMessage.parsedContent as XMPPNotificationObject).attachment?.preview || (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.url"
            :alt="(parsedMessage.parsedContent as XMPPNotificationObject).attachment?.name"
            class="image max-w-[300px] h-auto rounded-lg cursor-pointer"
            @load="onImageLoad?.()"
          />
        </div>

        <!-- File attachment (non-image) -->
        <div
          v-else
          class="file-attachment"
        >
          <a
            :href="(parsedMessage.parsedContent as XMPPNotificationObject).attachment?.url"
            target="_blank"
            class="file-attachment-link"
            download
          >
            <div class="file-wrapper">
              <i 
                class="file-icon pi text-2xl mr-3 text-gray-600"
                :class="{
                  'pi-file-pdf text-red-600': (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type === 'application/pdf',
                  'pi-file-excel text-green-600': (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.includes('sheet') || (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.includes('excel'),
                  'pi-file-word text-blue-600': (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.includes('word') || (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.includes('document'),
                  'pi-file': !(parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.includes('pdf') && !(parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.includes('sheet') && !(parsedMessage.parsedContent as XMPPNotificationObject).attachment?.type.includes('word')
                }"
              ></i>
              <span class="file-name-wrapper">
                <div class="file-name">{{ (parsedMessage.parsedContent as XMPPNotificationObject).attachment?.name }}</div>
                <div class="file-size">{{ formatFileSize((parsedMessage.parsedContent as XMPPNotificationObject).attachment?.size || 0) }}</div>
              </span>
            </div>
          </a>
        </div>
      </template>

      <!-- Multiple attachments -->
      <template v-if="parsedMessage.parsedContent?.attachments?.length">
        <div class="space-y-2">
          <div class="text-sm font-medium text-gray-500">
            Attachments ({{ (parsedMessage.parsedContent as XMPPNotificationObject).attachments?.length }})
          </div>
          <div 
            v-for="(attachment, attachmentIndex) in (parsedMessage.parsedContent as XMPPNotificationObject).attachments"
            :key="attachmentIndex"
            class="attachment-item"
          >
            <!-- Image attachment -->
            <div
              v-if="attachment.type.startsWith('image/')"
              class="image-wrapper"
              @click="onImageClick(attachment.url, attachment.name)"
            >
              <img 
                :src="attachment.preview || attachment.url"
                :alt="attachment.name"
                class="image max-w-[300px] h-auto rounded-lg cursor-pointer"
                @load="onImageLoad?.()"
              />
            </div>

            <!-- File attachment (non-image) -->
            <div
              v-else
              class="file-attachment"
            >
              <a
                :href="attachment.url"
                target="_blank"
                class="file-attachment-link"
                download
              >
                <div class="file-wrapper">
                  <i 
                    class="file-icon pi text-2xl mr-3 text-gray-600"
                    :class="{
                      'pi-file-pdf text-red-600': attachment.type === 'application/pdf',
                      'pi-file-excel text-green-600': attachment.type.includes('sheet') || attachment.type.includes('excel'),
                      'pi-file-word text-blue-600': attachment.type.includes('word') || attachment.type.includes('document'),
                      'pi-file': !attachment.type.includes('pdf') && !attachment.type.includes('sheet') && !attachment.type.includes('word')
                    }"
                  ></i>
                  <span class="file-name-wrapper">
                    <div class="file-name">{{ attachment.name }}</div>
                    <div class="file-size">{{ formatFileSize(attachment.size) }}</div>
                  </span>
                </div>
              </a>
            </div>
          </div>
        </div>
      </template>

      <!-- Knowledge Base content -->
      <div 
        v-if="parsedMessage.parsedContent?.kb"
        class="kb-preview bg-blue-50 border border-blue-200 rounded-lg p-3 mt-2"
      >
        <div class="font-medium text-blue-900">{{ (parsedMessage.parsedContent as XMPPNotificationObject).kb?.title }}</div>
        <div class="text-sm text-blue-700 mt-1">{{ (parsedMessage.parsedContent as XMPPNotificationObject).kb?.preview }}</div>
      </div>

      <!-- Bot actions -->
      <div 
        v-if="parsedMessage.parsedContent?.actions?.length"
        class="bot-actions"
      >
        <button
          v-for="action in (parsedMessage.parsedContent as XMPPNotificationObject).actions"
          :key="action.key"
          @click="handleActionClick(action)"
          class="bot-action-btn"
          :class="{ 'selected': action.selected }"
        >
          {{ action.lbl }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Message layout styles similar to the provided example */
.message {
  padding-right: 10px;
  padding-left: 48px;
  position: relative;
  margin-bottom: 4px;
}

/* Spacing between different message types */
.align-left + .align-right {
  margin-top: 10px;
}

.align-right + .align-left {
  margin-top: 10px;
}

.message.align-right {
  padding-left: 0px;
  padding-right: 48px;
  text-align: right;
}

.avatar-wrapper {
  overflow: hidden;
  position: absolute;
  top: 26px;
  left: 0px;
  width: 36px;
  height: 36px;
}

.align-right .avatar-wrapper {
  left: auto;
  right: 0px;
}

.avatar {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50%;
  border: 2px solid #ffffff;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.from-name {
  font-size: 12px;
  font-weight: bold;
  color: var(--text-color-secondary);
  margin: 24px 4px 2px 4px;
}

.timestamp {
  font-weight: normal;
  color: #999999;
}

.message-text {
  background-color: var(--surface-50);
  border-radius: 12px;
  margin: 2px 0px 0px 0px;
  padding: 12px 18px;
  position: relative;
  word-break: break-word;
  font-size: 14px;
  line-height: 20px;
  min-width: auto;
  max-width: 82%;
  display: inline-block;
  color: var(--text-color-primary);
  text-align: left;
  font-weight: 400;
}

.align-right .message-text {
  background-color: var(--primary-50);
}

.message-attachments {
  margin-top: 4px;
}

.image-wrapper {
  cursor: pointer;
  display: inline-block;
}

.image {
  border-radius: 12px;
  max-width: 200px;
  height: auto;
  border: 1px solid var(--surface-200);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: var(--surface-0);
}

.file-attachment {
  max-width: 65%;
  min-width: 30%;
  margin: 4px 0px;
  padding: 3px 0px 3px 0px;
  border-radius: 12px;
  background-color: #ffffff;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-attachment-link {
  text-decoration: none;
}

.file-attachment-link:hover .file-wrapper {
  transition: 250ms all linear;
  border: 1px solid #007AFF;
  background-color: #f8f9fa;
}

.file-wrapper {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  text-align: left;
  display: flex;
  align-items: center;
}

.file-icon {
  font-size: 28px;
  margin-right: 12px;
  vertical-align: middle;
  color: #666666;
  margin-bottom: 2px;
}

.file-name {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  line-height: 1.3;
}

.file-size {
  font-size: 12px;
  color: #999999;
  margin-top: 2px;
}

.kb-preview {
  max-width: 82%;
  display: inline-block;
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 12px;
  padding: 12px 16px;
}

.kb-preview .font-medium {
  color: #1a5490 !important;
  font-weight: 600;
}

.kb-preview .text-sm {
  color: #2563eb !important;
  margin-top: 4px;
}

/* Multiple attachments styling */
.attachment-item {
  margin-bottom: 8px;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

/* Deep styles for message content */
:deep(.message-text p) {
  margin-block-start: 0;
  margin-block-end: 0;
}

:deep(.message-text p:not(:first-child)) {
  margin-top: 6px;
}

:deep(.message-text p:not(:last-child)) {
  margin-bottom: 6px;
}

:deep(.message-text ol),
:deep(.message-text ul) {
  padding-inline-start: 14px;
}

:deep(.message-text a) {
  color: #007AFF;
  text-decoration: underline;
  transition: color 0.2s ease;
}

:deep(.message-text a:hover) {
  color: #0056b3;
  text-decoration: underline;
}

/* Bot action button styles */
.bot-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.bot-action-btn {
  padding: 8px 12px;
  font-size: 13px;
  border: 1px solid #007AFF;
  color: #007AFF;
  background-color: #ffffff;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.bot-action-btn:hover {
  background-color: #f0f8ff;
  border-color: #0056b3;
  color: #0056b3;
}

.bot-action-btn.selected {
  background-color: #007AFF;
  color: #ffffff;
}

/* Link preview styles */
.message-link-previews {
  margin-top: 8px;
}

.link-preview {
  margin-bottom: 8px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--surface-200);
  background-color: var(--surface-0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top left;
}

.link-preview.align-right {
  transform-origin: top right;
}

.link-preview-loading {
  min-height: 80px;
  transform: scale(0.9);
  opacity: 0.8;
}

.link-preview-error {
  padding: 12px;
}

.link-preview-content {
  display: block;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  animation: slideInAndGrow 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideInAndGrow {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.link-preview-content:hover {
  background-color: var(--surface-50);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.link-preview-image {
  width: 100%;
  height: 160px;
  background-color: var(--surface-100);
  overflow: hidden;
  position: relative;
}

.link-preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.link-preview-content:hover .link-preview-image img {
  transform: scale(1.02);
}

.link-preview-image-fallback {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-100);
  z-index: 1;
}

.link-preview-image.image-failed .link-preview-image-fallback {
  display: flex;
}

.link-preview-image:not(.image-failed) .link-preview-image-fallback {
  display: none;
}

.link-preview-text {
  padding: 12px 16px;
}

.link-preview-site {
  font-size: 11px;
  color: var(--text-color-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.link-preview-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-primary);
  line-height: 1.3;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.link-preview-description {
  font-size: 12px;
  color: var(--text-color-secondary);
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.link-preview-url {
  font-size: 11px;
  color: var(--text-color-muted);
  font-family: monospace;
}

/* Mention styles */
.mention {
  display: inline;
  background-color: rgba(29, 155, 209, 0.1);
  border-radius: 3px;
  padding: 1px 2px;
  color: #1D9BD1;
  font-weight: 500;
}
</style> 