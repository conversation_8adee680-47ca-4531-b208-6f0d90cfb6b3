<template>
  <iframe ref="iframe" frameborder="0" class="article-preview"></iframe>
</template>

<script lang="ts">
import { useUserStore } from '@/stores';

export default {
  name: 'ArticlePreview',
  props: {
    content: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: false,
      default: '',
    },
    subtitle: {
      type: String,
      required: false,
      default: '',
    },
    updated: {
      type: String,
      required: false,
      default: '',
    },
    visibility: {
      type: String,
      required: false,
      default: 'Public',
    },
    screensUrl: {
      type: String,
      required: false,
      default: 'https://f.goboomtown.com/screens',
    },
    articleTheme: {
      type: String,
      required: false,
      default: 'modern',
    },
    libraryCss: {
      type: String,
      required: false,
      default: '',
    },
    articleVisibility: {
      type: String,
      required: false,
      default: 'Public',
    },
  },
  mounted() {
    this.renderContent();
  },
  methods: {
    renderContent() {
      const iframe = this.$refs.iframe as HTMLIFrameElement;
      const doc = iframe.contentDocument || (iframe.contentWindow?.document);
      const baseUrl = useUserStore().screensUrl || this.screensUrl;

      const styles = `
      <style>
        ${this.libraryCss}
        @import url("//fonts.googleapis.com/css2?family=Inter");
        body {
            max-width: 1100px;
            width: 100%;
            margin: 0 auto !important;
            padding: 2rem 4rem 2rem 4rem !important;
            overflow-x: hidden;
            font-family: "Inter", Helvetica, sans-serif;
        }
        
        body > div:nth-of-type(2) {
            padding-bottom: 1rem;
        }

        .fa, .fas {
          font-family: "Font Awesome 5 Pro" !important;
        }
        
        .title-section {
          margin-bottom: 2rem;
        }
        
        .title-field {
          margin-bottom: 1rem;
        }
        
        .title-field h1 {
          margin: 0;
          font-size: 28px;
          color: var(--text-color-primary, #1e293b);
          line-height: 1.5;
          font-weight: 600;
        }
        
        .subtitle-field {
          margin-bottom: 1rem;
        }
        
        .subtitle-field h2 {
          margin: 0;
          font-size: 1.25rem;
          color: var(--text-color-secondary, #64748b);
          font-weight: normal;
        }
        
        .article-meta {
          display: flex;
          align-items: center;
          color: var(--text-color-secondary, #64748b);
          padding: 0 0 1rem 0;
          font-size: 0.875rem;
        }
        
        .visibility {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          margin-left: 0.5rem;
        }
        
        .visibility i {
          font-size: 0.875rem;
        }

        body, * {
          font-family: "Inter", Helvetica, sans-serif !important;
        }
        p, a, span, em, li, h1, h2, h3, h4, h5,  a.scrollSpy-active, a.scrollSpy-active:hover, a.scrollSpy-active:focus {
          font-family: "Inter";
          color: #4d4d4d;
        }

        a {
          color: #0a8484;
          text-decoration: underline;
        }

        a:focus,
        a:hover {
          color: #097777;
        }

        .navLink:focus {
          border: 2px solid #005DB2;
          border-radius: 8px;
        }

        .link-color {
          color: #0a8484;
        }

        .link-color-border {
          border-color: #0a8484;
        }

        .link-color-hover:hover {
          color: #0a8484;
        }

        .link-color-border-hover:hover {
          border-color: #0a8484;
        }

        .link-color-background {
          background-color: #0a8484;
          color: white;
        }


        .link-color-background-hover:hover {
          background-color: #0a8484;
          color: white;
        }

        .link-color-background-hover:hover a {
          color: white;
        }

        .toc-a-selected {
          background-color: #0a8484;
        }

        .tabs .tab-title>button.active {
          background: #0a8484;
          color: #efefef;
        }

        ul, ol {
          margin-left: 2.1rem !important;
        }
      </style>
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/kb.css">
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/header.css">
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/accessibility.css">
      <link rel="stylesheet" type="text/css" href="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/css/foundation.min.css">
      <link rel="stylesheet" href="${baseUrl}/assets/stylesheets/all.min.css">
      <link rel="stylesheet" href="${baseUrl}/assets/stylesheets/codesnippets.css">
      <link rel="stylesheet" type="text/css" href="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/css/normalize.css">
      `;

      const scripts = `
      <script src="${baseUrl}/assets/javascripts/jquery/3.5.1/jquery-3.5.1.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/js/foundation.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/jquery/plugins/jquery.waitforimages.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/scrollnav-3.0.2/scrollnav.min.umd.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/js/vendor/modernizr.js"><\/script>
      <script src="${baseUrl}/article/${this.articleTheme}/javascripts/default.js"><\/script>
      <script src="${baseUrl}/article/shared/javascripts/kbSearch.js"><\/script>
      <script src="${baseUrl}/article/shared/javascripts/kbArticle.js"><\/script>
      `;

      // Create title section HTML
      const titleSection = `
        <div class="title-section">
          <div class="title-field">
            <h1>${this.title || ''}</h1>
          </div>
          ${this.subtitle ? `
          <div class="subtitle-field">
            <h2>${this.subtitle}</h2>
          </div>
          ` : ''}
          <div class="article-meta">
            ${this.updated ? `
            <span class="updated">
              Updated ${this.updated}
            </span>
            ` : ''}
          </div>
        </div>
      `;

      const html = `
      <!DOCTYPE html>
      <html>
        <head>
          ${styles}
          ${scripts}
        </head>
        <body>
          ${titleSection}
          ${this.content || ''}
        </body>
      </html>
      `;

      if (doc) {
        doc.open();
        doc.writeln(html);
        doc.close();
      }
    }
  },
  watch: {
    // Re-render when content or CSS changes
    content() {
      this.renderContent();
    },
    libraryCss() {
      this.renderContent();
    },
    title() {
      this.renderContent();
    },
    subtitle() {
      this.renderContent();
    },
    updated() {
      this.renderContent();
    },
    visibility() {
      this.renderContent();
    }
  }
};
</script>

<style scoped>
  .article-preview {
    flex: 1;
    width: 100%;
  }
</style>

