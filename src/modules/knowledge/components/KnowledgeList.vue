<script setup lang="ts">
import { useKnowledgeStore } from '../stores/knowledge';
import { storeToRefs } from 'pinia';
import { ref, onMounted, watch, computed, onUnmounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { stripHtmlAndDecodeEntities, getStatusState, toISOString, findIdInObject } from '../utils/helpers';

import BravoDataTable from 'primevue/datatable';
import type { DataTableSortEvent } from 'primevue/datatable';
import Column from 'primevue/column';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoDrawer from '@services/ui-component-library/components/BravoDrawer.vue';
import type { MenuItem } from 'primevue/menuitem';
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue';
import BravoIconField from '@services/ui-component-library/components/BravoIconField.vue';
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue';
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
// FilterDropdown has been replaced with BravoFilterField
import KnowledgeFilters from './KnowledgeFilters.vue';
import KnowledgeListZeroState from './KnowledgeListZeroState.vue';
import CommentsDrawer from './CommentsDrawer.vue';

import AddArticleModal from '../components/AddArticleModal.vue';
import AddLabelModal from '../components/AddLabelModal.vue';
import DeleteArticleModal from '../components/DeleteArticleModal.vue';
import ArchiveArticleModal from '../components/ArchiveArticleModal.vue';
import BravoConfirmDialog from '@services/ui-component-library/components/BravoConfirmDialog.vue';
import Skeleton from 'primevue/skeleton';

// Import bulk action dialogs
import ArchiveArticlesDialog from './dialogs/ArchiveArticles.vue';
import UnpublishArticlesDialog from './dialogs/UnpublishArticles.vue';
import PublishArticlesDialog from './dialogs/PublishArticles.vue';
import ShareArticlesDialog from './dialogs/ShareArticles.vue';
import UnshareArticlesDialog from './dialogs/UnshareArticles.vue';
import UpdateArticlesTagDialog from './dialogs/UpdateArticlesTag.vue';
import UpdateProductsDialog from './dialogs/UpdateProducts.vue';
import UpdateAccessControlsDialog from './dialogs/UpdateAccessControls.vue';
import UpdateLabelsDialog from './dialogs/UpdateLabels.vue';

import Tooltip from 'primevue/tooltip';
import { useI18n } from 'vue-i18n';
import MultiSelect from 'primevue/multiselect';
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue';
import { useUserStore } from '@/stores/user';
import { usePermissions } from '@/composables/usePermissions';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';

interface MenuCommand {
  item: {
    data: any;
  };
}

// Initialize toast
const toast = useToast()
const confirm = useConfirm()

const { t } = useI18n();

const store = useKnowledgeStore()
const userStore = useUserStore();
const knowledgeAPI = useKnowledgeAPI();
const router = useRouter()
const route = useRoute()
const { currentList, searchQuery, statusFilter, totalCommentsCount } = storeToRefs(store)
const selectedItems = ref<any[]>([])

// Bulk actions menu and dialog state
const actionsMenu = ref();
const showLabelsDialog = ref(false);
const showAccessControlsDialog = ref(false);
const showProductsDialog = ref(false);
const showTagsDialog = ref(false);
const showShareDialog = ref(false);
const showUnshareDialog = ref(false);
const showPublishDialog = ref(false);
const showUnpublishDialog = ref(false);
const showArchiveDialog = ref(false);

// Build actions menu items based on permissions
const actionsMenuItems = computed(() => {
  const menuItems: { label: string; value: string; icon: string; command: () => void }[] = [];

  if (can.editArticle()) {
    menuItems.push(...[{
      label: t('knowledge.actionsMenu.update_labels'),
      value: 'label',
      icon: 'pi pi-tag',
      command: () => onBulkActionClick('label')
    },
    {
      label: t('knowledge.actionsMenu.update_access_controls'),
      value: 'access',
      icon: 'pi pi-lock',
      command: () => onBulkActionClick('access')
    },
    {
      label: t('knowledge.actionsMenu.add_rem_products'),
      value: 'products',
      icon: 'pi pi-box',
      command: () => onBulkActionClick('products')
    },
    {
      label: t('knowledge.actionsMenu.add_rem_tags'),
      value: 'tags',
      icon: 'pi pi-tags',
      command: () => onBulkActionClick('tags')
    },
    {
      label: t('knowledge.actionsMenu.share_with_orgs'),
      value: 'share',
      icon: 'pi pi-share-alt',
      command: () => onBulkActionClick('share')
    },
    {
      label: t('knowledge.actionsMenu.unshare_from_orgs'),
      value: 'unshare',
      icon: 'pi pi-share-alt',
      command: () => onBulkActionClick('unshare')
    }]);
  }

  if (can.publishArticle()) {
    menuItems.push(...[{
      label: t('knowledge.actionsMenu.publish'),
      value: 'publish',
      icon: 'pi pi-check-square',
      command: () => onBulkActionClick('publish')
    },
    {
      label: t('knowledge.actionsMenu.unpublish'),
      value: 'unpublish',
      icon: 'pi pi-times-circle',
      command: () => onBulkActionClick('unpublish')
    }]);
  }

  if (can.deleteArticle()) {
    menuItems.push(...[{
      label: t('knowledge.actionsMenu.archive'),
      value: 'archive',
      icon: 'pi pi-inbox',
      command: () => onBulkActionClick('archive')
    }]);
  }

  return menuItems;
});

// Handle bulk action clicks
const onBulkActionClick = (action: string) => {
  switch (action) {
    case 'label':
      showLabelsDialog.value = true;
      break;
    case 'access':
      showAccessControlsDialog.value = true;
      break;
    case 'products':
      showProductsDialog.value = true;
      break;
    case 'tags':
      showTagsDialog.value = true;
      break;
    case 'share':
      showShareDialog.value = true;
      break;
    case 'unshare':
      showUnshareDialog.value = true;
      break;
    case 'publish':
      showPublishDialog.value = true;
      break;
    case 'unpublish':
      showUnpublishDialog.value = true;
      break;
    case 'archive':
      showArchiveDialog.value = true;
      break;
  }
};

// Function to show actions menu
const showActionsMenu = (event: Event) => {
  if (actionsMenuItems.value.length) {
    actionsMenu.value.toggle(event);
  }
};

// Dialog handlers - these will refresh the search results after bulk operations
const handleLabelUpdate = (labels: string[]) => {
  setTimeout(() => {
    if (filtersRef.value) {
      filtersRef.value.performSearch();
    }
  }, 1000);
};

const handleAccessControlsUpdate = (data: { accessLevel: string; teamAccessTo: string[] }) => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

const handleProductsUpdate = (products: string[]) => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

const handleTagsUpdate = (tags: string[]) => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

const handleShareArticles = (organizations: string[]) => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

const handleUnshareArticles = (organizations: string[]) => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

const handlePublishArticles = () => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

const handleUnpublishArticles = () => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

const handleArchiveArticles = () => {
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
};

// Change this to ONLY use loadingContent for the DataTable
const isLoading = computed(() => store.loadingContent)

// Update debug computed properties to use currentList instead
const itemsCount = computed(() => currentList.value?.length || 0)
const currentListCount = computed(() => currentList.value?.length || 0)
const totalItemsCount = computed(() => store.totalCount || 0)

// Create a local computed property to ensure we have a valid array
const tableItems = computed(() => {
  if (!currentList.value || !Array.isArray(currentList.value)) {
    console.error('currentList is not an array:', currentList.value)
    return []
  }
  return currentList.value
})

// Key to force re-render of the table when items array changes
const tableKey = ref(0)
const currentPage = ref(1)
const rowsPerPage = ref(25)
const addArticleRef = ref()
const addLabelRef = ref()
const filtersRef = ref()
const pageEventTimeout = ref<number | null>(null)



// Add a ref to track items being cloned
const cloningItems = ref<Record<string, boolean>>({})

// Create a skeleton array for loading state
const currentVisibleRowCount = ref(0);

// Updated skeletonItems to preserve row count
const skeletonItems = computed(() => {
  if (!isLoading.value) return [];
  
  // Use the previously known row count or fallback to a small default
  // Only show the actual number of items that were visible previously
  const count = currentVisibleRowCount.value || Math.min(3, rowsPerPage.value);
  
  // Return an array of empty objects with unique IDs for skeleton placeholders
  return Array.from({ length: count }).map((_, index) => ({
    id: `skeleton-${index}`,
    _isSkeleton: true
  }));
})

// Computed property to determine what data to show in the table
const displayItems = computed(() => {
  if (isLoading.value) return skeletonItems.value;
  return tableItems.value;
})

// Before starting search/filter operations, capture the current visible row count
const captureCurrentRowCount = () => {
  currentVisibleRowCount.value = tableItems.value.length;
}

// Handler for when filters component emits a search event
const handleFilterSearch = (apiParams: any) => {
  // Capture current row count before loading starts
  captureCurrentRowCount();
  
  // Set loading state
  store.loadingContent = true;
  
  // Call the API
  store.fetchKnowledgeListing(
    {
      rootParentId: null,
      parentId: null
    }, 
    apiParams
  )
    .then(() => {
      tableKey.value++; // Force table re-render
      
      // Update selection to match new items
      if (selectedItems.value.length > 0) {
        const newSelection = selectedItems.value.filter(selected => 
          currentList.value.some(item => item.id === selected.id)
        );
        selectedItems.value = newSelection;
      }
    })
    .catch(err => {
      console.error('Error performing search:', err);
      toast.add({
        severity: 'error', 
        summary: 'Search Error',
        detail: `Failed to search: ${err.message || 'Unknown error'}`,
        life: 5000
      });
    });
};

// Handler for when filters component emits a reset event
const handleFilterReset = () => {
  // Capture current row count before loading starts
  captureCurrentRowCount();
  
  // Reset to first page when clearing filters
  currentPage.value = 1;
  store.loadingContent = true;
  
  const apiParams: any = {
    page: 1,
    start: 0,
    limit: rowsPerPage.value,
    filter: [
      { property: "type", value: 0 },
      { property: "id", value: "_no_filter_" },
      { property: "parent_id", value: null },
      { property: "root_parent_id", value: null },
      { property: "partner_ids", operator: "intersect_set", value: null },
      { property: "visibility", operator: "=", value: null },
      { property: "status", operator: "=", value: null },
      { property: "owner_partner_id", value: "_no_filter_" },
      { property: "dict_id", value: null },
      { property: "tag_ids", value: null }
    ]
  };
  
  // Call the API
  store.fetchKnowledgeListing(
    {
      rootParentId: null,
      parentId: null
    }, 
    apiParams
  )
    .then(() => {
      tableKey.value++; // Force table re-render
    })
    .catch(err => {
      console.error('Error resetting filters:', err);
      toast.add({
        severity: 'error', 
        summary: 'Error',
        detail: `Failed to reset filters: ${err.message || 'Unknown error'}`,
        life: 5000
      });
    });
};

watch(() => currentList.value, (newItems) => {
  // Force table re-render
  tableKey.value++
}, { deep: true })

watch(() => isLoading.value, (loading) => {
  // Loading state changed
})

const handleTitleClick = (item: any, event?: MouseEvent) => {
  // Allow default behavior for middle mouse button (button 1) and Ctrl+click
  if (event && (event.button === 1 || event.ctrlKey || event.metaKey)) {
    // For middle mouse button or Ctrl/Cmd+click, let the browser handle it naturally
    // The href attribute will ensure it opens the correct URL
    return;
  }
  
  // For regular left clicks, prevent default and use router navigation
  if (event) {
    event.preventDefault();
  }
  router.push(`/knowledge/articles/${item.id}`);
}

const sortField = ref<string>('')
const sortOrder = ref<number>(1)

// Handle sorting changes
const onSort = (event: DataTableSortEvent) => {
  // Only update if there's actually a change - this prevents unnecessary API calls
  const newSortField = event.sortField as string;
  const newSortOrder = event.sortOrder as number;
  
  // Don't do anything if the sort hasn't actually changed
  if (sortField.value === newSortField && sortOrder.value === newSortOrder) {
    return;
  }
  
  // Update sort state
  sortField.value = newSortField;
  sortOrder.value = newSortOrder;
  
  // Capture current row count before loading starts
  captureCurrentRowCount();
  
  // Reset to first page when sorting
  currentPage.value = 1;
  
  // Set loading state
  store.loadingContent = true;
  
  // The search will be triggered by the watcher in KnowledgeFilters component
}

const menu = ref();
const selectedRecord = ref<any>(null);

const editRecord = (record: any) => {
    router.push({
        path: `/knowledge/articles/${record.id}`,
        query: { mode: 'edit' }
    });
};

const deleteRecord = (record: any) => {
    selectedArticle.value = {
        id: record.id,
        title: stripHtmlAndDecodeEntities(record.title)
    };
    
    nextTick(() => {
        deleteArticleRef.value.showConfirmation();
    });
};

const archiveRecord = (record: any) => {
    selectedArticle.value = {
        id: record.id,
        title: stripHtmlAndDecodeEntities(record.title)
    };
    
    nextTick(() => {
        archiveArticleRef.value.showConfirmation();
    });
};

const unarchiveRecord = (record: any) => {
    // Show confirmation dialog first
    confirm.require({
        message: 'Are you sure you want to unarchive?',
        header: 'Unarchive?',
        icon: 'pi pi-exclamation-triangle',
        acceptClass: 'p-button-primary',
        rejectClass: 'p-button-secondary',
        acceptLabel: 'Yes',
        rejectLabel: 'No',
        accept: async () => {
            // Proceed with unarchive after confirmation
            try {
                const updatedArticle = {
                    id: record.id,
                    _unarchive: true,
                    status: 1,
                };

                const response = await knowledgeAPI.unarchiveArticles([updatedArticle]);

                if (response.success) {
                    toast.add({
                        severity: 'success',
                        summary: t('knowledge.unarchive'),
                        detail: t('common.success'),
                        life: 3000,
                    });

                    // Force refresh the table
                    tableKey.value++;

                    // table not getting refreshed, so we need to reload the data
                    await loadTableData();
                } else {
                    toast.add({
                        severity: 'error',
                        summary: t('knowledge.unarchive'),
                        detail: response.message || t('common.error'),
                        life: 5000,
                    });
                }
            } catch (error) {
                console.error('Error unarchiving article:', error);
                toast.add({
                    severity: 'error',
                    summary: t('knowledge.unarchive'),
                    detail: t('common.error'),
                    life: 5000,
                });
            }
        },
        reject: () => {
            // User cancelled the operation - do nothing
        },
    });
};

const cloneRecord = (record: any) => {
    const articleId = record.id;
    const articleTitle = stripHtmlAndDecodeEntities(record.title);
    
    // Set cloning state for this specific item
    cloningItems.value[articleId] = true;
    
    // Call the clone method from the store
    store.cloneArticle(articleId)
        .then((response) => {
            let newArticleId = null;
            
            // Extract the ID from the response using various methods
            if (response.id) {
                newArticleId = response.id;
            } else if (response.kb_ids && Array.isArray(response.kb_ids) && response.kb_ids.length > 0) {
                newArticleId = response.kb_ids[0];
            } else if (response.message && typeof response.message === 'string' && response.message.includes('id=')) {
                const match = response.message.match(/id=([^&\s]+)/);
                if (match && match[1]) {
                    newArticleId = match[1];
                }
            } 
            
            // If still no ID, try deep search
            if (!newArticleId) {
                newArticleId = findIdInObject(response);
            }
            
            // Navigate to the new article in edit mode if we have an ID
            if (newArticleId) {
                router.push({
                    path: `/knowledge/articles/${newArticleId}`,
                    query: { mode: 'edit' }
                });
            } else {
                tableKey.value++; // Force table re-render
                delete cloningItems.value[articleId];
            }
        })
        .catch((error) => {
            console.error('Error cloning article:', error);
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: `Failed to clone article: ${error.message || 'Unknown error'}`,
                life: 5000
            });
            delete cloningItems.value[articleId];
        });
};

const mergeRecord = (record: any) => {
    // Implement merge functionality
    // Add your merge implementation here
};

const menuItems = computed(() => {
    const items: MenuItem[] = [];
    
    // Only add menu items if we have a selected record with _uiAccess
    if (selectedRecord.value?._uiAccess) {
        const { edit, delete: canDelete, clone, merge } = selectedRecord.value._uiAccess;

        // 1. Edit
        if (edit) {
            items.push({
                label: t('common.edit'),
                icon: 'pi pi-pencil',
                command: () => {
                    editRecord(selectedRecord.value);
                }
            });
        }

        // 2. Clone
        if (clone) {
            items.push({
                label: t('common.clone'),
                icon: 'pi pi-clone',
                command: () => {
                    cloneRecord(selectedRecord.value);
                }
            });
        }

        // 3. Archive/Unarchive
        if (canDelete) {
          const isArchived = selectedRecord.value.isArchived === true;
          items.push({
              label: isArchived ? t('knowledge.unarchive') : t('knowledge.actionsMenu.archive'),
              icon: isArchived ? 'pi pi-undo' : 'pi pi-inbox',
              command: () => {
                  if (isArchived) {
                      unarchiveRecord(selectedRecord.value);
                  } else {
                      archiveRecord(selectedRecord.value);
                  }
              }
          });
        }

        // 4. Delete
        if (canDelete) {
            items.push({
                label: t('common.delete'),
                icon: 'pi pi-trash',
                command: () => {
                    deleteRecord(selectedRecord.value);
                }
            });
        }

        if (merge) {
            items.push({
                label: t('common.merge'),
                icon: 'pi pi-objects-column',
                command: () => {
                    mergeRecord(selectedRecord.value);
                }
            });
        }
    }

    return items;
});

const toggleMenu = (event: Event, record: any) => {
  selectedRecord.value = record;
  nextTick(() => {
    if (menuItems.value.length) {
      selectedRecord.value = record;
      menu.value.toggle(event);
    }
  });
};

const showAddArticleModal = () => {
  addArticleRef.value.showModal()
}

const showAddLabelModal = () => {
  addLabelRef.value.showModal()
}

// Update the onPage function to update URL params
const onPage = (event: any) => {
  // Debounce the page event to prevent duplicate calls
  if (pageEventTimeout.value !== null) {
    clearTimeout(pageEventTimeout.value);
  }
  
  pageEventTimeout.value = window.setTimeout(() => {
    // Update local state
    const newPage = event.page + 1; // PrimeVue is 0-based, our API is 1-based
    const newRowsPerPage = event.rows;
  
  // Capture current row count before loading starts
  captureCurrentRowCount();
  
    // Set loading state
  store.loadingContent = true;
  
    // Update the state values after logging to ensure consistent values in logs
    currentPage.value = newPage;
    rowsPerPage.value = newRowsPerPage;
    
    pageEventTimeout.value = null;
    // The search will be triggered by the watcher in KnowledgeFilters component
  }, 10);
}

const loadTableData = async () => {
  // Check for pagination and sorting in URL first
  if (route.query.page) {
      currentPage.value = parseInt(route.query.page as string) || 1;
    }
    
    if (route.query.limit) {
      rowsPerPage.value = parseInt(route.query.limit as string) || 25;
    }
    
    if (route.query.sortField) {
      sortField.value = route.query.sortField as string;
      sortOrder.value = parseInt(route.query.sortOrder as string) || 1;
    }
    
    // If we have params from URL, use the filters component to perform search
    if (route.query.q || route.query.status || route.query.access || route.query.library || route.query.label ||
        route.query.page || route.query.limit || route.query.sortField) {
      
      if (filtersRef.value) {
        // Filters component will load URL params automatically
        filtersRef.value.performSearch();
      }
      return; // Skip the default initialization since we're using URL params
    }
    
    // Default initialization if no URL params are present
    const apiParams: any = {
      page: 1,
      start: 0,
      limit: rowsPerPage.value,
      filter: [
        { property: "type", value: store.selectedContentType === 'templates' ? 1 : 0 },
        { property: "id", value: "_no_filter_" },
        { property: "parent_id", value: null },
        { property: "root_parent_id", value: null },
        { property: "partner_ids", operator: "intersect_set", value: null },
        { property: "visibility", operator: "=", value: null },
        { property: "status", operator: "=", value: null },
        { property: "owner_partner_id", value: "_no_filter_" },
        { property: "dict_id", value: null },
        { property: "tag_ids", value: null }
      ]
    };
    
    // Add status filter if it's not 'all'
    if (store.statusFilter && store.statusFilter !== 'all') {
      // Find and update the status filter
      const statusIndex = apiParams.filter.findIndex((f: any) => f.property === "status");
      if (statusIndex >= 0) {
        // Ensure we're passing a string value
        const statusValue = typeof store.statusFilter === 'number'
          ? String(store.statusFilter)
          : store.statusFilter;
        apiParams.filter[statusIndex].value = statusValue;
      }
    }
    
    await store.fetchKnowledgeListing(
      {
        rootParentId: null,
        parentId: null
      },
      apiParams
    );
}

// Initialize component
onMounted(async () => {
  try {
    // Set loading state immediately to show skeleton while initializing
    store.loadingContent = true;
    
    // Initialize the label tree and library data for filters
    await Promise.all([
      store.fetchLabelTree(),
      store.fetchLibrariesOnly()
    ]);
    
    await loadTableData();
    
    // Fetch comment count on mount
    await store.fetchComments({ limit: 1 }); // Only need count
  } catch (error) {
    console.error('Error loading initial knowledge listing:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load knowledge articles',
      life: 5000
    });
  }
});

// Cleanup when component is unmounted
onUnmounted(() => {
  store.clearCurrentList()
})

// Track selected articles
const allCurrentPageSelected = computed(() => {
  return selectedItems.value.length > 0 && selectedItems.value.length === tableItems.value.length
})

// Handle selection changes
const onSelectionChange = (selection: any[]) => {
  selectedItems.value = selection
}

// Watch for status filter changes
watch(() => store.statusFilter, (newStatus) => {
  // Reset to first page when changing filters
  currentPage.value = 1;
  
  // Update filters in the filters component
  if (filtersRef.value) {
    filtersRef.value.performSearch();
  }
});

// Watch for sidebar selection changes
watch(() => store.sidebarSelectionChanged, (changed) => {
  if (changed && filtersRef.value) {
    // Update both filters at once with a single search call
    const libraryId = store.selectedLibraryId || null;
    const labelId = store.selectedLabelId || null;
    
    filtersRef.value.updateLibraryAndLabelFilters(libraryId, labelId);
    
    // Reset the flag
    store.sidebarSelectionChanged = false;
  }
});

// Watch for changes in library and label filter values to update sidebar selection
watch(() => [filtersRef.value?.getLibraryValues(), filtersRef.value?.getLabelValues()], ([libraryValues, labelValues]) => {
  if (filtersRef.value) {
    // Extract single values from arrays (for backward compatibility with the exposed methods)
    const currentLibraryId = libraryValues && libraryValues.length > 0 ? libraryValues[0] : null;
    const currentLabelId = labelValues && labelValues.length > 0 ? labelValues[0] : null;
    
    // Update store selection if different from current
    if (store.selectedLibraryId !== currentLibraryId || store.selectedLabelId !== currentLabelId) {
      store.setSelectedLibraryId(currentLibraryId);
      store.setSelectedLabelId(currentLabelId);
      
      // Update sidebar selection to match filters
      if (currentLibraryId || currentLabelId) {
        const nodeKeyToSelect = currentLabelId || currentLibraryId;
        if (nodeKeyToSelect) {
          store.selectedNodeKey = { [nodeKeyToSelect]: true };
        }
      } else {
        store.selectedNodeKey = {};
      }
    }
  }
}, { deep: true });

// Register Tooltip directive
const vTooltip = Tooltip;

// Refs for the new modals
const deleteArticleRef = ref();
const archiveArticleRef = ref();

// Selected article for operations
const selectedArticle = ref<{ id: string, title: string } | null>(null);

// Handle successful article deletion
const handleArticleDeleted = (articleId: string) => {
  // Force refresh the table
  tableKey.value++;
  
  // Clear the selected article
  selectedArticle.value = null;
}

// Handle successful article archiving
const handleArticleArchived = (articleId: string) => {
  // Force refresh the table
  tableKey.value++;
  
  // Clear the selected article
  selectedArticle.value = null;
}

// Handle errors in operations
const handleOperationError = (error: any) => {
  console.error('Operation error:', error);
  // Clear the selected article
  selectedArticle.value = null;
}

// Add a computed property to check if we should show the zero state
const showZeroState = computed(() => {
  // Show zero state only when:
  // 1. Not loading
  // 2. There are no items in the list
  // 3. There are no active filters (check with filtersRef)
  
  if (isLoading.value) return false;
  if (tableItems.value.length > 0) return false;
  
  // Check if any filters are applied by getting data from the filters component
  let hasActiveFilters = false;
  
  if (filtersRef.value) {
    const activeFilters = filtersRef.value.getActiveFilters();
    const searchInputValue = filtersRef.value.getSearchInputValue();
    
    hasActiveFilters = (activeFilters && activeFilters.length > 0) || 
                      (searchInputValue && searchInputValue.trim() !== '');
  }
  
  return !hasActiveFilters && totalItemsCount.value === 0;
});

// Add drawer visibility ref
const commentsDrawerVisible = ref(false);

// Toggle drawer method
const toggleCommentsDrawer = async () => {
  commentsDrawerVisible.value = !commentsDrawerVisible.value;
  if (commentsDrawerVisible.value) {
    await store.fetchComments(); // Load comments when opening
  }
};

const contentTypeLabel = computed(() => {
  if (store.selectedContentType === 'templates') {
    return t('knowledge.templates') || 'templates';
  }
  return t('knowledge.articles') || 'articles';
});

// Add in the setup section, near other composable initializations
const { can } = usePermissions();

// Function to determine article status based on new logic
const getArticleStatus = (data: any) => {
  if (!data) return 'unpublished';
  
  // Check for deleted status first (using 'closed' as the closest valid state)
  if (data.status === 99) {
    return 'closed';
  }
  
  // Check for archived status
  if (data.isArchived === true) {
    return 'archived';
  }
  
  // Check published/unpublished status
  if (data.isPublished === true) {
    return 'published';
  } else if (data.isPublished === false) {
    // If not published, check if it's a draft
    if (data.isDraft === true) {
      return 'draft';
    }
    return 'unpublished';
  }
  
  // Fallback to unpublished if none of the conditions match
  return 'unpublished';
};

</script>

<template>
  <div class="knowledge-list">
    
    <!-- Add ConfirmDialog component for delete confirmation -->
    <BravoConfirmDialog />
    
    <div class="header">
      <BravoTitlePage>{{ store.selectedContentType === 'templates' ? t('knowledge.templates') : t('knowledge.articles') }}</BravoTitlePage>
      <div class="button-group">
        <template v-if="store.selectedContentType === 'articles'">
          <div style="position: relative; display: inline-block;">
            <BravoButton 
              icon="pi pi-comments" 
              severity="secondary" 
              aria-label="Comments"
              v-tooltip.bottom="{ value: t('knowledge.comments'), showDelay: 400 }"
              @click="toggleCommentsDrawer"
              class="comments-button" />
            <BravoBadge v-if="totalCommentsCount > 0" :value="totalCommentsCount" severity="primary" style="position: absolute; top: -10px; right: -10px;" />
          </div>
          <BravoButton v-if="can.addArticle()" :label="t('knowledge.new_label')" icon="pi pi-tag" severity="secondary" @click="showAddLabelModal" />
          <BravoButton v-if="can.addArticle()" :label="t('knowledge.new_article')" icon="pi pi-plus" @click="showAddArticleModal" />
        </template>
        <template v-else-if="store.selectedContentType === 'templates'">
          <BravoButton :label="t('knowledge.new_template')" icon="pi pi-plus" @click="showAddArticleModal" />
        </template>
      </div>
    </div>
    
    <!-- Filters and Actions Row -->
    <div class="filters-actions-row mb-4">
      <KnowledgeFilters 
        ref="filtersRef"
        :isLoading="isLoading"
        :currentPage="currentPage"
        :rowsPerPage="rowsPerPage"
        :sortField="sortField"
        :sortOrder="sortOrder"
        @search="handleFilterSearch"
        @reset="handleFilterReset"
        class="flex-grow-1"
      />
      
      <!-- Actions Button -->
      <BravoButton 
        v-if="selectedItems.length > 0"
        :label="t('knowledge.actions')" 
        icon="pi pi-check-square"
        severity="secondary"
        @click="showActionsMenu" 
        class="actions-button ml-3"
        :disabled="isLoading"
      />
      
      <!-- Actions Menu -->
      <BravoMenu 
        v-if="actionsMenuItems.length" 
        ref="actionsMenu" 
        :model="actionsMenuItems" 
        :popup="true" 
      />
    </div>

    <!-- Show zero state when applicable, otherwise show the table -->
    <KnowledgeListZeroState 
      v-if="showZeroState"
      :onAddArticle="showAddArticleModal"
      :selectedContentType="store.selectedContentType"
      class="flex-grow-1"
      style="min-height: calc(100vh - 230px); display: flex; align-items: center; justify-content: center;"
    />
    
    <!-- Article List Table - hide when showing zero state -->
    <BravoDataTable 
      v-if="!showZeroState"
      :key="tableKey"
      :value="displayItems"
      :columns="[]" 
      dataKey="id"
      :rowHover="true"
      :stripedRows="false"
      :showGridlines="false" 
      class="p-datatable-sm skeleton-table-container" 
      v-model:selection="selectedItems"
      @selection-change="onSelectionChange"
      :sortField="sortField"
      :sortOrder="sortOrder"
      @sort="onSort"
      removableSort
      :loading="false"
      :rows="rowsPerPage"
      :totalRecords="totalItemsCount"
      :paginator="true"
      :lazy="true"
      @page="onPage"
      :first="(currentPage - 1) * rowsPerPage"
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      :rowsPerPageOptions="[10, 25, 50]"
      scrollable
      scrollHeight="calc(100vh - 230px)"
      paginatorPosition="bottom"
      paginatorClassName="fixed-paginator"
    >
      <template #header>
        <div>
          <span>
            <span v-if="selectedItems.length > 0">
              {{ selectedItems.length }} of {{ currentListCount }} {{ t('knowledge.articles_selected') }}
            </span>
            <span v-else>
              <template v-if="totalItemsCount === 0 && currentListCount > 0">
                {{ t('knowledge.showing') }} {{ currentListCount }} {{ contentTypeLabel }}
              </template>
              <template v-else>
                {{ t('knowledge.showing') }} {{ currentListCount }} of {{ totalItemsCount }} {{ contentTypeLabel }}
              </template>
            </span>
          </span>
        </div>
      </template>
      <template #empty>
        <div>
          <div v-if="isLoading">
            <!-- Show a skeleton loader in the empty state when loading -->
            <div class="skeleton-table">
              <div class="skeleton-row" v-for="i in 5" :key="i">
                <Skeleton width="3rem" height="1.25rem" class="skeleton-first-col" />
                <Skeleton width="40%" height="1.25rem" />
                <Skeleton width="10%" height="1.25rem" />
                <Skeleton width="10%" height="1.25rem" />
                <Skeleton width="8%" height="1.25rem" />
                <Skeleton width="15%" height="1.25rem" />
                <Skeleton width="3rem" height="1.25rem" class="skeleton-last-col" />
              </div>
            </div>
          </div>
          <div v-else-if="totalItemsCount === 0">No content available</div>
          <div v-else>No matching content found</div>
        </div>
      </template>
      
      <Column selectionMode="multiple" headerStyle="width: 3rem" />
      
      <Column field="title" :header="t('knowledge.title')" sortable style="min-width: 300px">
        <template #body="{ data }">
          <div v-if="data?._isSkeleton" class="flex align-items-center">
            <Skeleton width="80%" height="1.25rem" />
          </div>
          <a 
            v-else
            :href="`/knowledge/articles/${data.id}`"
            class="article-title" 
            @click="(event) => handleTitleClick(data, event)"
            @mousedown="(event) => handleTitleClick(data, event)"
          >
            {{ stripHtmlAndDecodeEntities(data?.title) || 'Untitled' }}
          </a>
        </template>
      </Column>
      
      <Column field="status" :header="t('knowledge.status')" sortable style="min-width: 80px">
        <template #body="{ data }">
          <div v-if="data?._isSkeleton" class="flex align-items-center">
            <Skeleton width="80%" height="1.25rem" />
          </div>
          <div v-else class="flex align-items-center">
            <BravoTag
              :value="t(`knowledge.${getArticleStatus(data)}`)" 
              :state="getArticleStatus(data)" />
            <BravoTag v-if="data?.has_latest_draft && getArticleStatus(data) !== 'draft'" 
              :value="t('knowledge.draft')" 
              :state="'draft'"
              style="margin-left: 0.5rem;" />
          </div>
        </template>
      </Column>
      
      <Column field="access" :header="t('knowledge.access')" sortable style="min-width: 40px">
        <template #body="{ data }">
          <div v-if="data?._isSkeleton" class="flex align-items-center">
            <Skeleton width="70%" height="1.25rem" />
          </div>
          <span v-else>{{ t(`knowledge.${data?.c__d_visibility?.toLowerCase() || 'unknown'}`) }}</span>
        </template>
      </Column>
      
      <Column field="access_cnt" :header="t('knowledge.views')" sortable style="min-width: 80px">
        <template #body="{ data }">
          <div v-if="data?._isSkeleton" class="flex align-items-center">
            <Skeleton width="50%" height="1.25rem" />
          </div>
          <span v-else>{{ data?.access_cnt || 0 }}</span>
        </template>
      </Column>
      
      <Column field="updated" :header="t('knowledge.last_updated')" sortable style="min-width: 70px">
        <template #body="{ data }">
          <div v-if="data?._isSkeleton" class="flex align-items-center">
            <Skeleton width="90%" height="1.25rem" />
          </div>
          <template v-else>
            <BravoTimestamp 
              v-if="data?.updated" 
              :datetime="toISOString(data.updated)" 
            />
            <span v-else>N/A</span>
          </template>
        </template>
      </Column>
      
      <Column :exportable="false" style="width: 4rem">
        <template #body="{ data }">
          <div class="flex align-items-center justify-content-center">
            <div v-if="data?._isSkeleton" class="flex align-items-center justify-content-center">
              <Skeleton shape="square" size="2rem" />
            </div>
            <template v-else>
              <BravoButton
                v-if="!cloningItems[data.id]"
                icon="pi pi-ellipsis-v"
                text
                severity="secondary"
                class="table-action-button"
                @click="(e) => toggleMenu(e, data)"
                aria-label="Article actions"
                v-tooltip.bottom="{ value: t('knowledge.actions'), showDelay: 400 }"
              />
              <i v-else class="pi pi-spin pi-spinner" style="font-size: 1.25rem; color: var(--primary-color);" aria-hidden="true"></i>
            </template>
          </div>
        </template>
      </Column>
    </BravoDataTable>
    <BravoMenu v-if="menuItems.length" ref="menu" :model="menuItems" :popup="true" />
    <CommentsDrawer v-model:visible="commentsDrawerVisible" />
    <AddArticleModal ref="addArticleRef" />
    <AddLabelModal ref="addLabelRef" />
    <DeleteArticleModal 
      ref="deleteArticleRef" 
      :articleId="selectedArticle?.id"
      :articleTitle="selectedArticle?.title"
      @deleted="handleArticleDeleted" 
      @error="handleOperationError" 
    />
    <ArchiveArticleModal 
      ref="archiveArticleRef" 
      :articleId="selectedArticle?.id"
      :articleTitle="selectedArticle?.title"
      @archived="handleArticleArchived" 
      @error="handleOperationError" 
    />
    
    <!-- Bulk Action Dialogs -->
    <UpdateLabelsDialog
      v-model:visible="showLabelsDialog"
      :selectedArticles="selectedItems"
      @save="handleLabelUpdate"
    />

    <UpdateAccessControlsDialog
      v-model:visible="showAccessControlsDialog"
      :selectedArticles="selectedItems"
      @save="handleAccessControlsUpdate"
    />

    <UpdateProductsDialog
      v-model:visible="showProductsDialog"
      :selectedArticles="selectedItems"
      @save="handleProductsUpdate"
    />

    <UpdateArticlesTagDialog
      v-model:visible="showTagsDialog"
      :selectedArticles="selectedItems"
      @save="handleTagsUpdate"
    />

    <ShareArticlesDialog
      v-model:visible="showShareDialog"
      :selectedArticlesCount="selectedItems.length"
      :selectedArticles="selectedItems"
      @share="handleShareArticles"
    />

    <UnshareArticlesDialog
      v-model:visible="showUnshareDialog"
      :selectedArticlesCount="selectedItems.length"
      :selectedArticles="selectedItems"
      @unshare="handleUnshareArticles"
    />

    <PublishArticlesDialog
      v-model:visible="showPublishDialog"
      :selectedArticlesCount="selectedItems.length"
      :selectedArticles="selectedItems"
      @publish="handlePublishArticles"
    />

    <UnpublishArticlesDialog
      v-model:visible="showUnpublishDialog"
      :selectedArticlesCount="selectedItems.length"
      :selectedArticles="selectedItems"
      @unpublish="handleUnpublishArticles"
    />

    <ArchiveArticlesDialog
      v-model:visible="showArchiveDialog"
      :selectedArticlesCount="selectedItems.length"
      :selectedArticles="selectedItems"
      @archive="handleArchiveArticles"
    />
  </div>
</template>

<style scoped>
.knowledge-list {
  padding: 1rem 2rem 0rem 2rem;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  /* Use max-height here to avoid any overflows */
  max-height: 100vh;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  align-items: center;
  flex: 0 0 auto;
}

.button-group {
  display: flex;
  gap: .75rem;
}

.filters-actions-row {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex-wrap: wrap;
  width: 100%;
}

.actions-button {
  flex-shrink: 0;
}

.search-row {
  margin-bottom: 1.5rem;
  flex: 0 0 auto;
}

/* Make the data table take up available space and paginator fixed at bottom */
:deep(.p-datatable) {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: calc(100vh - 230px);
  position: relative; /* Add position relative to contain the absolute paginator */
}

:deep(.p-datatable-wrapper) {
  flex-grow: 1;
  overflow: auto;
  min-height: 200px; /* Ensure a minimum height even with few results */
  padding-bottom: 56px; /* Add padding at the bottom to make space for the fixed paginator */
}

:deep(.p-paginator) {
  position: absolute; /* Change to absolute positioning */
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--surface-a);
  z-index: 1;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  width: 100%;
  align-items: center;
}

.search-input {
  padding-right: 2.5rem;
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
}

:deep(.p-menu) {
  min-width: 12rem;
}

.article-title {
  color: inherit;
  text-decoration: none;
  
  &:hover {
    color: var(--blue-650);
    text-decoration: underline;
  }
}


.selected-count {
  display: flex;
  align-items: center;
  background-color: var(--surface-200);
  padding: 0.3rem 0.5rem;
  border-radius: 0.375rem;
  border-left: 3px solid var(--primary-color);
}

.selected-badge {
  color: var(--primary-color);
  font-weight: 600;
  margin-right: 0.5rem;
}

:deep(.p-datatable-table) {
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0 !important; /* Ensure no extra margin at the bottom */
}

:deep(.p-datatable-tbody) {
  min-height: 50px; /* Ensure there's always some space even with no data */
}

:deep(.p-datatable-tbody > tr.p-highlight) {
  background-color: var(--primary-50) !important;
  color: var(--primary-700) !important;
}

:deep(.p-datatable-tbody > tr.p-highlight:hover) {
  background-color: var(--primary-100) !important;
}

:deep(.p-checkbox .p-checkbox-box.p-highlight) {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

/* Skeleton loader styles for table */
.skeleton-table-container :deep(.p-datatable-tbody > tr > td:first-child) {
  position: relative;
}

.skeleton-table-container :deep(tr[data-id^="skeleton-"]) .p-checkbox {
  visibility: hidden;
}

.skeleton-table-container :deep(tr[data-id^="skeleton-"]) td:first-child::before {
  content: "";
  position: absolute;
  left: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 50%;
  background-color: var(--surface-200);
}

/* Improve alignment of skeleton cells */
.skeleton-table-container :deep(td) {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  height: 3rem !important;
}

.skeleton-table-container :deep(tr[data-id^="skeleton-"] td) {
  vertical-align: middle !important;
}

/* Target the specific div containers holding skeletons */
.skeleton-table-container :deep(.flex.align-items-center) {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
}

/* Update the actions column skeleton to remain centered */
.skeleton-table-container :deep(td:last-child .flex.align-items-center.justify-content-center) {
  justify-content: center !important;
}

/* Keep the original style for all container selectors to avoid breaking anything */
.skeleton-table-container :deep(.flex.align-items-center.justify-content-center) {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.delete-selected-container {
  margin-left: auto;
}

:deep(.p-datatable .p-datatable-header) {
  border: none !important;
}  

/* Add skeleton table styles */
.skeleton-table {
  width: 100%;
  padding: 0 1rem; /* Match the padding used in table cells */
}

.skeleton-row {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Align content to the left like the real table */
  padding: 0.75rem 0; /* Remove horizontal padding since we added it to the container */
  border-bottom: 1px solid var(--surface-200);
  height: 3.5rem;
  gap: 1rem;
}

.skeleton-row :deep(.p-skeleton) {
  margin: 0;
  display: block;
}

/* Set specific widths to match column structure better */
.skeleton-row :deep(.p-skeleton:nth-child(1)) {
  flex: 0 0 3rem;
  margin-left: -0.5rem; /* Reduce the left margin to align better with column headers */
}

.skeleton-row :deep(.skeleton-first-col) {
  margin-left: -0.5rem; /* Match the adjustment for table rows */
}

.skeleton-row :deep(.skeleton-last-col) {
  margin-left: auto; /* Push to the right */
  margin-right: 0.5rem; /* Add a bit of margin on the right */
}

.skeleton-row :deep(.p-skeleton:nth-child(2)) {
  flex: 1 1 40%;
  margin-left: 0.5rem; /* Add a bit more space after the first column */
}

.skeleton-row :deep(.p-skeleton:nth-child(3)),
.skeleton-row :deep(.p-skeleton:nth-child(4)) {
  flex: 0 0 10%;
}

.skeleton-row :deep(.p-skeleton:nth-child(5)) {
  flex: 0 0 8%;
}

.skeleton-row :deep(.p-skeleton:nth-child(6)) {
  flex: 0 0 15%;
}

.skeleton-row :deep(.p-skeleton:nth-child(7)) {
  flex: 0 0 3rem;
}

.filters-panel {
  min-width: 350px;
}

.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-200);
}

.filter-panel-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.filter-types {
  margin-bottom: 1.5rem;
}

.filter-types h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.filter-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.active-filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.filter-item {
  border: 1px solid var(--surface-200);
  border-radius: 0.375rem;
  padding: 0.75rem;
  background-color: var(--surface-50);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.filter-header h4 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
}

.filter-actions {
  padding-top: 0.5rem;
  border-top: 1px solid var(--surface-200);
}

/* Make the overlay panel larger and better styled */
:deep(.p-overlaypanel) {
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Make the filter button indicate state */
.filter-button.p-button {
  position: relative;
}

.filter-button.p-button:has(.p-badge) {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
}

.active-filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.active-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.clear-filters-btn {
  margin-left: auto;
}

.skeleton-table-container {
  min-height: calc(100vh - 230px); /* Match the DataTable min-height */
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Set a min-height for the empty state as well */
.skeleton-table-container :deep(.p-datatable-emptymessage) {
  min-height: 200px; /* Ensure minimum height for empty state */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 56px; /* Make space for the fixed paginator */
}

/* Make table wrapper take up available space */
.skeleton-table-container :deep(.p-datatable-wrapper) {
  flex: 1;
}

/* Add skeleton table styles */
.skeleton-table {
  width: 100%;
  min-height: inherit;
}

.p-button-relative {
  position: relative;
}

:deep(.p-button-relative .bravo-badge) {
  position: absolute;
  top: -5px;
  right: -5px;
}

.table-action-button {
  width: 2rem !important;
  height: 2rem !important;
  padding: 0.5rem !important;
}

/* Add custom class for the fixed paginator */
:deep(.fixed-paginator) {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  margin: 0 !important;
}

</style>
