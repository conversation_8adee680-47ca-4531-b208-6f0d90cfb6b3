<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { storeToRefs } from 'pinia';
import BravoDrawer from '@services/ui-component-library/components/BravoDrawer.vue';
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoComment from '@services/ui-component-library/components/BravoComment.vue';
import { useI18n } from 'vue-i18n';
import { useToast } from 'primevue/usetoast';
import { usePermissions } from '@/composables/usePermissions';

const { can } = usePermissions();

const { t } = useI18n();
const toast = useToast();
const store = useKnowledgeStore();
const { comments, loadingComments, totalCommentsCount } = storeToRefs(store);

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['update:visible']);

const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// Load comments when drawer becomes visible
watch(() => props.visible, async (newValue) => {
  if (newValue) {
    try {
      await store.fetchComments();
    } catch (error) {
      toast.add({
        severity: 'error',
        summary: t('knowledge.error'),
        detail: t('knowledge.failed_to_load_comments'),
        life: 3000
      });
    }
  }
});

const handleResolve = async (commentId: string) => {
  try {
    await store.resolveComment(commentId);
    toast.add({
      severity: 'success',
      summary: t('common.success'),
      detail: t('knowledge.comment_resolved'),
      life: 2000
    });
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: t('knowledge.error'),
      detail: t('knowledge.failed_to_resolve_comment'),
      life: 3000
    });
  }
};
</script>

<template>
  <BravoDrawer
    :visible="visible"
    @update:visible="updateVisible"
    position="right"
    :style="{ width: '450px' }"
    :modal="true"
    :dismissable="true"
  >
    <template #header>
      <div>
        <BravoTitlePage>{{ t('knowledge.comments') }}</BravoTitlePage>
      </div>
    </template>
    <div class="p-3">
      <div v-if="loadingComments" class="loading-comments">
        <div class="skeleton-card" v-for="n in 2" :key="n">
          <div class="skeleton-comment">
            <div class="skeleton-avatar"></div>
            <div class="skeleton-content">
              <div class="skeleton-line skeleton-name"></div>
              <div class="skeleton-line skeleton-body"></div>
              <div class="skeleton-line skeleton-body short"></div>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="comments.length === 0" class="no-comments">
        {{ t('knowledge.no_comments') }}
      </div>
      <div v-else class="comments-container">
        <BravoComment
          v-for="comment in comments"
          :key="comment.id"
          :commenter-name="(comment as any).c__users_full_name || (comment as any).c__users_nickname || t('knowledge.unknown_user')"
          :commenter-avatar="(comment as any).user_avatar || undefined"
          :comment-date="new Date((comment as any).created || '')"
          :article-title="(comment as any).kb_title || ''"
          :article-url="`/knowledge/articles/${(comment as any).kb_id}?tab=comments`"
          :comment-body="(comment as any).notes || (comment as any).diff_log || (comment as any).c__diff_log || ''"
          :show-resolve-button="can.editArticle() && !(comment as any).comment_resolved"
          :max-display-length="300"
          @resolve="handleResolve(comment.id)"
          class="mb-3"
        />
      </div>
    </div>
  </BravoDrawer>
</template>

<style scoped>
.comments-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.no-comments,
.loading-comments {
  text-align: center;
  color: var(--text-color-secondary);
  padding: 1rem;
}

/* Skeleton loader styles */
.skeleton-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(16, 30, 54, 0.06);
  padding: 1.25rem 1.5rem;
  margin-bottom: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.skeleton-comment {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0;
}
.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--surface-200, #eee);
  flex-shrink: 0;
}
.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.skeleton-line {
  height: 12px;
  background: var(--surface-200, #eee);
  border-radius: 4px;
  margin-bottom: 0.25rem;
  animation: skeleton-loading 1.2s infinite linear alternate;
}
.skeleton-name {
  width: 120px;
  height: 14px;
}
.skeleton-body {
  width: 90%;
}
.skeleton-body.short {
  width: 60%;
}
@keyframes skeleton-loading {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}
</style> 