<!-- KnowledgeFilters.vue -->
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoIconField from '@services/ui-component-library/components/BravoIconField.vue';
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoFilterField from './BravoFilterField.vue';
import { useI18n } from 'vue-i18n';
import { useKnowledgeStore } from '../stores/knowledge';
import { usePartnerStore } from '@/stores/partner';
import { useUsersStore } from '@/stores/users';
import { useMetaStore } from '@/stores/meta';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { onMounted } from 'vue';
import Tooltip from 'primevue/tooltip';

const props = defineProps<{
  isLoading?: boolean;
  currentPage?: number;
  rowsPerPage?: number;
  sortField?: string;
  sortOrder?: number;
}>();

const emit = defineEmits<{
  (e: 'search', params: any): void; 
  (e: 'reset'): void;
}>();

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const store = useKnowledgeStore();
const partnerStore = usePartnerStore();
const usersStore = useUsersStore();
const metaStore = useMetaStore();
const knowledgeAPI = useKnowledgeAPI();

// Search related
const searchInputValue = ref('');
const searchDelayTimeout = ref<number | null>(null);
const isUpdatingFromUrl = ref(false);

// Status and Access filter
const activeFilters = ref<string[]>([]);
const statusValues = ref<string[]>([]);
const accessValues = ref<string[]>([]);
const libraryValues = ref<string | null>(null);
const labelValues = ref<string | null>(null);
const productValues = ref<string[]>([]);
const tagValues = ref<string[]>([]);
const filterMenu = ref();

// Filter types for the dropdown
const filterTypes = [
  { label: 'Status', value: 'status', icon: 'pi pi-tag' },
  { label: 'Access', value: 'access', icon: 'pi pi-lock' },
  { label: 'Library', value: 'library', icon: 'pi pi-book' },
  { label: 'Label', value: 'label', icon: 'pi pi-tag' },
  { label: 'Tags', value: 'tags', icon: 'pi pi-tags' },
  { label: 'Products', value: 'products', icon: 'pi pi-desktop' }
];

// Helper function to truncate label text
const truncateLabel = (text: string, maxLength: number = 10): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength).trim() + '...';
};

// Helper function to truncate label path
const truncateLabelPath = (path: string): string => {
  // Split the path by ' / ' separator
  const parts = path.split(' / ');
  
  // Truncate each part to 20 characters
  const truncatedParts = parts.map(part => truncateLabel(part.trim(), 20));
  
  // Join back with ' / '
  return truncatedParts.join(' / ');
};

// Filter options
const statusOptions = [
  { label: 'Draft', value: '1' },
  { label: 'Published', value: '0' },
  { label: 'Archived', value: '98' }
];

const accessOptions = [
  { label: 'Public', value: '0' },
  { label: 'Internal', value: '1' },
  { label: 'Ecosystem', value: '2' }
];

// Library and Label options - populated from store
const libraryOptions = computed(() => {
  const options: Array<{ label: string; value: string; fullLabel: string }> = [];
  // Get libraries from tree nodes (top-level items)
  for (const node of store.treeNodes) {
    if (node.isLibrary) {
      const fullLabel = node.text || 'Untitled Library';
      const truncatedLabel = truncateLabel(fullLabel, 20);
      options.push({
        label: truncatedLabel,
        fullLabel: fullLabel, // Keep the full text for search functionality
        value: node.id
      });
    }
  }
  return options;
});

const labelOptions = computed(() => {
  const options: Array<{ label: string; value: string; fullLabel: string }> = [];
  
  if (libraryValues.value) {
    // If a library is selected, show only labels for that library
    const selectedLibrary = store.treeNodes.find(node => node.id === libraryValues.value && node.isLibrary);
    
    if (selectedLibrary && selectedLibrary.children) {
      // Get labels that belong to the selected library with full path
      const addLabelsFromNode = (node: any, parentPath: string = '') => {
        // Add this node if it's a label (not a library)
        if (!node.isLibrary && node.text) {
          const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
          const truncatedPath = truncateLabelPath(currentPath);
          options.push({
            label: truncatedPath,
            fullLabel: currentPath, // Keep the full path for search functionality
            value: node.id
          });
        }
        
        // Recursively add children labels
        if (node.children) {
          const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
          node.children.forEach((child: any) => addLabelsFromNode(child, currentPath));
        }
      };
      
      selectedLibrary.children.forEach((child: any) => addLabelsFromNode(child));
    }
  } else {
    // If no library is selected, show all labels from all libraries with full path
    const addLabelsFromNode = (node: any, parentPath: string = '') => {
      // Add this node if it's a label (not a library)
      if (!node.isLibrary && node.text) {
        const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
        const truncatedPath = truncateLabelPath(currentPath);
        options.push({
          label: truncatedPath,
          fullLabel: currentPath, // Keep the full path for search functionality
          value: node.id
        });
      }
      
      // Recursively add children labels
      if (node.children) {
        const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
        node.children.forEach((child: any) => addLabelsFromNode(child, currentPath));
      }
    };
    
    // Iterate through all libraries and their children
    store.treeNodes.forEach(libraryNode => {
      if (libraryNode.isLibrary && libraryNode.children) {
        libraryNode.children.forEach((child: any) => addLabelsFromNode(child, libraryNode.text));
      }
    });
  }
  
  // Sort options by full label for better UX (but display truncated)
  return options.sort((a, b) => a.fullLabel.localeCompare(b.fullLabel));
});

// Products options - we'll load these from the knowledge API
const productOptions = ref<Array<{ label: string; value: string }>>([]);

// Tags options - loaded from meta store (filtered to category === 3)
const tagOptions = computed(() => {
  if (!metaStore.metaData?.pl__tags) return [];
  return metaStore.metaData.pl__tags
    .filter((tag: any) => tag.category === 3)
    .map((tag: any) => ({
      label: tag.lbl,
      value: tag.id
    }));
});

// Note: hasDraft options have been removed

// Function to show filter dropdown menu
const showFilterMenu = (event: Event) => {
  filterMenu.value.toggle(event);
};

// Function to show specific filter
const addFilter = (filterType: string) => {
  if (!activeFilters.value.includes(filterType)) {
    activeFilters.value.push(filterType);
  }
  filterMenu.value.toggle();
};

// Function to hide specific filter
const removeFilter = (filterType: string) => {
  // Remove the filter type from active filters
  activeFilters.value = activeFilters.value.filter(f => f !== filterType);
  
  // Clear the corresponding filter values
  switch (filterType) {
    case 'status':
      statusValues.value = [];
      break;
    case 'access':
      accessValues.value = [];
      break;
    case 'library':
      libraryValues.value = null;
      store.setSelectedLibraryId(null);
      break;
    case 'label':
      labelValues.value = null;
      store.setSelectedLabelId(null);
      break;
    case 'products':
      productValues.value = [];
      break;
    case 'tags':
      tagValues.value = [];
      break;
  }
  
  // Trigger search with updated filters
  performSearch();
};

// Function to remove all filters and update URL
const clearAllFilters = () => {
  activeFilters.value = [];
  statusValues.value = [];
  accessValues.value = [];
  libraryValues.value = null;
  labelValues.value = null;
  productValues.value = [];
  tagValues.value = [];
  searchInputValue.value = '';
  
  // After clearing all filters, refresh the results
  emit('reset');
};

// Update URL params
const updateUrlParams = (params: any) => {
  // Skip URL update if we're currently updating from the URL
  if (isUpdatingFromUrl.value) return;
  
  const query: Record<string, any> = { ...route.query };
  
  // Update search query param
  if (searchInputValue.value) {
    query.q = searchInputValue.value;
  } else {
    delete query.q;
  }
  
  // Update status filter param
  if (statusValues.value.length > 0) {
    query.status = statusValues.value.length === 1 ? statusValues.value[0] : statusValues.value;
  } else {
    delete query.status;
  }
  
  // Update access filter param
  if (accessValues.value.length > 0) {
    query.access = accessValues.value.length === 1 ? accessValues.value[0] : accessValues.value;
  } else {
    delete query.access;
  }
  
  // Update library filter param
  if (libraryValues.value) {
    query.library = libraryValues.value;
  } else {
    delete query.library;
  }
  
  // Update label filter param
  if (labelValues.value) {
    query.label = labelValues.value;
  } else {
    delete query.label;
  }
  
  // Update products filter param
  if (productValues.value.length > 0) {
    query.products = productValues.value.length === 1 ? productValues.value[0] : productValues.value;
  } else {
    delete query.products;
  }
  
  // Update tags filter param
  if (tagValues.value.length > 0) {
    query.tags = tagValues.value.length === 1 ? tagValues.value[0] : tagValues.value;
  } else {
    delete query.tags;
  }



  // Update pagination params - always include page parameter
  query.page = String(props.currentPage || 1);
  
  if (props.rowsPerPage && props.rowsPerPage !== 25) { // Only include if not default
    query.limit = props.rowsPerPage.toString();
  } else {
    delete query.limit;
  }
  
  // Update sorting params - only if a sort field is specified
  if (props.sortField && props.sortField !== '') {
    query.sortField = props.sortField;
    query.sortOrder = props.sortOrder?.toString() || '1';
    
    // For debugging - this shows the exact sort format used in the API call
    console.log('Sort in URL:', { 
      field: props.sortField, 
      order: props.sortOrder, 
      apiFormat: JSON.stringify([{
        property: props.sortField,
        direction: props.sortOrder === 1 ? 'ASC' : 'DESC'
      }])
    });
  } else {
    delete query.sortField;
    delete query.sortOrder;
  }
  
  // Replace the current URL with the new params
  router.replace({ query });
};

// Handle search input with debounce
const handleSearchInput = (event: Event) => {
  // Clear any existing timeouts
  if (searchDelayTimeout.value !== null) {
    clearTimeout(searchDelayTimeout.value);
  }
  
  // Set a new timeout for debounce (500ms)
  searchDelayTimeout.value = window.setTimeout(() => {
    performSearch();
  }, 500);
};

// Handle search on enter key
const handleSearchKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (searchDelayTimeout.value !== null) {
      clearTimeout(searchDelayTimeout.value);
      searchDelayTimeout.value = null;
    }
    performSearch();
  }
};

// Clear search function
const clearSearch = () => {
  searchInputValue.value = '';
  performSearch();
};

// Perform search with all filters
const performSearch = () => {
  const apiParams: any = {
    filter: [
      { property: "type", value: store.selectedContentType === 'templates' ? 1 : 0 },
      { property: "id", value: "_no_filter_" },
      { property: "parent_id", value: null },
      { property: "root_parent_id", value: null },
      { property: "partner_ids", operator: "intersect_set", value: null },
      { property: "visibility", operator: "=", value: null },
      { property: "status", operator: "=", value: null },
      { property: "dict_id", value: null },
      { property: "tag_ids", value: null }
    ]
  };
  
  // Add query parameter if there is a search term
  if (searchInputValue.value.trim()) {
    apiParams.query = searchInputValue.value.trim();
  }
  
  // Add status filter if values are selected
  if (statusValues.value && statusValues.value.length > 0) {
    // Find and update the status filter
    const statusIndex = apiParams.filter.findIndex((f: any) => f.property === "status");
    if (statusIndex >= 0) {
      if (statusValues.value.length === 1) {
        // For single value, use "=" operator
        apiParams.filter[statusIndex].operator = "=";
        // Keep status value as string
        apiParams.filter[statusIndex].value = statusValues.value[0];
      } else {
        // For multiple values, use "in" operator
        apiParams.filter[statusIndex].operator = "in";
        // Keep status values as strings for the API
        apiParams.filter[statusIndex].value = statusValues.value;
      }
    }
  }
  
  // Add access/visibility filter if values are selected
  if (accessValues.value && accessValues.value.length > 0) {
    // Find and update the visibility filter
    const visibilityIndex = apiParams.filter.findIndex((f: any) => f.property === "visibility");
    if (visibilityIndex >= 0) {
      if (accessValues.value.length === 1) {
        // For single value, use "=" operator
        apiParams.filter[visibilityIndex].operator = "=";
        apiParams.filter[visibilityIndex].value = accessValues.value[0];
      } else {
        // For multiple values, use "in" operator
        apiParams.filter[visibilityIndex].operator = "in";
        apiParams.filter[visibilityIndex].value = accessValues.value;
      }
    }
  }
  
  // Add library filter if values are selected
  if (libraryValues.value) {
    // Find and update the root_parent_id filter (which represents library)
    const rootParentIndex = apiParams.filter.findIndex((f: any) => f.property === "root_parent_id");
    if (rootParentIndex >= 0) {
      apiParams.filter[rootParentIndex].operator = "=";
      apiParams.filter[rootParentIndex].value = libraryValues.value;
    }
  }
  
  // Add label filter if values are selected
  if (labelValues.value) {
    // Find and update the parent_id filter (which represents label)
    const parentIndex = apiParams.filter.findIndex((f: any) => f.property === "parent_id");
    if (parentIndex >= 0) {
      apiParams.filter[parentIndex].operator = "=";
      apiParams.filter[parentIndex].value = labelValues.value;
    }
  }
  
  // Add products filter if values are selected
  if (productValues.value && productValues.value.length > 0) {
    // Find and update the dict_id filter (which represents products)
    const dictIndex = apiParams.filter.findIndex((f: any) => f.property === "dict_id");
    if (dictIndex >= 0) {
      if (productValues.value.length === 1) {
        apiParams.filter[dictIndex].operator = "=";
        apiParams.filter[dictIndex].value = productValues.value[0];
      } else {
        apiParams.filter[dictIndex].operator = "in";
        apiParams.filter[dictIndex].value = productValues.value;
      }
    }
  }
  
  // Add tags filter if values are selected
  if (tagValues.value && tagValues.value.length > 0) {
    // Find and update the tag_ids filter
    const tagIndex = apiParams.filter.findIndex((f: any) => f.property === "tag_ids");
    if (tagIndex >= 0) {
      if (tagValues.value.length === 1) {
        apiParams.filter[tagIndex].operator = "=";
        apiParams.filter[tagIndex].value = tagValues.value[0];
      } else {
        apiParams.filter[tagIndex].operator = "in";
        apiParams.filter[tagIndex].value = tagValues.value;
      }
    }
  }
  


  // Add pagination parameters - ensure these are numbers
  const page = props.currentPage || 1;
  const limit = props.rowsPerPage || 25;
  
  apiParams.page = page;
  apiParams.start = (page - 1) * limit;
  apiParams.limit = limit;
  
  // Add sorting parameters if available
  if (props.sortField && props.sortField !== '') {
    // Format sort parameter exactly as shown in the example
    apiParams.sort = JSON.stringify([{
      property: props.sortField,
      direction: props.sortOrder === 1 ? 'ASC' : 'DESC'
    }]);
  }
  
  // Update URL params
  updateUrlParams(apiParams);
  
  // Emit search event with parameters
  emit('search', apiParams);
};

// Load params from URL
const loadParamsFromUrl = () => {
  // Set the flag to indicate we're updating from URL
  isUpdatingFromUrl.value = true;
  
  try {
    // Get search query from URL
    if (route.query.q) {
      searchInputValue.value = route.query.q as string;
    } else {
      searchInputValue.value = '';
    }
    
    // Get status filter from URL
    if (route.query.status) {
      const statusParam = route.query.status;
      if (Array.isArray(statusParam)) {
        statusValues.value = statusParam.map(s => String(s));
      } else {
        statusValues.value = [String(statusParam)];
      }
      
      if (!activeFilters.value.includes('status')) {
        activeFilters.value.push('status');
      }
    } else {
      statusValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'status');
    }
    
    // Get access filter from URL
    if (route.query.access) {
      const accessParam = route.query.access;
      if (Array.isArray(accessParam)) {
        accessValues.value = accessParam.map(a => String(a));
      } else {
        accessValues.value = [String(accessParam)];
      }
      
      if (!activeFilters.value.includes('access')) {
        activeFilters.value.push('access');
      }
    } else {
      accessValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'access');
    }
    
    // Get library filter from URL
    if (route.query.library) {
      const libraryParam = route.query.library;
      libraryValues.value = String(libraryParam);
      
      if (!activeFilters.value.includes('library')) {
        activeFilters.value.push('library');
      }
      
      // Update store state for sidebar synchronization
      store.setSelectedLibraryId(String(libraryParam));
    } else {
      libraryValues.value = null;
      activeFilters.value = activeFilters.value.filter(f => f !== 'library');
      
      // Clear library selection in store if no URL param
      if (!route.query.label) {
        store.setSelectedLibraryId(null);
      }
    }
    
    // Get label filter from URL
    if (route.query.label) {
      const labelParam = route.query.label;
      labelValues.value = String(labelParam);
      
      if (!activeFilters.value.includes('label')) {
        activeFilters.value.push('label');
      }
      
      // Update store state for sidebar synchronization
      store.setSelectedLabelId(String(labelParam));
      
      // If we have a label but no library, we need to find the library for this label
      if (!route.query.library) {
        // Find the library ID for this label from the labelMap or treeNodes
        const findLibraryForLabel = (labelId: string) => {
          for (const node of store.treeNodes) {
            if (node.isLibrary && node.children) {
              const hasLabel = (children: any[]): boolean => {
                return children.some(child => {
                  if (child.id === labelId) return true;
                  if (child.children) return hasLabel(child.children);
                  return false;
                });
              };
              
              if (hasLabel(node.children)) {
                return node.id;
              }
            }
          }
          return null;
        };
        
        const parentLibraryId = findLibraryForLabel(String(labelParam));
        if (parentLibraryId) {
          libraryValues.value = parentLibraryId;
          if (!activeFilters.value.includes('library')) {
            activeFilters.value.push('library');
          }
          store.setSelectedLibraryId(parentLibraryId);
        }
      }
    } else {
      labelValues.value = null;
      activeFilters.value = activeFilters.value.filter(f => f !== 'label');
      
      // Clear label selection in store
      store.setSelectedLabelId(null);
    }
    
    // Get products filter from URL
    if (route.query.products) {
      const productsParam = route.query.products;
      if (Array.isArray(productsParam)) {
        productValues.value = productsParam.map(p => String(p));
      } else {
        productValues.value = [String(productsParam)];
      }
      
      if (!activeFilters.value.includes('products')) {
        activeFilters.value.push('products');
      }
    } else {
      productValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'products');
    }
    
    // Get tags filter from URL
    if (route.query.tags) {
      const tagsParam = route.query.tags;
      if (Array.isArray(tagsParam)) {
        tagValues.value = tagsParam.map(t => String(t));
      } else {
        tagValues.value = [String(tagsParam)];
      }
      
      if (!activeFilters.value.includes('tags')) {
        activeFilters.value.push('tags');
      }
    } else {
      tagValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'tags');
    }
    
    // Note: hasDraft filter has been removed

  } finally {
    // Reset the flag when we're done
    isUpdatingFromUrl.value = false;
  }
};

// Watch for filter changes
watch(() => statusValues.value, () => {
  if (statusValues.value.length === 0 && activeFilters.value.includes('status')) {
    // Don't remove status from active filters when deselecting all values
    // We still want to show the empty filter
  } else if (statusValues.value.length > 0 && !activeFilters.value.includes('status')) {
    // Add status to active filters when selecting values
    activeFilters.value.push('status');
  }
}, { deep: true });

watch(() => accessValues.value, () => {
  if (accessValues.value.length === 0 && activeFilters.value.includes('access')) {
    // Don't remove access from active filters when deselecting all values
    // We still want to show the empty filter
  } else if (accessValues.value.length > 0 && !activeFilters.value.includes('access')) {
    // Add access to active filters when selecting values
    activeFilters.value.push('access');
  }
}, { deep: true });

watch(() => libraryValues.value, (newValue, oldValue) => {
  if (!newValue && activeFilters.value.includes('library')) {
    // Remove library from active filters when value is cleared (including via clear icon)
    activeFilters.value = activeFilters.value.filter(f => f !== 'library');
    store.setSelectedLibraryId(null);
    
    // Clear label selection when library is cleared
    if (labelValues.value) {
      labelValues.value = null;
      activeFilters.value = activeFilters.value.filter(f => f !== 'label');
      store.setSelectedLabelId(null);
    }
    
    performSearch(); // Trigger search when cleared
  } else if (newValue && !activeFilters.value.includes('library')) {
    // Add library to active filters when selecting a value
    activeFilters.value.push('library');
    store.setSelectedLibraryId(newValue);
  } else if (newValue) {
    // Update store even if filter was already active
    store.setSelectedLibraryId(newValue);
    
    // Clear label selection when library changes (unless we're loading from URL)
    if (oldValue && oldValue !== newValue && !isUpdatingFromUrl.value && labelValues.value) {
      labelValues.value = null;
      activeFilters.value = activeFilters.value.filter(f => f !== 'label');
      store.setSelectedLabelId(null);
    }
  }
});

watch(() => labelValues.value, (newValue) => {
  if (!newValue && activeFilters.value.includes('label')) {
    // Remove label from active filters when value is cleared (including via clear icon)
    activeFilters.value = activeFilters.value.filter(f => f !== 'label');
    store.setSelectedLabelId(null);
    performSearch(); // Trigger search when cleared
  } else if (newValue && !activeFilters.value.includes('label')) {
    // Add label to active filters when selecting a value
    activeFilters.value.push('label');
    store.setSelectedLabelId(newValue);
  } else if (newValue) {
    // Update store even if filter was already active
    store.setSelectedLabelId(newValue);
  }
});

// Watch for new filter changes
watch(() => productValues.value, () => {
  if (productValues.value.length === 0 && activeFilters.value.includes('products')) {
    // Don't remove products from active filters when deselecting all values
  } else if (productValues.value.length > 0 && !activeFilters.value.includes('products')) {
    activeFilters.value.push('products');
  }
}, { deep: true });

watch(() => tagValues.value, () => {
  if (tagValues.value.length === 0 && activeFilters.value.includes('tags')) {
    // Don't remove tags from active filters when deselecting all values
  } else if (tagValues.value.length > 0 && !activeFilters.value.includes('tags')) {
    activeFilters.value.push('tags');
  }
}, { deep: true });

// Note: hasDraft watcher has been removed

// Watch for store changes to update sidebar selection
watch(() => [store.selectedLibraryId, store.selectedLabelId], ([libraryId, labelId]) => {
  // Update selectedNodeKey to reflect the current selection
  if (labelId) {
    // If a label is selected, highlight the label in the sidebar
    store.selectedNodeKey = { [labelId]: true };
  } else if (libraryId) {
    // If only a library is selected, highlight the library in the sidebar
    store.selectedNodeKey = { [libraryId]: true };
  } else {
    // Clear selection if neither is selected
    store.selectedNodeKey = {};
  }
});

// Watch for props changes to update search
const propsChangeTimeout = ref<number | null>(null);

watch(() => [props.currentPage, props.rowsPerPage, props.sortField, props.sortOrder], () => {
  // Only trigger a search if we're not currently loading from URL
  if (!isUpdatingFromUrl.value) {
    // Clear any existing timeout to prevent duplicate calls
    if (propsChangeTimeout.value !== null) {
      clearTimeout(propsChangeTimeout.value);
    }
    
    // Set a new timeout for a very small debounce (50ms)
    // This helps prevent duplicate calls when multiple props change at once
    propsChangeTimeout.value = window.setTimeout(() => {
      performSearch();
      propsChangeTimeout.value = null;
    }, 50);
  }
}, { deep: true });

// Load filter options data
const loadProductsOptions = async () => {
  try {
    const response = await knowledgeAPI.loadProducts({
      sAction: 'listingTemplate',
      page: '1',
      start: '0',
      limit: '1000'
    });
    
    if (response?.partners_product_list?.results) {
      productOptions.value = response.partners_product_list.results.map((item: any) => ({
        label: item.c__lbl || item.name || 'Unnamed Product',
        value: item.dict_id || item.id
      }));
    }
  } catch (error) {
    console.error('Error loading products:', error);
  }
};

const loadTagsOptions = async () => {
  try {
    // Tags are loaded from metaStore, so we just need to ensure meta data is loaded
    if (!metaStore.metaData?.pl__tags) {
      await metaStore.loadMetaData();
    }
  } catch (error) {
    console.error('Error loading tags from meta store:', error);
  }
};

const loadUsersOptions = async () => {
  try {
    await usersStore.fetchUsers();
  } catch (error) {
    console.error('Error loading users:', error);
  }
};

// Initialize component
onMounted(async () => {
  // Load filter options
  await Promise.all([
    loadProductsOptions(),
    loadTagsOptions(),
    loadUsersOptions()
  ]);
  
  // Load URL parameters after data is loaded
  loadParamsFromUrl();
});

// Watch for tree data to be available and update sidebar selection on initial load
watch(() => store.treeNodes.length, (newLength) => {
  if (newLength > 0 && (libraryValues.value || labelValues.value)) {
    // Tree data is now available, update the sidebar selection
    const nodeIdToSelect = labelValues.value || libraryValues.value;
    if (nodeIdToSelect) {
      store.selectedNodeKey = { [nodeIdToSelect]: true };
    }
  }
}, { immediate: true });

// Methods to update filters programmatically (for sidebar integration)
const updateLibraryFilter = (libraryId: string | null) => {
  libraryValues.value = libraryId;
  if (libraryId && !activeFilters.value.includes('library')) {
    activeFilters.value.push('library');
  } else if (!libraryId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'library');
  }
  performSearch();
};

const updateLabelFilter = (labelId: string | null) => {
  labelValues.value = labelId;
  if (labelId && !activeFilters.value.includes('label')) {
    activeFilters.value.push('label');
  } else if (!labelId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'label');
  }
  performSearch();
};

// Combined method to update both library and label filters with a single search
const updateLibraryAndLabelFilters = (libraryId: string | null, labelId: string | null) => {
  // Update library filter
  libraryValues.value = libraryId;
  if (libraryId && !activeFilters.value.includes('library')) {
    activeFilters.value.push('library');
  } else if (!libraryId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'library');
  }
  
  // Update label filter
  labelValues.value = labelId;
  if (labelId && !activeFilters.value.includes('label')) {
    activeFilters.value.push('label');
  } else if (!labelId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'label');
  }
  
  // Only call performSearch once
  performSearch();
};

const clearLibraryAndLabelFilters = () => {
  libraryValues.value = null;
  labelValues.value = null;
  activeFilters.value = activeFilters.value.filter(f => f !== 'library' && f !== 'label');
  performSearch();
};

// Expose methods for parent components
defineExpose({
  performSearch,
  loadParamsFromUrl,
  clearAllFilters,
  updateLibraryFilter,
  updateLabelFilter,
  updateLibraryAndLabelFilters,
  clearLibraryAndLabelFilters,
  getSearchInputValue: () => searchInputValue.value,
  getStatusValues: () => statusValues.value,
  getAccessValues: () => accessValues.value,
  getLibraryValues: () => libraryValues.value ? [libraryValues.value] : [],
  getLabelValues: () => labelValues.value ? [labelValues.value] : [],
  getActiveFilters: () => activeFilters.value
});

const searchPlaceholder = computed(() =>
  store.selectedContentType === 'templates'
    ? t('knowledge.search_templates')
    : t('knowledge.search_articles')
);

// Register Tooltip directive
const vTooltip = Tooltip;
</script>

<template>
  <div class="filters-container">
    <BravoIconField class="search-field">
      <BravoInputIcon class="pi pi-search" />
      <BravoInputText 
        v-model="searchInputValue" 
        @input="handleSearchInput" 
        @keydown="handleSearchKeydown" 
        :placeholder="searchPlaceholder" 
        :disabled="isLoading"
      />
      <BravoInputIcon 
        v-if="searchInputValue" 
        class="pi pi-times search-clear-icon" 
        @click="clearSearch"
        v-tooltip.bottom="{ value: 'Clear search', showDelay: 400 }"
      />
    </BravoIconField>
    
    <!-- Add Filter dropdown -->
    <BravoButton 
      v-if="activeFilters.length < 7"
      :label="t('knowledge.add_filter')" 
      icon="pi pi-filter"
      severity="secondary"
      @click="showFilterMenu" 
      class="filter-button"
      :disabled="isLoading"
    />
    
    <!-- Clear all filters button -->
    <BravoButton 
      v-if="activeFilters.length > 0"
      icon="pi pi-filter-slash"
      text
      size="small"
      @click="clearAllFilters"
      class="clear-filters-btn"
      label="Clear All"
      :disabled="isLoading"
    />
    
    <!-- Active Filters -->
    <!-- Status Filter -->
    <div v-if="activeFilters.includes('status')" class="active-filter">
      <BravoFilterField
        label="Status"
        :options="statusOptions"
        v-model="statusValues"
        mode="multi"
        @remove="removeFilter('status')"
        @change="performSearch"
      />
    </div>
    
    <!-- Access Filter -->
    <div v-if="activeFilters.includes('access')" class="active-filter">
      <BravoFilterField
        label="Access"
        :options="accessOptions"
        v-model="accessValues"
        mode="multi"
        @remove="removeFilter('access')"
        @change="performSearch"
      />
    </div>
    
    <!-- Library Filter -->
    <div v-if="activeFilters.includes('library')" class="active-filter">
      <BravoFilterField
        label="Library"
        :options="libraryOptions"
        v-model="libraryValues"
        mode="single"
        :filter="true"
        @remove="removeFilter('library')"
        @change="performSearch"
      />
    </div>
    
    <!-- Label Filter -->
    <div v-if="activeFilters.includes('label')" class="active-filter">
      <BravoFilterField
        label="Label"
        :options="labelOptions"
        v-model="labelValues"
        mode="single"
        :filter="true"
        @remove="removeFilter('label')"
        @change="performSearch"
      />
    </div>
    
    <!-- Products Filter -->
    <div v-if="activeFilters.includes('products')" class="active-filter">
      <BravoFilterField
        label="Products"
        :options="productOptions"
        v-model="productValues"
        mode="multi"
        @remove="removeFilter('products')"
        @change="performSearch"
      />
    </div>
    
    <!-- Tags Filter -->
    <div v-if="activeFilters.includes('tags')" class="active-filter">
      <BravoFilterField
        label="Tags"
        :options="tagOptions"
        v-model="tagValues"
        mode="multi"
        @remove="removeFilter('tags')"
        @change="performSearch"
      />
    </div>
    
    <!-- Filter Menu -->
    <BravoMenu ref="filterMenu" :model="filterTypes.filter(f => !activeFilters.includes(f.value)).map(f => ({
      label: f.label,
      icon: f.icon,
      command: () => addFilter(f.value)
    }))" :popup="true" />
  </div>
</template>

<style scoped>
.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
  margin-bottom: 1.5rem;
  width: 100%;
  justify-content: flex-start;
}

.search-field {
  position: relative;
  /* flex: 1 1 300px; */
  min-width: 200px;
}

.filter-button,
.clear-filters-btn,
.active-filter {
  flex-shrink: 0;
}

.search-clear-icon {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--text-color-secondary);
  z-index: 10;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  font-size: 0.875rem;
  margin-top: 0px;
}

.search-clear-icon:hover {
  color: var(--text-color);
  background-color: var(--surface-100);
}
</style> 