<!-- BravoFilterField.vue -->
<script setup lang="ts">
import { ref, watch, onBeforeUnmount, computed } from 'vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoFilterMultiSelect from '@services/ui-component-library/components/BravoFilterMultiSelect.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';

interface FilterOption {
  label: string;
  value: string;
}

const props = defineProps<{
  label: string;
  options: FilterOption[];
  modelValue: string[] | string | null;
  borderColor?: string;
  mode?: 'single' | 'multi'; // New prop to determine single or multi-select
  placeholder?: string;
  filter?: boolean; // New prop to enable filtering
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string[] | string | null): void;
  (e: 'remove'): void;
  (e: 'change'): void;
}>();

const internalValue = ref<string[] | string | null>(null);
const changeTimeout = ref<number | null>(null);

// Determine if we're in single or multi mode
const isMultiMode = computed(() => props.mode === 'multi');

// Sync internal value with external value
watch(() => props.modelValue, (newVal) => {
  if (isMultiMode.value) {
    // Multi-select mode
    const newArray = Array.isArray(newVal) ? newVal : [];
    if (JSON.stringify(internalValue.value) !== JSON.stringify(newArray)) {
      internalValue.value = [...newArray];
    }
  } else {
    // Single-select mode
    const newSingle = Array.isArray(newVal) ? newVal[0] || null : newVal;
    if (internalValue.value !== newSingle) {
      internalValue.value = newSingle;
    }
  }
}, { immediate: true });

// Update external value when internal value changes with debounce
watch(() => internalValue.value, (newVal) => {
  emit('update:modelValue', newVal);
  
  // Clear any existing timeout
  if (changeTimeout.value !== null) {
    window.clearTimeout(changeTimeout.value);
    changeTimeout.value = null;
  }
  
  // Set a new timeout to prevent rapid consecutive changes
  changeTimeout.value = window.setTimeout(() => {
    emit('change');
  }, 300);
}, { deep: true });

// Clean up timeout on component unmount
onBeforeUnmount(() => {
  if (changeTimeout.value !== null) {
    window.clearTimeout(changeTimeout.value);
    changeTimeout.value = null;
  }
});

// Handle the remove button click
const removeFilter = () => {
  emit('remove');
};

// Computed placeholder
const computedPlaceholder = computed(() => {
  if (props.placeholder) return props.placeholder;
  return `Select ${props.label.toLowerCase()}`;
});

// Computed value for single select to handle type conversion
const singleSelectValue = computed({
  get: () => {
    if (isMultiMode.value) return null;
    return internalValue.value as string | null;
  },
  set: (value: string | null) => {
    internalValue.value = value;
  }
});

// Custom filter function for options with fullLabel
const customFilterFunction = computed(() => {
  if (!props.filter) return undefined;
  
  // Check if any option has a fullLabel property
  const hasFullLabel = props.options.some(option => 'fullLabel' in option);
  
  if (!hasFullLabel) return undefined;
  
  // Return custom filter function that searches against fullLabel
  return (value: string, filter: string) => {
    if (!filter) return true;
    
    const option = props.options.find(opt => opt.value === value);
    if (!option) return false;
    
    const searchText = (option as any).fullLabel || option.label || '';
    return searchText.toLowerCase().includes(filter.toLowerCase());
  };
});
</script>

<template>
  <div class="filter-container" :style="{ 
    borderColor: borderColor || 'var(--p-inputtext-border-color, #ced4da)' 
  }">
    <div class="filter-label-with-close" :style="{ 
      borderColor: borderColor || 'var(--p-inputtext-border-color, #ced4da)' 
    }">
      <BravoButton 
        icon="pi pi-times" 
        text 
        size="small" 
        @click="removeFilter" 
        class="filter-remove-btn"
      />
      <div class="filter-label">{{ label }}:</div>
    </div>
    
    <!-- Multi-select mode -->
    <BravoFilterMultiSelect
      v-if="isMultiMode"
      v-model="internalValue"
      :filterOptions="options"
      :placeholder="computedPlaceholder"
      optionLabel="label"
      optionValue="value"
      class="w-full"
      @filter-change="(value) => emit('update:modelValue', value)"
    />
    
    <!-- Single-select mode -->
    <BravoSelect
      v-else
      v-model="singleSelectValue as string | number | null"
      :options="options as any"
      :placeholder="computedPlaceholder"
      :filter="filter"
      :filterFunction="customFilterFunction"
      class="bravo-filter-single"
      id="filter-select"
      dataTestId="filter-select"
    />
  </div>
</template>

<style scoped lang="scss">
.filter-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--p-inputtext-border-color, #ced4da);
  border-radius: 6px;
  overflow: hidden;
  background-color: var(--surface-0, #ffffff);
}

.filter-label-with-close {
  display: flex;
  align-items: center;
}

.filter-label {
  font-weight: 600;
  font-size: 0.875rem;
  white-space: nowrap;
  padding: 0 0.75rem 0 0;
  color: var(--text-color-secondary, #6c757d);
}

.filter-remove-btn {
  margin: 0;
  padding: 0 0.5rem 0 0.75rem;
}

.filter-remove-btn:deep(.p-button-icon) {
  font-size: 0.75rem;
}

:deep(.bravo-filter) {
  min-width: 180px;
  border: none;
  border-radius: 0;
}

:deep(.bravo-filter .p-multiselect) {
  border: none !important;
  border-radius: 0;
  min-width: 180px !important;
  position: relative !important;
}

:deep(.bravo-filter .p-multiselect-label-container) {
  min-width: 0 !important;
}

:deep(.bravo-filter .p-multiselect-trigger) {
  position: absolute !important;
  right: 0.5rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

:deep(.bravo-filter .p-multiselect-label) {
  padding-right: 1rem !important;
  min-width: 0 !important;
}
.filter-container {
  &:deep(.p-select) {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    width: 100%;
  }
}


:deep(.bravo-filter .p-multiselect:hover) {
  background-color: var(--surface-hover, #f8f9fa) !important;
}

:deep(.bravo-filter .p-multiselect:focus-within) {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: -2px !important;
}

.bravo-filter-single {
  min-width: 180px;
  border: none !important;
  border-radius: 0;
}

:deep(.bravo-filter-single .p-dropdown) {
  border: none !important;
  border-radius: 0;
  box-shadow: none !important;
  min-width: 180px !important;
  position: relative !important;
}

:deep(.bravo-filter-single .p-inputtext) {
  border: none !important;
  box-shadow: none !important;
  padding-right: 1rem !important;
  width: 100% !important;
}

:deep(.bravo-filter-single .p-dropdown-trigger) {
  border-left: none !important;
  position: absolute !important;
  right: 0.5rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

:deep(.bravo-filter-single .p-dropdown-label) {
  padding-right: 2.5rem !important;
  width: 100% !important;
  min-width: 0 !important;
}

:deep(.bravo-filter-single .p-dropdown:hover) {
  background-color: var(--surface-hover, #f8f9fa) !important;
}

:deep(.bravo-filter-single .p-dropdown:focus-within) {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: -2px !important;
}
</style> 