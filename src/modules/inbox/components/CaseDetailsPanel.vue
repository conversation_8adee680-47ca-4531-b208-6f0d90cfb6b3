<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import { markRaw, defineComponent } from 'vue'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import BravoBlock from '@services/ui-component-library/components/BravoBlock.vue'
import BravoAccordionHeader from '@services/ui-component-library/components/BravoAccordionHeader.vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoDrawer from '@services/ui-component-library/components/BravoDrawer.vue'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import BravoProgressBar from '@services/ui-component-library/components/BravoProgressBar.vue'
import CaseFilesList from './CaseFilesList.vue'
import TaskDetail from './TaskDetail.vue'
import AddFileModal from './AddFileModal.vue'
import type { Issue } from '../../../services/IssuesAPI'
import type { Task } from '../../../types/task'
import type { SchemaField } from '@/composables/services/useIssuesAPI'
import { useCasesStore } from '@/stores/cases'
import { usePartnerStore } from '@/stores/partner'
import { useMetaStore } from '@/stores/meta'
import { fetchCaseTasks, updateTaskStatus } from '../../../composables/services/useTasksApi'
import { 
  formatUtcTimestamp, 
  formatTaskDueDate,
  getTaskTeam,
  getTaskUser 
} from '../utils/eventHelpers'
import { getTaskTeamName, getTaskUserName, getTaskStatusSeverity } from '../utils/taskHelpers'
import { getSentimentSeverity, getScoreColor, getScoreTextColor, formatSentimentScore, formatKeyIndicators, formatConfidence } from '../utils/aiHelper'
import CaseInfoPanel from './CaseInfoPanel.vue'
import { useFilesAPI } from '@/composables/services/useFilesAPI'
import { useToast } from 'primevue/usetoast'

interface PanelConfig {
  id: string
  title: string
  component: any
  visible: boolean
  props?: Record<string, any>
  events?: Record<string, (...args: any[]) => void>
}

const props = withDefaults(defineProps<{
  issue?: Issue
  loading?: boolean
}>(), {
  issue: undefined,
  loading: false
})

const emit = defineEmits<{
  (e: 'contact-click', contact: any): void
}>()

const casesStore = useCasesStore()
const partnerStore = usePartnerStore()
const metaStore = useMetaStore()
const filesAPI = useFilesAPI()
const toast = useToast()

// Get the case schema from the cases store with proper schema selection logic
const caseSchema = computed(() => {
  // Start with trying to get the proper schema based on the old application logic
  let selectedSchema: SchemaField[] = []
  
  // Get the case meta data which contains alt_schema
  const caseMeta = casesStore.currentIssueCaseMeta
  const issue = props.issue
  
  if (caseMeta && issue) {
    // Determine the schema key to use based on issue type and organization
    const issueType = (issue as any).type || 1
    const orgId = (issue as any).context_org_id || (issue as any).owner_partners_id || (issue as any).sponsor_partners_id
    
    // Build possible schema keys in order of preference (matching old app's buildGlobalSchema)
    const schemaKeys = []
    
    // 1. Organization-specific schema with issue type
    if (orgId && issueType === 3) {
      schemaKeys.push(`${orgId}-issues-3`)
    }
    
    // 2. Organization-specific schema
    if (orgId) {
      schemaKeys.push(`${orgId}-issues`)
    }
    
    // 3. Issue type specific schema
    if (issueType === 3) {
      schemaKeys.push('issues-3')
    }
    
    // 4. Default issues schema
    schemaKeys.push('issues')
    
    // Try to find a schema in alt_schema first
    if (caseMeta.alt_schema) {
      for (const key of schemaKeys) {
        if (caseMeta.alt_schema[key] && caseMeta.alt_schema[key].length > 0) {
          selectedSchema = caseMeta.alt_schema[key]
          break
        }
      }
    }
    
    // If no alt_schema found, use the main schema
    if (selectedSchema.length === 0 && caseMeta.schema && caseMeta.schema.length > 0) {
      selectedSchema = caseMeta.schema
    }
  }
  
  // Fallback to currentIssueSchema if no schema selected yet
  if (selectedSchema.length === 0) {
    selectedSchema = casesStore.currentIssueSchema || []
  }
  
  // Check if resolution fields are already in the schema
  const hasResolutionStatus = selectedSchema.some(field => field.field === 'resolve_issue')
  const hasResolutionNotes = selectedSchema.some(field => field.field === 'resolution_status')
  
  // If resolution fields are missing, add them manually
  const additionalFields = []
  
  if (!hasResolutionStatus) {
    additionalFields.push({
      layout_id: "manual",
      creator_user_id: null,
      position: 9999, // Put at end
      type: 1,
      field: "resolve_issue",
      data: {},
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      metadata: null,
      c__creator_user_id: null,
      c__condition: "null",
      _highlighted: false,
      _highlightmap: {},
      conditions: [],
      object: "issues",
      fieldType: "picklist",
      required: false,
      requiredResolve: false,
      readOnly: false,
      lbl: "Resolution Status",
      options: []
    })
  }
  
  if (!hasResolutionNotes) {
    additionalFields.push({
      layout_id: "manual",
      creator_user_id: null,
      position: 10000, // Put at end
      type: 1,
      field: "resolution_status",
      data: {},
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      metadata: null,
      c__creator_user_id: null,
      c__condition: "null",
      _highlighted: false,
      _highlightmap: {},
      conditions: [],
      object: "issues",
      fieldType: "textarea",
      required: false,
      requiredResolve: false,
      readOnly: false,
      lbl: "Resolution Notes",
      options: []
    })
  }
  

  
  return [...selectedSchema, ...additionalFields]
})

// Tasks state management
const tasks = ref<Task[]>([])
const loadingTasks = ref(false)

// Add File Modal state
const showAddFileModal = ref(false)
const fileUploadProgress = ref(0)

// Create a simple div component
const DivComponent = defineComponent({
  name: 'DivComponent',
  props: {
    class: String
  },
  template: '<div :class="class"><slot></slot></div>'
})

// Simple panels configuration - no complex state management
const panels = ref<PanelConfig[]>([
  {
    id: 'caseInfo',
    title: 'Case Information',
    component: markRaw(DivComponent),
    visible: true,
    props: {
      class: 'case-info-content'
    }
  },
  {
    id: 'caseSummary',
    title: 'Case Summary',
    component: markRaw(DivComponent),
    visible: true,
    props: {
      class: 'case-summary-content'
    }
  },
  {
    id: 'tasks',
    title: 'Tasks',
    component: markRaw(DivComponent),
    visible: true,
    props: {
      class: 'tasks-content'
    }
  },
  {
    id: 'files',
    title: 'Files',
    component: markRaw(CaseFilesList),
    visible: true,
    props: {
      files: casesStore.files,
      loading: casesStore.loadingFiles
    },
    events: {
      'file-deleted': handleFileDeleted,
      'add-file': openAddFileModal
    }
  }
])

// Watch for changes to issue and fetch related data
watch(() => props.issue?.id, async (newIssueId) => {
  if (newIssueId) {
    try {
      // Fetch files
      await casesStore.fetchFiles(newIssueId)
      
      // Update Files panel visibility based on whether there are files
      const filesPanel = panels.value.find(panel => panel.id === 'files')
      if (filesPanel) {
        filesPanel.visible = casesStore.files.length > 0
        // Update props to ensure reactivity
        filesPanel.props = {
          files: casesStore.files,
          loading: casesStore.loadingFiles
        }
      }

      // Fetch tasks for this case
      await fetchTasksForCase(newIssueId)

      // Load partner teams if not already loaded
      if (partnerStore.partnerTeams.length === 0) {
        await partnerStore.fetchPartnerTeams()
      }

      // Load partner metadata if not already loaded
      if (!metaStore.partnerMetaData) {
        await metaStore.loadPartnerMetaData()
      }

      // Fetch case summaries
      try {
        await casesStore.fetchCaseSummaries(newIssueId)
      } catch (error) {
        // Handle 404 silently (case summaries don't exist yet)
        if (error instanceof Error && error.message === 'CASE_SUMMARY_NOT_FOUND') {
          // Silently handle case where summaries don't exist yet
        } else {
          // For other errors, we could show a global notification here
          console.error('Failed to fetch case summaries:', error)
          // TODO: Add global notification for non-404 errors
        }
      }
    } catch (error) {
      console.error('Failed to fetch files:', error)
    }
  } else {
    // Clear tasks when no issue is selected
    tasks.value = []
  }
}, { immediate: true })

// Watch for files changes to update panel visibility
watch(() => casesStore.files, (newFiles) => {
  const filesPanel = panels.value.find(panel => panel.id === 'files')
  if (filesPanel) {
    filesPanel.visible = newFiles.length > 0
    // Update props to ensure reactivity
    filesPanel.props = {
      files: newFiles,
      loading: casesStore.loadingFiles
    }
  }
}, { deep: true })

// Local loading state for the entire summary creation process
const creatingAndFetchingSummary = ref(false)

// Handle create summary button click
async function handleCreateSummary() {
  if (!props.issue?.id) {
    console.error('No issue ID available for summary creation')
    return
  }

  creatingAndFetchingSummary.value = true

  try {
    // Step 1: Create the summary
    const result = await casesStore.createCaseSummary(props.issue.id)
    
    // Step 2: Fetch the updated summaries (now handles pending status with retries)
    try {
      await casesStore.fetchCaseSummaries(props.issue.id)
    } catch (fetchError) {
      console.error('Failed to fetch updated summaries:', fetchError)
      // Don't throw here - the creation was successful even if fetch failed
    }
  } catch (error) {
    console.error('Failed to create summary:', error)
  } finally {
    creatingAndFetchingSummary.value = false
  }
}

// Computed property to determine if we should show the create button or summary content
const showCreateButton = computed(() => {
  return casesStore.caseSummaries.length === 0 && !casesStore.loadingCaseSummaries
})

// Computed property to get the latest summary
const latestSummary = computed(() => {
  if (casesStore.caseSummaries.length === 0) return null
  
  // Sort by created_date and get the most recent
  const sorted = [...casesStore.caseSummaries].sort((a, b) => 
    new Date(b.created_date).getTime() - new Date(a.created_date).getTime()
  )
  

  
  return sorted[0]
})

// Computed property for button label
const summaryButtonLabel = computed(() => {
  return casesStore.caseSummaries.length > 0 ? 'Update Summary' : 'Create Summary'
})

// Computed property for key indicators
const keyIndicators = computed(() => {
  const summary = latestSummary.value
  return summary?.sentiment?.key_indicators || []
})



// Function to fetch tasks for a case
async function fetchTasksForCase(caseId: string) {
  loadingTasks.value = true
  try {
    tasks.value = await fetchCaseTasks(caseId)
  } catch (error) {
    console.error('Failed to fetch tasks for case:', caseId, error)
    tasks.value = []
  } finally {
    loadingTasks.value = false
  }
}

// Task drawer state management
const taskDrawerVisible = ref(false)
const selectedTask = ref<Task | null>(null)

// Function to handle task card click
function handleTaskClick(task: Task) {
  selectedTask.value = task
  taskDrawerVisible.value = true
}

// Function to handle drawer close
function handleDrawerClose() {
  taskDrawerVisible.value = false
  selectedTask.value = null
}

// Function to handle task updates from the drawer
function handleTaskUpdated(updatedTask: Task) {
  // Update the task in the tasks array
  const taskIndex = tasks.value.findIndex(t => t.id === updatedTask.id)
  if (taskIndex !== -1) {
    tasks.value[taskIndex] = updatedTask
  }
  // Update the selected task as well
  selectedTask.value = updatedTask
}

// Function to toggle task completion status
async function toggleTaskComplete(task: Task, event: Event) {
  // Stop event propagation to prevent opening the task drawer
  event.stopPropagation()
  
  const isCompleted = task.status === 'Completed'
  const newStatus = isCompleted ? 'Not Started' : 'Completed'
  
  try {
    const updatedTask = await updateTaskStatus(task, newStatus)
    // Update the task in the tasks array
    const taskIndex = tasks.value.findIndex(t => t.id === task.id)
    if (taskIndex !== -1) {
      tasks.value[taskIndex] = updatedTask
    }
  } catch (error) {
    console.error('Failed to update task status:', error)
    // Could add a toast notification here for better UX
  }
}

// Function to handle field changes from the dynamic form
function handleFieldChange(field: string, value: any) {
  // TODO: Implement field change handling
  // This could update the case via the cases store
}

// Function to handle form save
async function handleFormSave(changes: Record<string, any>) {
  if (!props.issue?.id) {
    console.error('No issue ID available for saving changes')
    return
  }

  try {
    // Use the cases store to update the case with the field changes
    await casesStore.updateCase({
      id: props.issue.id,
      ...changes
    })
  } catch (error) {
    console.error('Failed to save field changes:', error)
    throw error // Re-throw to let the form handle the error
  }
}

// Function to handle form cancel
function handleFormCancel() {
  // TODO: Implement any cleanup logic if needed
}

// Handle contact click from CaseInfoPanel
function handleContactClick(contact: any) {
  emit('contact-click', contact)
}

// Function to handle file deletion
async function handleFileDeleted(fileId: string) {
  if (!props.issue?.id) return
  
  try {
    // Refresh the files list
    await casesStore.fetchFiles(props.issue.id)
    
    // Update Files panel visibility and props
    const filesPanel = panels.value.find(panel => panel.id === 'files')
    if (filesPanel) {
      filesPanel.visible = casesStore.files.length > 0
      filesPanel.props = {
        files: casesStore.files,
        loading: casesStore.loadingFiles
      }
    }
  } catch (error) {
    console.error('Failed to refresh files after deletion:', error)
  }
}

// Function to open add file modal
function openAddFileModal() {
  showAddFileModal.value = true
}

// Function to handle file upload
async function handleFileUpload(file: File, fileTag: string) {
  if (!props.issue?.id) {
    throw new Error('No issue selected')
  }

  try {
    // Use the files API to upload the file
    await filesAPI.uploadFile(
      {
        object: 'issues',
        object_id: props.issue.id,
        file_tag: fileTag,
        file
      },
      (progress) => {
        fileUploadProgress.value = progress
      }
    )

    // Refresh the files list
    await casesStore.fetchFiles(props.issue.id)
    
    // Update Files panel visibility and props
    const filesPanel = panels.value.find(panel => panel.id === 'files')
    if (filesPanel) {
      filesPanel.visible = casesStore.files.length > 0
      filesPanel.props = {
        files: casesStore.files,
        loading: casesStore.loadingFiles
      }
    }

    // Show success toast
    toast.add({
      severity: 'success',
      summary: 'File Uploaded',
      detail: `${file.name} has been uploaded successfully`,
      life: 3000,
    })

    // Close modal
    showAddFileModal.value = false
  } catch (error) {
    console.error('Failed to upload file:', error)
    toast.add({
      severity: 'error',
      summary: 'Upload Failed',
      detail: error instanceof Error ? error.message : 'Failed to upload file',
      life: 5000,
    })
    throw error // Re-throw so modal can handle error state
  } finally {
    fileUploadProgress.value = 0
  }
}

// Function to handle file upload cancel
function handleFileUploadCancel() {
  showAddFileModal.value = false
  fileUploadProgress.value = 0
}

// Computed properties for task statistics
const openTasks = computed(() => {
  return tasks.value.filter(task => task.status !== 'Completed').length
})

const completedTasks = computed(() => {
  return tasks.value.filter(task => task.status === 'Completed').length
})

const totalTasks = computed(() => {
  return tasks.value.length
})

const progressPercentage = computed(() => {
  if (totalTasks.value === 0) return 0
  return Math.round((completedTasks.value / totalTasks.value) * 100)
})

// Flag to track if auto-expand has already been executed
const hasAutoExpanded = ref(false)

// Auto-expand Case Information panel when data is loaded
watch(() => [props.loading, props.issue], async ([loading, issue]) => {
  // Only proceed when loading is false, we have issue data, and haven't already auto-expanded
  if (!loading && issue && !hasAutoExpanded.value) {
    hasAutoExpanded.value = true // Set flag to prevent future executions
    // Find and click the Case Information accordion header
    setTimeout(async () => {
      await nextTick()
      // Target the specific accordion header button for Case Information
      const caseInfoButton = document.querySelector('button[id*="_accordionheader_caseInfo"]')
      
      if (caseInfoButton) {
        ;(caseInfoButton as HTMLElement).click()
      } else {
        // Fallback: look for any accordion header with "Case Information" text
        const accordionHeaders = document.querySelectorAll('button.p-accordionheader')
        
        for (const header of accordionHeaders) {
          if (header.textContent?.includes('Case Information')) {
            ;(header as HTMLElement).click()
            break
          }
        }
      }
    }, 100) // Small delay to ensure everything is rendered
  }
}, { immediate: false })
</script>

<template>
  <div class="panel-content">
    <!-- Loading State -->
    <div v-if="props.loading || !props.issue" class="details-skeleton">
      <!-- Simple BravoBlock Skeletons -->
      <BravoSkeleton v-for="i in 4" :key="'bravoblock-skeleton-' + i" width="100%" height="30px" border-radius="6px" class="skeleton-block" />
    </div>
    
    <!-- Actual Content -->
    <div v-else>
      <!-- Panels using BravoBlock with natural accordion behavior -->
      <BravoBlock 
        v-for="panel in panels"
        :key="panel.id"
        v-show="panel.visible"
        multiple 
        class="panel-block"
      >
        <AccordionPanel :value="panel.id">
          <!-- Use BravoAccordionHeader for Tasks panel to show count -->
          <BravoAccordionHeader 
            v-if="panel.id === 'tasks'"
            :count="totalTasks"
            :show-badge="true"
          >
            {{ panel.title }}
          </BravoAccordionHeader>
          
          <!-- Use BravoAccordionHeader for Files panel to show count -->
          <BravoAccordionHeader 
            v-else-if="panel.id === 'files'"
            :count="casesStore.filesTotalCount"
            :show-badge="true"
          >
            {{ panel.title }}
          </BravoAccordionHeader>
          
          <!-- Use regular AccordionHeader for other panels -->
          <AccordionHeader v-else>
            {{ panel.title }}
          </AccordionHeader>
          
          <AccordionContent>
            <!-- Case Information content -->
            <template v-if="panel.id === 'caseInfo'">
              <CaseInfoPanel 
                v-if="caseSchema.length > 0"
                :schema-fields="caseSchema"
                :issue="props.issue"
                :loading="props.loading"
                :readonly="false"
                @field-change="handleFieldChange"
                @save="handleFormSave"
                @cancel="handleFormCancel"
                @contact-click="handleContactClick"
              />
              
              <!-- Fallback when no schema is available -->
              <div v-else class="fallback-case-info">
                <div v-if="casesStore.loading" class="loading-message">
                  <BravoSkeleton width="100%" height="20px" class="mb-2" />
                  <BravoSkeleton width="80%" height="20px" class="mb-2" />
                  <BravoSkeleton width="60%" height="20px" />
                </div>
                <div v-else>
                  <div class="case-field">
                    <strong>ID:</strong> {{ props.issue?.id }}
                  </div>
                  <div v-if="props.issue?.source_data" class="case-field">
                    <strong>Source:</strong> {{ props.issue?.source_data.source }}
                  </div>
                  <div class="case-field">
                    <strong>Available Communications:</strong> 
                    {{ props.issue?.availableComm ? props.issue?.availableComm.length : 0 }}
                  </div>
                </div>
              </div>
            </template>
            
            <!-- Case Summary content -->
            <template v-else-if="panel.id === 'caseSummary'">
              <!-- Loading state for initial fetch -->
              <div v-if="casesStore.loadingCaseSummaries && !creatingAndFetchingSummary" class="summary-loading">
                <BravoSkeleton width="100%" height="16px" class="mb-2" />
                <BravoSkeleton width="80%" height="16px" class="mb-2" />
                <BravoSkeleton width="60%" height="16px" />
              </div>
              
              <!-- Loading state during summary creation/update -->
              <div v-else-if="creatingAndFetchingSummary" class="summary-creating">
                <div class="creating-message">
                  <strong>{{ summaryButtonLabel.includes('Update') ? 'Updating' : 'Creating' }} summary...</strong>
                </div>
                <BravoSkeleton width="100%" height="16px" class="mb-2" />
                <BravoSkeleton width="85%" height="16px" class="mb-2" />
                <BravoSkeleton width="70%" height="16px" />
                <div class="loading-note">
                  <small class="text-muted">This may take a few moments while we analyze the case.</small>
                </div>
              </div>
              
              <!-- Content when not loading -->
              <div v-else class="summary-section">
                <!-- Show summary if available -->
                <div v-if="latestSummary" class="summary-content">
                  <div class="summary-text">
                    {{ latestSummary.summary }}
                  </div>
                  
                  <!-- Display sentiment emotion and score -->
                  <div v-if="latestSummary.sentiment?.emotion" class="summary-sentiment">
                    <div
                      v-tooltip.top="formatKeyIndicators(keyIndicators)"
                      class="emotion-wrapper"
                    >
                      <BravoTag 
                        :value="latestSummary.sentiment.emotion"
                        :severity="getSentimentSeverity(latestSummary.sentiment.emotion)"
                      />
                    </div>
                    <div 
                      v-if="latestSummary.sentiment.score !== undefined"
                      v-tooltip.top="formatConfidence(latestSummary.sentiment.confidence || 0)"
                      class="sentiment-score"
                      :style="{
                        backgroundColor: getScoreColor(latestSummary.sentiment.score),
                        color: getScoreTextColor(latestSummary.sentiment.score)
                      }"
                    >
                      {{ formatSentimentScore(latestSummary.sentiment.score) }}
                    </div>
                  </div>
                  
                  <div class="summary-meta">
                    <small class="text-muted">
                      Generated {{ formatUtcTimestamp(latestSummary.created_date) }} 
                      ({{ latestSummary.status }})
                    </small>
                  </div>
                </div>
                
                <!-- Always show the button -->
                <BravoButton 
                  :label="summaryButtonLabel" 
                  :loading="creatingAndFetchingSummary"
                  @click="handleCreateSummary"
                  class="summary-button"
                />
              </div>
            </template>
            
            <!-- Tasks content -->
            <template v-else-if="panel.id === 'tasks'">
              <!-- Loading state -->
              <div v-if="loadingTasks" class="tasks-loading">
                <BravoSkeleton width="100%" height="16px" class="mb-2" />
                <BravoSkeleton width="80%" height="16px" class="mb-2" />
                <BravoSkeleton width="60%" height="16px" />
              </div>
              
              <!-- Empty state -->
              <div v-else-if="tasks.length === 0" class="tasks-empty">
                <div class="tasks-placeholder">
                  <p>No tasks found for this case.</p>
                  <small class="text-muted">Tasks will appear here when they are created.</small>
                </div>
              </div>
              
              <!-- Tasks list -->
              <div v-else class="tasks-section">
                <div class="tasks-header">
                    <div class="tasks-title">
                    <strong>{{ openTasks }} Open Task{{ openTasks !== 1 ? 's' : '' }}</strong>
                    </div>
                    <div class="tasks-progress">
                      <BravoProgressBar 
                        :value="progressPercentage"
                        class="progress-bar"
                      >
                        {{ completedTasks }}/{{ totalTasks }} Completed
                      </BravoProgressBar>
                    </div>
                </div>
                
                <div class="tasks-list">
                  <div 
                    v-for="task in tasks" 
                    :key="task.id" 
                    class="task-item"
                    @click="handleTaskClick(task)"
                  >
                    <div class="task-header">
                      <div class="task-name">{{ task.name }}</div>
                      <BravoTag 
                        :value="task.status" 
                        :severity="getTaskStatusSeverity(task)"
                      />
                    </div>
                    
                    <div v-if="task.description" class="task-description">
                      {{ task.description }}
                    </div>
                    
                    <div class="task-meta">
                      <div class="task-meta-item">
                        <strong>Created:</strong> {{ formatUtcTimestamp(task.createdAt) }}
                      </div>
                      <div v-if="task.dueDate" class="task-meta-item">
                        <strong>Due:</strong> {{ formatTaskDueDate(task.dueDate) }}
                      </div>
                      <div v-if="getTaskTeamName(task, partnerStore.partnerTeams) !== '—'" class="task-meta-item">
                        <strong>Team:</strong> {{ getTaskTeamName(task, partnerStore.partnerTeams) }}
                      </div>
                      <div v-if="getTaskUserName(task, metaStore.partnerMetaData) !== '—'" class="task-meta-item">
                        <strong>User:</strong> {{ getTaskUserName(task, metaStore.partnerMetaData) }}
                      </div>
                    </div>
                    
                    <div class="task-actions">
                      <BravoButton
                        :label="task.status === 'Completed' ? 'Reopen' : 'Mark as Complete'"
                        :icon="task.status === 'Completed' ? 'pi pi-undo' : 'pi pi-check'"
                        size="small"
                        :severity="task.status === 'Completed' ? 'secondary' : 'primary'"
                        @click="(event: Event) => toggleTaskComplete(task, event)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </template>
            
            <!-- Dynamic component rendering -->
            <component
              v-else
              :is="panel.component"
              v-bind="panel.props"
              v-on="panel.events || {}"
            />
          </AccordionContent>
        </AccordionPanel>
      </BravoBlock>
    </div>
  </div>

  <!-- Task Drawer -->
  <BravoDrawer
    :visible="taskDrawerVisible"
    @update:visible="(value) => taskDrawerVisible = value"
    position="left"
    :style="{ width: '600px' }"
    :modal="true"
    :dismissable="true"
  >
    <template #header>
      <div>
        <h3>{{ selectedTask ? selectedTask.name : 'Task Details' }}</h3>
      </div>
    </template>
    <TaskDetail 
      v-if="selectedTask" 
      :task="selectedTask"
      @back="handleDrawerClose"
      @task-updated="handleTaskUpdated"
    />
  </BravoDrawer>

  <!-- Add File Modal -->
  <AddFileModal
    :visible="showAddFileModal"
    @update:visible="(value) => showAddFileModal = value"
    :issue="props.issue || null"
    :uploadProgress="fileUploadProgress"
    :onSubmit="handleFileUpload"
    :onCancel="handleFileUploadCancel"
  />
</template>

<style scoped>
.panel-content {
  display: flex;
  flex-direction: column;
  padding: 0rem 0rem 1rem 0rem;
  gap: 1rem;
  flex: 1;
  width: 100%;
  max-width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 150px); /* Account for header and tabs */
}

.panel-block {
  width: 100%;
}

.case-field {
  margin-bottom: 0.75rem;
}

.case-field:last-child {
  margin-bottom: 0;
}

/* Animation transitions */
.panel-list-move {
  transition: transform 0.3s ease;
}

.panel-list-enter-active,
.panel-list-leave-active {
  transition: all 0.3s ease;
}

.panel-list-enter-from,
.panel-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.details-skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

.skeleton-block {
  margin-bottom: 0;
}

.summary-creating {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.creating-message {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.loading-note {
  margin-top: 0.5rem;
  text-align: center;
}

.summary-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.summary-text {
  line-height: 1.5;
  color: var(--text-color);
}

.summary-sentiment {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.emotion-wrapper {
  display: inline-flex;
  cursor: help;
}

.sentiment-score {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  text-align: center;
  cursor: help;
}

.summary-meta {
  padding-top: 0.5rem;
  border-top: 1px solid var(--surface-200);
}

.summary-button {
  align-self: flex-start;
}

.text-muted {
  color: var(--text-color-secondary);
}

.tasks-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tasks-placeholder {
  text-align: center;
  padding: 2rem;
  color: var(--text-color-secondary);
  background: var(--surface-50);
  border-radius: 6px;
  border: 1px dashed var(--surface-300);
}

.tasks-loading {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tasks-empty {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tasks-header {
  margin-bottom: 1rem;
  color: var(--text-color);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tasks-title {
  display: flex;
  align-items: center;
}

.tasks-progress {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-bar {
  height: 20px;
  background: var(--surface-200);
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-item {
  padding: 1rem;
  border: 1px solid var(--surface-200);
  border-radius: 6px;
  background: var(--surface-0);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-item:hover {
  background: var(--surface-50);
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.task-name {
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
}

.task-description {
  color: var(--text-color);
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
}

.task-meta-item {
  color: var(--text-color-secondary);
}

.task-meta-item strong {
  color: var(--text-color);
}

.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.fallback-case-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.loading-message {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.accordion-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.add-file-button {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.add-file-button:hover {
  opacity: 1;
  background-color: var(--surface-100) !important;
}
</style> 