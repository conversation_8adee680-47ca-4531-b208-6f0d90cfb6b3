<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import BravoInputGroup from '@services/ui-component-library/components/BravoInputGroup.vue'
import BravoInputMask from '@services/ui-component-library/components/BravoInputMask.vue'
import InputGroupAddon from 'primevue/inputgroupaddon'

// Interface for form data when creating a contact
interface CreateContactData {
  firstName: string
  lastName: string
  email: string
  phone: string
}

const props = defineProps<{
  visible: boolean
  onSubmit?: (contactData: CreateContactData) => Promise<void>
  onCancel?: () => void
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

// Form state
const firstName = ref('')
const lastName = ref('')
const email = ref('')
const phone = ref('')
const isLoading = ref(false)
const error = ref<string | null>(null)

// Add ref for the first name input to handle focus
const firstNameInputRef = ref()

// Track if fields have been touched (blurred)
const fieldsTouched = ref({
  email: false
})

// Validation error state
const validationErrors = ref({
  firstName: false,
  emailFormat: false
})

// Email validation regex
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

// Computed property for email validation
const isEmailValid = computed(() => {
  const emailValue = email.value.trim()
  return emailValue === '' || emailRegex.test(emailValue)
})

// Computed property to show email format error only when touched
const showEmailFormatError = computed(() => {
  return fieldsTouched.value.email && validationErrors.value.emailFormat
})

// Computed property for form validation
const isFormValid = computed(() => {
  return firstName.value.trim() !== '' && 
         isEmailValid.value
})

// Function to focus the first name input
function focusFirstNameInput() {
  // Target the specific input by ID
  const input = document.getElementById('first-name-input')
  if (input) {
    input.focus()
    return true
  }
  return false
}

// Watch for modal visibility changes
watch(() => props.visible, async (newValue) => {
  if (newValue) {
    resetForm()
    
    // Focus the first name input after a short delay to ensure the modal is fully rendered
    await nextTick()
    setTimeout(() => focusFirstNameInput(), 100)
    setTimeout(() => focusFirstNameInput(), 200)
    setTimeout(() => focusFirstNameInput(), 300)
  }
})

// Watch for form field changes to clear validation errors
watch(firstName, (newValue) => {
  if (newValue.trim() !== '') {
    validationErrors.value.firstName = false
  }
})

watch(email, (newValue) => {
  const emailValue = newValue.trim()
  // Clear format error if email becomes valid
  if (emailValue === '' || emailRegex.test(emailValue)) {
    validationErrors.value.emailFormat = false
  }
})

// Methods
function resetForm() {
  firstName.value = ''
  lastName.value = ''
  email.value = ''
  phone.value = ''
  error.value = null
  // Reset validation errors
  validationErrors.value = {
    firstName: false,
    emailFormat: false
  }
  // Reset touched state
  fieldsTouched.value = {
    email: false
  }
}

// Handle blur events to mark fields as touched and validate
function handleEmailBlur() {
  fieldsTouched.value.email = true
  const emailValue = email.value.trim()
  if (emailValue !== '' && !emailRegex.test(emailValue)) {
    validationErrors.value.emailFormat = true
  }
}

function validateForm() {
  const emailValue = email.value.trim()
  const errors = {
    firstName: firstName.value.trim() === '',
    emailFormat: emailValue !== '' && !emailRegex.test(emailValue)
  }
  
  validationErrors.value = errors
  
  // Return true if no errors
  return !Object.values(errors).some(error => error)
}

async function handleSubmit() {
  // Validate form first
  if (!validateForm()) {
    return // Stop submission if validation fails
  }
  
  if (!props.onSubmit) return
  
  const contactData: CreateContactData = {
    firstName: firstName.value.trim(),
    lastName: lastName.value.trim(),
    email: email.value.trim(),
    phone: phone.value.trim()
  }
  
  console.log('Contact data:', contactData)
  
  isLoading.value = true
  try {
    await props.onSubmit(contactData)
    emit('update:visible', false)
  } catch (error) {
    console.error('Error creating contact:', error)
    // Error handling is done in the parent component
  } finally {
    isLoading.value = false
  }
}

function handleCancel() {
  if (props.onCancel) {
    props.onCancel()
  }
  emit('update:visible', false)
}
</script>

<template>
  <BravoDialog
    :visible="visible"
    @update:visible="(value) => emit('update:visible', value)"
    header="Add Contact"
    :modal="true"
    :style="{ width: '500px', minHeight: '400px' }"
    :closable="true"
    :closeOnEscape="!isLoading"
    :dismissableMask="!isLoading"
  >
    <div class="add-contact-content">
      <div class="form-fields">
        <!-- First Name -->
        <div class="field">
          <BravoLabel text="First Name" isRequired />
          <BravoInputText
            id="first-name-input"
            v-model="firstName"
            placeholder="Enter first name..."
            :disabled="isLoading"
            :invalid="validationErrors.firstName"
            ref="firstNameInputRef"
            data-testid="first-name-input"
          />
        </div>

        <!-- Last Name -->
        <div class="field">
          <BravoLabel text="Last Name" />
          <BravoInputText
            id="last-name-input"
            v-model="lastName"
            placeholder="Enter last name..."
            :disabled="isLoading"
            data-testid="last-name-input"
          />
        </div>

        <!-- Email -->
        <div class="field">
          <BravoLabel text="Email" />
          <BravoInputGroup class="email-input">
            <InputGroupAddon>
              <i class="pi pi-envelope"></i>
            </InputGroupAddon>
            <BravoInputText
              id="email-input"
              v-model="email"
              type="email"
              placeholder="Enter email address..."
              :disabled="isLoading"
              :invalid="validationErrors.emailFormat"
              @blur="handleEmailBlur"
              data-testid="email-input"
            />
          </BravoInputGroup>
          <div v-if="showEmailFormatError" class="field-error">
            Please enter a valid email address
          </div>
        </div>

        <!-- Phone -->
        <div class="field">
          <BravoLabel text="Phone" />
          <BravoInputGroup class="phone-input">
            <InputGroupAddon>
              <i class="pi pi-phone"></i>
            </InputGroupAddon>
            <BravoInputMask
              id="phone-input"
              v-model="phone"
              mask="(*************"
              placeholder="(*************"
              :disabled="isLoading"
              data-testid="phone-input"
            />
          </BravoInputGroup>
        </div>
      </div>
      
      <div v-if="error" class="error-message">
        {{ error }}
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
          data-testid="cancel-contact-btn"
        />
        <BravoButton
          :label="isLoading ? 'Creating...' : 'Create Contact'"
          severity="primary"
          @click="handleSubmit"
          :loading="isLoading"
          :disabled="isLoading || !isFormValid"
          data-testid="create-contact-btn"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
.add-contact-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-error {
  font-size: 0.875rem;
  color: var(--red-500);
  margin-top: 0.25rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}

/* Ensure consistent button sizing */
.modal-footer .p-button {
  min-height: 40px;
}

/* Email and Phone input styling */
.email-input :deep(.p-inputgroup-addon) {
  background-color: var(--surface-100, #f8f9fa);
  border-color: var(--surface-300, #dee2e6);
  color: var(--text-color-secondary, #6c757d);
}

.phone-input :deep(.p-inputgroup-addon) {
  background-color: var(--surface-100, #f8f9fa);
  border-color: var(--surface-300, #dee2e6);
  color: var(--text-color-secondary, #6c757d);
}

.error-message {
  color: var(--red-600);
  font-size: 0.875rem;
  padding: 0.75rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 6px;
}
</style>
