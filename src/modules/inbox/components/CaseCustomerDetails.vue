<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import BravoAccordionHeader from '@services/ui-component-library/components/BravoAccordionHeader.vue'
import BravoBlock from '@services/ui-component-library/components/BravoBlock.vue'
import Drawer from 'primevue/drawer'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import { useSummarizerStore } from '@/stores/summarizer'
import { useMemberStore } from '@/stores/member'
import { useCasesStore } from '@/stores/cases'
import { useMemberAPI } from '@/composables/services/useMemberAPI'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoTitle3 from '@services/ui-component-library/components/BravoTypography/BravoTitle3.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import ZeroStateSearchSvg from '@/assets/zero-state-search.svg'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import CustomerInfoPanel from './customer-panels/CustomerInfoPanel.vue'
import CustomerLocationPanel from './customer-panels/CustomerLocationPanel.vue'
import CustomerProductsPanel from './customer-panels/CustomerProductsPanel.vue'
import CustomerCaseHistoryPanel from './customer-panels/CustomerCaseHistoryPanel.vue'
import CustomerContactsPanel from './customer-panels/CustomerContactsPanel.vue'
import ContactDrawerPanel from './customer-panels/ContactDrawerPanel.vue'
import ProductDrawerPanel from './customer-panels/ProductDrawerPanel.vue'
import AddContact from './AddContact.vue'

// Interface for form data when creating a contact
interface CreateContactData {
  firstName: string
  lastName: string
  email: string
  phone: string
}

const props = defineProps<{ issue: any }>()

const emit = defineEmits<{
  (e: 'update:issue', issue: any): void
}>()

const expandedIndex = ref<number>(0) // 0 = Customer section open by default
const localIssue = ref(props.issue)

// Drawer state for contact details
const contactDrawerOpen = ref(false)
const selectedContact = ref<any>(null)
const contactDrawerMode = ref<'detail' | 'list'>('detail')
const showContactBackButton = ref(false)

// Drawer state for product details
const productDrawerOpen = ref(false)
const selectedProduct = ref<any>(null)

// Add Contact modal state
const addContactModalVisible = ref(false)



const accordions = [
  { key: 'customerSummary', label: 'Customer Summary' },
  { key: 'customer', label: 'Customer' },
  { key: 'location', label: 'Location' },
  { key: 'contacts', label: 'Contacts' },
  { key: 'products', label: 'Products' },
  { key: 'caseHistory', label: 'Cases' }
]

const summarizerStore = useSummarizerStore()
const memberStore = useMemberStore() 
const casesStore = useCasesStore()
const memberAPI = useMemberAPI()

// Computed properties for counts
const contactsCount = computed(() => memberStore.memberUsers.length)
const productsCount = computed(() => memberStore.memberProducts.length)
const caseHistoryTotalCount = computed(() => casesStore.memberCasesTotalCount)
const caseHistoryOpenCount = computed(() => casesStore.memberCasesOpenCount)

// Watch for prop changes and update local issue
watch(() => props.issue, (newIssue) => {
  localIssue.value = newIssue
}, { deep: true, immediate: true })

const member = computed(() => localIssue.value?.member || {})
const memberUser = computed(() => localIssue.value?.memberUser || {})

const customerName = computed(() =>
  memberUser.value.full_name ||
  member.value.name_legal ||
  member.value.name ||
  '—'
)

const memberId = computed(() => localIssue.value?.member?.id)
const locationId = computed(() => localIssue.value?.location?.id)
const orgId = computed(() => localIssue.value?.member?.context_org_id)

// Handle issue updates from child components
const handleIssueUpdate = (updatedIssue: any) => {
  localIssue.value = updatedIssue
  emit('update:issue', updatedIssue)
}

// Handle contact selection for drawer (from contacts panel)
const handleContactClick = (contact: any) => {
  selectedContact.value = contact
  contactDrawerMode.value = 'detail'
  showContactBackButton.value = false // No back button when coming directly from contacts panel
  contactDrawerOpen.value = true
}

// Handle contact click from list mode (different from direct contact click)
const handleContactClickFromList = (contact: any) => {
  selectedContact.value = contact
  contactDrawerMode.value = 'detail'
  showContactBackButton.value = true
}

// Handle back to list
const handleBackToList = () => {
  contactDrawerMode.value = 'list'
  selectedContact.value = null
  showContactBackButton.value = false
}

// Close contact drawer
const closeContactDrawer = () => {
  contactDrawerOpen.value = false
  selectedContact.value = null
  contactDrawerMode.value = 'detail'
  showContactBackButton.value = false
}

// Handle product selection for drawer
const handleProductClick = (product: any) => {
  selectedProduct.value = product
  productDrawerOpen.value = true
}

// Close product drawer
const closeProductDrawer = () => {
  productDrawerOpen.value = false
  selectedProduct.value = null
}

// Handle email action
const handleEmailContact = (email: string) => {
  const link = document.createElement('a')
  link.href = `mailto:${email}`
  link.click()
}

// Handle phone action
const handleCallContact = (phone: string) => {
  const link = document.createElement('a')
  link.href = `tel:${phone}`
  link.click()
}

// Handle Add Contact modal
const handleAddContactClick = () => {
  addContactModalVisible.value = true
}

const handleAddContactSubmit = async (contactData: CreateContactData) => {
  try {
    console.log('Adding contact:', contactData)
    
    // Get the required IDs from the current issue
    const membersId = memberId.value
    const membersLocationsId = locationId.value
    const contextOrgId = orgId.value
    
    if (!membersId || !membersLocationsId || !contextOrgId) {
      throw new Error('Missing required member information')
    }
    
    // Create the contact using the API
    const newContact = await memberAPI.createContact({
      members_id: membersId,
      members_locations_id: membersLocationsId,
      context_org_id: contextOrgId,
      first_name: contactData.firstName,
      last_name: contactData.lastName,
      email: contactData.email,
      sms_number: contactData.phone
    })
    
    console.log('Contact created successfully:', newContact)
    
    // Refresh the member users list to show the new contact
    await memberStore.fetchMemberUsers({
      members_id: membersId,
      members_locations_id: membersLocationsId,
      context_org_id: contextOrgId
    })
    
    // Close the modal
    addContactModalVisible.value = false
    
  } catch (error) {
    console.error('Error creating contact:', error)
    // The error will be handled by the AddContact component
    throw error
  }
}

const handleAddContactCancel = () => {
  addContactModalVisible.value = false
}

// Handle contact updates from the drawer
const handleContactUpdated = async (updatedContact: any) => {
  console.log('🔄 Contact updated, refreshing data...', updatedContact)
  
  // If contact was deleted, close the drawer
  if (updatedContact.deleted) {
    console.log('🗑️ Contact was deleted, closing drawer')
    closeContactDrawer()
  } else {
    // Update the selectedContact to reflect changes in the drawer
    if (selectedContact.value && selectedContact.value.id === updatedContact.id) {
      selectedContact.value = { ...updatedContact }
    }
  }
  
  // Refresh the member users list to update the contact cards
  const membersId = memberId.value
  const membersLocationsId = locationId.value
  const contextOrgId = orgId.value
  
  if (membersId && membersLocationsId && contextOrgId) {
    try {
      await memberStore.fetchMemberUsers({
        members_id: membersId,
        members_locations_id: membersLocationsId,
        context_org_id: contextOrgId
      })
      console.log('✅ Member users list refreshed')
    } catch (error) {
      console.error('❌ Failed to refresh member users list:', error)
    }
  }
}

// Handle View All Contacts button click
const handleViewAllContacts = () => {
  console.log('🔍 View All Contacts clicked')
  contactDrawerMode.value = 'list'
  selectedContact.value = null
  contactDrawerOpen.value = true
}



// Flag to track if auto-expand has already been executed
const hasAutoExpanded = ref(false)

const creatingSummary = computed(() => summarizerStore.createLoading)
const createSummaryError = computed(() => summarizerStore.createError)

const sentimentSeverity = computed(() => {
  const sentiment = summarizerStore.customerSummary?.sentiment?.toLowerCase()
  switch (sentiment) {
    case 'positive':
      return 'success'
    case 'neutral':
      return 'warn'
    case 'negative':
      return 'danger'
    case 'unknown':
      return 'info'
    default:
      return 'info'
  }
})

function getCustomerId() {
  return localIssue.value?.member.id
}

watch(() => getCustomerId(), (customerId) => {
  if (customerId) {
    summarizerStore.loadCustomerSummary(customerId)
  }
})

onMounted(async () => {
  const customerId = getCustomerId()
  if (customerId) {
    summarizerStore.loadCustomerSummary(customerId)
  }
  
  await nextTick()
  // Auto-expand Customer Summary if not already done
  if (!hasAutoExpanded.value) {
    hasAutoExpanded.value = true
    // Small delay to ensure everything is rendered
    setTimeout(async () => {
      await nextTick()
      // Look for the Customer Summary accordion header
      const accordionHeaders = document.querySelectorAll('button.p-accordionheader')
      
      for (const header of accordionHeaders) {
        if (header.textContent?.includes('Customer Summary')) {
          console.log('🔧 Auto-expanding Customer Summary panel')
          ;(header as HTMLElement).click()
          break
        }
      }
    }, 100)
  }
})



async function handleCreateSummary() {
  if (!memberId.value || !orgId.value || !customerName.value) return
  // Use a default date range (last 3 years)
  const endDate = new Date().toISOString().split('T')[0] + 'T00:00:00.00Z'
  const startDate = new Date(new Date().setFullYear(new Date().getFullYear() - 3)).toISOString().split('T')[0] + 'T00:00:00.00Z'
  await summarizerStore.createCustomerSummary({
    orgId: orgId.value,
    customerId: memberId.value,
    customerName: customerName.value,
    startDate,
    endDate
  })
}
</script>
<template>
  <div class="case-customer-container" style="border-left-color: var(--border-color);">
    <!-- Fixed Header -->
    <div class="case-customer-header px-4 pt-5 pb-4 bg-white ">
      <BravoTitle1>Customer</BravoTitle1>
    </div>
    
    <!-- Scrollable Body -->
    <div class="case-customer-body">
      <div class="pb-8">
        <!-- Accordions -->
        <BravoBlock v-model="expandedIndex" multiple class="mt-2 w-full max-w-full overflow-hidden">
          <AccordionPanel v-for="(section, idx) in accordions" :key="section.key" :value="idx">
            <!-- Use BravoAccordionHeader for sections with counts -->
            <BravoAccordionHeader 
              v-if="section.key === 'contacts'"
              :count="contactsCount"
              :show-badge="true"
            >
              <span class="font-semibold text-slate-700">{{ section.label }}</span>
            </BravoAccordionHeader>
            
            <BravoAccordionHeader 
              v-else-if="section.key === 'products'"
              :count="productsCount"
              :show-badge="true"
            >
              <span class="font-semibold text-slate-700">{{ section.label }}</span>
            </BravoAccordionHeader>
            
            <BravoAccordionHeader 
              v-else-if="section.key === 'caseHistory'"
              :first-badge-count="caseHistoryTotalCount"
              :show-first-badge="true"
              middle-text="Open"
              :second-badge-count="caseHistoryOpenCount"
              :show-second-badge="true"
              second-badge-severity="ready"
            >
              <span class="font-semibold text-slate-700">{{ section.label }}</span>
            </BravoAccordionHeader>
            
            <!-- Use regular AccordionHeader for other sections -->
            <AccordionHeader v-else>
              <div v-if="section.key === 'summary'" class="case-pane-tab-header">{{ section.label }}</div>
              <span v-else class="font-semibold text-slate-700">{{ section.label }}</span>
            </AccordionHeader>
            <AccordionContent class="w-full max-w-full overflow-hidden">
              <!-- Customer Summary content -->
              <template v-if="section.key === 'customerSummary'">
                <div v-if="summarizerStore.loading" class="text-slate-500 italic">Loading summary...</div>
                <template v-if="summarizerStore.error || !summarizerStore.customerSummary">
                  <BravoZeroStateScreen
                    title="No Customer Summary"
                    message="No customer summary has been created yet. Generate a comprehensive summary of this customer's history and interactions."
                    buttonLabel="Create Customer Summary"
                    buttonIcon="pi pi-plus"
                    :imageSrc="ZeroStateSearchSvg"
                    imageAlt="No customer summary"
                    :actionHandler="handleCreateSummary"
                  />
                  <div v-if="creatingSummary" class="text-slate-500 italic mt-2">Creating summary. This could take a while...</div>
                  <div v-if="createSummaryError" class="text-red-500 mt-2">{{ createSummaryError }}</div>
                </template>
                <template v-else>
                  <div>
                    <!-- Summary -->
                    <div class="mb-5">
                      <BravoTitle3 class="section-title">Summary</BravoTitle3>
                      <div>
                        {{ summarizerStore.customerSummary.summary || summarizerStore.customerSummary.text || 'No summary available.' }}
                      </div>
                    </div>

                    <!-- Sentiment -->
                    <div class="mb-5">
                      <BravoTitle3 class="section-title">Sentiment</BravoTitle3>
                      <div class="flex items-center gap-2">
                        <BravoTag 
                          :severity="sentimentSeverity"
                          :value="summarizerStore.customerSummary.sentiment"
                        />
                        <span v-if="summarizerStore.customerSummary.most_common_issue" class="text-slate-600 text-sm">
                          <span class="font-medium">Most Common Issue:</span> {{ summarizerStore.customerSummary.most_common_issue }}
                        </span>
                      </div>
                    </div>

                    <!-- Engagement Level -->
                    <div v-if="summarizerStore.customerSummary.engagement_level" class="mb-5">
                      <BravoTitle3 class="section-title">Engagement Level</BravoTitle3>
                      <div>{{ summarizerStore.customerSummary.engagement_level }}</div>
                    </div>

                    <!-- Open Issues -->
                    <div v-if="summarizerStore.customerSummary.open_issues" class="mb-5">
                      <BravoTitle3 class="section-title">Open Issues</BravoTitle3>
                      <div v-if="summarizerStore.customerSummary.open_issues.length" class="ml-2">
                        <ul class="list-disc pl-4">
                          <li v-for="(issue, idx) in summarizerStore.customerSummary.open_issues" :key="idx">
                            {{ issue }}
                          </li>
                        </ul>
                      </div>
                      <div v-else class="ml-2">No open issues</div>
                    </div>

                    <!-- Trending Topics -->
                    <div v-if="summarizerStore.customerSummary.trending_topics" class="mb-5">
                      <BravoTitle3 class="section-title">Trending Topics</BravoTitle3>
                      <div class="flex flex-wrap gap-2">
                        <span 
                          v-for="(topic, idx) in summarizerStore.customerSummary.trending_topics" 
                          :key="idx"
                          class="bg-blue-100 text-blue-600 rounded px-2 py-0.5 text-xs font-semibold"
                        >
                          {{ topic }}
                        </span>
                      </div>
                    </div>

                    <!-- Suggestions -->
                    <div v-if="summarizerStore.customerSummary.suggestions" class="mb-5">
                      <BravoTitle3 class="section-title">Suggestions</BravoTitle3>
                      <ul class="list-disc pl-4 ml-2">
                        <li v-for="(suggestion, idx) in summarizerStore.customerSummary.suggestions" :key="idx">
                          {{ suggestion }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </template>
              </template>
              
              <!-- Other panel content -->
              <CustomerInfoPanel v-else-if="section.key === 'customer'" :issue="localIssue" @update:issue="handleIssueUpdate" />
              <CustomerLocationPanel v-else-if="section.key === 'location'" :issue="localIssue" @update:issue="handleIssueUpdate" />
              <template v-else-if="section.key === 'contacts'">
                <div class="flex justify-end mb-3">
                  <BravoButton
                    label="Add Contact"
                    severity="secondary"
                    size="small"
                    @click="handleAddContactClick"
                  />
                </div>
                <CustomerContactsPanel 
                  :issue="localIssue" 
                  @contact-click="handleContactClick"
                  @view-all-contacts="handleViewAllContacts"
                />
              </template>
              <CustomerProductsPanel v-else-if="section.key === 'products'" :issue="localIssue" @product-click="handleProductClick" />
              <CustomerCaseHistoryPanel v-else-if="section.key === 'caseHistory'" :issue="localIssue" />
            </AccordionContent>
                      </AccordionPanel>
          </BravoBlock>
      </div>
    </div>

    <!-- Contact Details Drawer -->
    <Drawer 
      v-model:visible="contactDrawerOpen" 
      :header="contactDrawerMode === 'list' ? 'All Contacts' : 'Contact Details'"
      position="left"
      class="contact-drawer"
      style="width: 400px"
    >
      <template #header>
        <div class="flex items-center justify-between w-full">
          <h3 class="text-lg font-semibold text-slate-700">
            {{ contactDrawerMode === 'list' ? 'All Contacts' : 'Contact Details' }}
          </h3>
        </div>
      </template>

      <ContactDrawerPanel 
        :contact="selectedContact"
        :mode="contactDrawerMode"
        :issue="localIssue"
        :show-back-button="showContactBackButton"
        @email-contact="handleEmailContact"
        @call-contact="handleCallContact"
        @contact-updated="handleContactUpdated"
        @contact-click="handleContactClickFromList"
        @back-to-list="handleBackToList"
      />
    </Drawer>

    <!-- Product Details Drawer -->
    <Drawer 
      v-model:visible="productDrawerOpen" 
      header="Product Details"
      position="left"
      class="product-drawer"
      style="width: 400px"
    >
      <template #header>
        <div class="flex items-center justify-between w-full">
          <h3 class="text-lg font-semibold text-slate-700">Product Details</h3>
        </div>
      </template>

      <ProductDrawerPanel :product="selectedProduct" />
    </Drawer>

    <!-- Add Contact Modal -->
    <AddContact 
      :visible="addContactModalVisible"
      @update:visible="(value) => addContactModalVisible = value"
      :onSubmit="handleAddContactSubmit"
      :onCancel="handleAddContactCancel"
    />


  </div>
</template>
<style scoped>
.case-customer-container {
  height: 100%;
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  border-left: 1px solid var(--border-color);
  overflow: hidden;
}
.case-customer-header {
  flex-shrink: 0;
  background: white;
  display: flex;
  align-items: center;
}
.case-customer-body {
  flex: 1;
  width: 100%;
  max-width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background: white;
  min-height: 0;
}
.min-h-full { min-height: 100%; }
.case-pane-tab-header {
  font-size: 1.125rem;
  font-weight: 600;
  color: #22223b;
  padding: 0.75rem 1rem 0.5rem 1rem;
  border-bottom: 2px solid #e5e7eb;
  background: #fff;
  margin-bottom: 0;
  letter-spacing: 0.01em;
}
.section-title {
  padding-bottom: 0.4rem;
}

/* Drawer styles */
.contact-drawer,
.product-drawer {
  z-index: 1100;
}
</style> 