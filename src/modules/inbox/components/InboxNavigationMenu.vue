<script setup lang="ts">
import PanelMenu from 'primevue/panelmenu'
import Badge from 'primevue/badge'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

interface SidebarItem {
  key?: string
  id?: string
  label: string
  count?: number
  icon?: string
  type?: 'item' | 'folder' | 'task'
  isOpen?: boolean
  children?: SidebarItem[]
  badge?: number | string
  items?: SidebarItem[]
  command?: () => void
  active?: boolean
  to?: any
  viewId?: string
}

// Props
defineProps<{
  model: SidebarItem[]
  expandedKeys: { [key: string]: boolean }
}>()

// Emits
defineEmits<{
  'update:expandedKeys': [keys: { [key: string]: boolean }]
}>()

const menuRef = ref<HTMLElement | null>(null)
const route = useRoute()

// Function to handle item clicks
function handleItemClick(event: Event, onClick?: any, item?: any) {
  // If this is a section header (has items), don't prevent default - let PrimeVue handle it
  if (item && item.items && item.items.length > 0) {
    if (onClick && typeof onClick === 'function') {
      onClick(event)
    }
    return
  }

  // For regular menu items, prevent default behavior
  event.preventDefault()

  // Call the provided onClick handler if it exists
  if (onClick && typeof onClick === 'function') {
    onClick(event)
  }
}

// Function to update active states based on current route
function updateActiveStatesFromRoute(): boolean {
  if (!menuRef.value) {
    return false
  }
  
  // Remove active class from all menu items
  const allLinks = menuRef.value.querySelectorAll('a, .p-menuitem-link, .p-panelmenu-item-link')
  allLinks.forEach(link => {
    link.classList.remove('inbox-active-item')
  })
  
  const view = route.query.view as string | undefined
  const object = route.query.object as string | undefined
  const path = route.path
  let foundActiveItem = false
  
  // Find and activate the matching menu item
  allLinks.forEach(link => {
    const linkElement = link as HTMLElement
    const linkText = linkElement.textContent?.trim()
    const itemViewId = linkElement.getAttribute('data-view-id')
    
    // Skip section headers
    const isInHeader = !!linkElement.closest('.p-panelmenu-header')
    const sectionHeaders = ['Navigation', 'Cases', 'Tasks']
    const isSectionHeader = isInHeader && sectionHeaders.includes(linkText || '')
    
    if (isSectionHeader) {
      return
    }
    
    // Match based on route and menu item
    let shouldActivate = false
    
    if (path === '/inbox' || path.startsWith('/inbox/')) {
      if (itemViewId) {
        // Exact match between view query param and item viewId
        if (view && itemViewId === view) {
          shouldActivate = true
        }
        // Handle case where no view is specified - default to home or inbox ONLY on exact /inbox route
        else if (!view && itemViewId && !object && path === '/inbox') {
          shouldActivate = itemViewId === 'home' || itemViewId === 'inbox'
        }
      }
    }
    
    if (shouldActivate) {
      linkElement.classList.add('inbox-active-item')
      foundActiveItem = true
    }
  })
  
  return foundActiveItem
}

// Watch for route changes and update active states
watch(
  () => [route.query.view, route.query.object, route.path],
  () => {
    updateActiveStatesWithRetry()
  },
  { immediate: true }
)

// Function to retry active state updates with delays
function updateActiveStatesWithRetry(attempt = 1, maxAttempts = 5) {
  const delay = attempt * 100
  
  setTimeout(() => {
    const success = updateActiveStatesFromRoute()
    
    if (!success && attempt < maxAttempts) {
      updateActiveStatesWithRetry(attempt + 1, maxAttempts)
    }
  }, delay)
}

// Setup click handlers and initial active states
onMounted(() => {
  setTimeout(() => {
    if (menuRef.value) {
      setupClickHandlers()
      updateActiveStatesFromRoute()
    }
  }, 300)
})

function setupClickHandlers() {
  const allLinks = menuRef.value?.querySelectorAll('a.menu-item, a') || []

  allLinks.forEach((link) => {
    const linkText = link.textContent?.trim()
    
    // Skip section headers or links that already have handlers
    const sectionHeaders = ['Navigation', 'Cases', 'Tasks']
    const isSectionHeader = link.closest('.p-panelmenu-header') && sectionHeaders.includes(linkText || '')
    if (isSectionHeader || link.hasAttribute('data-menu-click-handled')) {
      return
    }

    link.setAttribute('data-menu-click-handled', 'true')

    link.addEventListener('click', () => {
      if (!link.closest('.p-panelmenu-header')) {
        // Remove active class from all links
        document.querySelectorAll('.inbox-active-item').forEach((el) => {
          el.classList.remove('inbox-active-item')
        })

        // Add active class to this link
        link.classList.add('inbox-active-item')
      }
    })
  })
}

// Expose method for manual updates
defineExpose({
  updateActiveStates: () => updateActiveStatesFromRoute()
})
</script>

<template>
  <div ref="menuRef" class="inbox-navigation-menu">
    <PanelMenu
      class="inbox-panel-menu"
      :model="model"
      :expandedKeys="expandedKeys"
      @update:expandedKeys="$emit('update:expandedKeys', $event)"
      expand-icon="pi pi-angle-right"
      collapse-icon="pi pi-angle-down"
      multiple
    >
      <template #item="{ item }">
        <a
          class="menu-item flex items-center justify-between px-4 py-2 cursor-pointer"
          :data-view-id="item.viewId || item.id"
          href="javascript:void(0)"
          @click="(event) => handleItemClick(event, item.command, item)"
        >
          <div class="flex items-center menu-item-content">
            <span
              v-if="item.icon"
              :class="[item.icon]"
              class="menu-item-icon"
            ></span>
            <span
              v-if="!item.items"
              class="ml-2 menu-item-label"
            >{{ item.label }}</span>
            <span
              v-else
              class="font-semibold header-label menu-item-label"
            >{{ item.label }}</span>
          </div>
          <div class="flex items-center menu-item-actions">
            <BravoSkeleton
              v-if="item.badge === 'loading'"
              width="24px"
              height="14px"
              border-radius="4px"
              class="badge-skeleton"
            />
            <Badge
              v-else-if="item.badge && item.badge !== 'loading'"
              :value="item.badge"
              class="custom-badge"
            />
            <i
              v-if="item.items"
              class="pi pi-angle-right custom-submenu-icon"
            ></i>
          </div>
        </a>
      </template>
    </PanelMenu>
  </div>
</template>

<style lang="scss" scoped>
.inbox-navigation-menu {
  width: 100%;
}

.inbox-panel-menu {
  /* General styling */
  .custom-submenu-icon {
    font-size: 16px;
    margin-left: 8px;
    transition: transform 0.3s;
    color: var(--text-color-primary) !important;
  }

  /* Target header icons - dynamic state handling through CSS */
  :deep(.p-panelmenu-header) {
    &[aria-expanded='true'] {
      .custom-submenu-icon {
        color: var(--text-color-primary) !important;
        transform: rotate(90deg);
      }
    }
  }

  /* Custom header label styling */
  .header-label {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-color-primary);
    text-transform: uppercase;
    margin: 0;
    padding: 0;
  }

  /* Badge styling */
  :deep(.p-badge),
  :deep(.custom-badge) {
    background-color: transparent !important;
    color: var(--text-color-secondary, #6c757d) !important;
    font-size: 14px !important;
    font-weight: normal !important;
    padding: 0 !important;
    min-width: auto !important;
    height: auto !important;
  }

  /* Badge skeleton styling */
  .badge-skeleton {
    margin-left: 8px;
  }

  /* Bold badge when in selected state */
  :deep(a.inbox-active-item .custom-badge),
  :deep(.inbox-active-item .custom-badge),
  :deep(.menu-item.inbox-active-item .custom-badge) {
    font-weight: 700 !important;
    color: var(--text-color-secondary) !important;
  }

  :deep(.p-panelmenu-icon) {
    margin-right: 0.5rem;
  }

  :deep(.p-panelmenu-panel) {
    border: none;
    outline: none;
    background-color: var(--surface-50);
  }

  /* Submenu icons - hide PrimeVue's default ones */
  :deep(.p-submenu-icon) {
    display: none !important;
  }

  /* Active state styling */
  :deep(a.inbox-active-item),
  :deep(.inbox-active-item),
  :deep(.menu-item.inbox-active-item),
  :deep(.p-panelmenu-content a.inbox-active-item),
  :deep(.p-panelmenu-content .inbox-active-item) {
    background-color: white !important;
    font-weight: 600 !important;
    position: relative;
    border-radius: 8px;
  }

  :deep(.p-panelmenu-item-link) {
    padding-left: 14px;
    padding-right: 14px;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  /* Header link styling */
  :deep(.p-panelmenu-header-link) {
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 10px;
    padding-bottom: 10px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;

    /* Ensure header links don't get the active/highlight styling */
    &.p-highlight,
    &.inbox-active-item {
      background-color: transparent !important;
      font-weight: normal !important;
      color: inherit !important;
    }
  }

  /* Hide Navigation section header specifically */
  :deep(.p-panelmenu-header-link:has(.header-label)) {
    .header-label {
      &:first-child {
        /* Check if this is the Navigation header by text content */
        &:is([data-section="navigation"], :contains("Navigation")) {
          display: none;
        }
      }
    }
  }

  /* Alternative approach - hide Navigation header by targeting the first panel */
  :deep(.p-panelmenu-panel:first-child .p-panelmenu-header) {
    display: none;
  }

  /* Menu item styling */
  :deep(.menu-item),
  :deep(a.menu-item) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    cursor: pointer;
    background-color: var(--surface-50);
    border-radius: 8px;
    transition: background-color 0.2s, color 0.2s;

    &:hover {
      background-color: white;
    }
  }

  /* Menu item content and actions layout */
  :deep(.menu-item-content) {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
  }

  :deep(.menu-item-actions) {
    flex-shrink: 0;
    margin-left: 8px;
  }

  /* Menu item label truncation */
  :deep(.menu-item-label) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
  }

  /* Icon spacing */
  :deep(.menu-item-icon) {
    flex-shrink: 0;
    margin-right: 8px;
  }

  /* Override the ml-2 margin when using truncation */
  :deep(.menu-item-content .menu-item-label.ml-2) {
    margin-left: 0;
  }

  /* Add background-color to all menu items */
  :deep(.p-panelmenu-item-link),
  :deep(.p-menuitem-link) {
    background-color: var(--surface-50);
  }

  /* Hover effects */
  :deep(a:hover),
  :deep(.menu-item:hover),
  :deep(.p-panelmenu-item-link:hover),
  :deep(.p-menuitem-link:hover) {
    background-color: white;
    border-radius: 8px;
  }

  :deep(.p-panelmenu-submenu .p-panelmenu-root-list) {
    padding-left: 0 !important;
  }

  :deep(.p-panelmenu-content) {
    .p-submenu-list,
    .p-panelmenu-root-list,
    ul {
      padding-left: 0 !important;
      margin-left: 0 !important;
    }
  }
}
</style> 