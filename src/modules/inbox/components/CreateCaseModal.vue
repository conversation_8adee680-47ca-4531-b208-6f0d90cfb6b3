<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import BravoSelectField from '@services/ui-component-library/components/BravoSelectField.vue'
import BravoToast from '@services/ui-component-library/components/BravoToast.vue'
import AutoComplete from 'primevue/autocomplete'
import { useToast } from 'primevue/usetoast'
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { MemberLocation } from '../../../composables/services/useMemberAPI'
import { useCasesStore } from '../../../stores/cases'
import { useMemberStore } from '../../../stores/member'

// Define props
const props = withDefaults(defineProps<{
  visible: boolean
  prefilledData?: {
    members_locations_id: string
    members_users_id: string
    members_id?: string
    context_org_id?: string
    locationData?: any // Add location data to the interface
  } | null
}>(), {
  visible: false,
  prefilledData: null
})

// Define emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'case-created': [newCase: any]
}>()

// Stores
const memberStore = useMemberStore()
const casesStore = useCasesStore()
const userStore = useUserStore()
const toast = useToast()
const router = useRouter()
const bravoToast = ref()

// Form state
const addCaseForm = ref<{
  caseName: string
  ownerTeam: string | null
  customerLocation: MemberLocation | null
  contact: any | null
}>({
  caseName: '',
  ownerTeam: null,
  customerLocation: null,
  contact: null
})

// Other state
const customerSearchQuery = ref('')
const locationMemberUsers = ref<any[]>([])
const loadingMemberUsers = ref(false)
const creatingCase = ref(false)
const locationPartnerTeams = ref<any[]>([])

// Create options for owner team dropdown using location-specific teams
const ownerTeamOptions = computed(() => {
  return locationPartnerTeams.value.map(team => ({
    label: team.lbl,
    value: team.val
  }))
})

// Create options for member users dropdown
const memberUserOptions = computed(() => {
  return locationMemberUsers.value.map(user => {
    // Use the full_name field or construct from first_name and last_name
    const label = user.full_name || `${user.first_name} ${user.last_name}`.trim() || user.email || 'Unknown User'
    
    return {
      label,
      value: user.id
    }
  })
})

// Helper function to strip HTML tags (same as in ChangeCustomerModal)
const stripHtml = (html: string) => {
  if (!html) return ''
  const div = document.createElement('div')
  div.innerHTML = html
  return div.textContent || div.innerText || ''
}

// Get clean label for customer location (same as in ChangeCustomerModal)
const getCleanCustomerLabel = (option: any) => {
  const name = stripHtml(option.c__name) || stripHtml(option.site_name) || 'Unknown Customer'
  // Add location ID for debugging
  return `${name} (ID: ${option.id})`
}

// Handle customer search
const onCustomerFilter = async (event: any) => {
  const query = event.query || ''
  customerSearchQuery.value = query
  
  if (query && query.length >= 2) {
    try {
      await memberStore.searchCustomerLocations(query)
    } catch (error) {
      console.error('Error searching customers:', error)
    }
  } else if (query.length === 0) {
    // Clear results when search is empty
    memberStore.clearCustomerLocations()
  }
}

// Watch for prefilled data to auto-populate form
watch(() => props.prefilledData, async (newPrefilledData) => {
  if (newPrefilledData && props.visible) {
    console.log('🔧 Prefilling case form with data:', newPrefilledData)
    console.log('🔍 Location data in prefilled:', newPrefilledData.locationData)
    
    try {
      // Try to find the location by searching with the location ID
      if (newPrefilledData.members_locations_id && newPrefilledData.members_users_id) {
        console.log('🔍 Searching for location by ID:', newPrefilledData.members_locations_id)
        
        try {
          // Search for the location using the location ID
          await memberStore.searchCustomerLocations(newPrefilledData.members_locations_id)
          
          // Look for the location in the search results
          const foundLocation = memberStore.customerLocations.find(loc => 
            loc.id === newPrefilledData.members_locations_id
          )
          
          if (foundLocation) {
            console.log('✅ Found location via search:', foundLocation)
            addCaseForm.value.customerLocation = foundLocation
          } else {
            console.log('⚠️ Location not found in search results, trying fallback approach')
            
            // Fallback: Use the provided members_id and context_org_id from the enhanced data
            const membersId = newPrefilledData.members_id || userStore.userData?.context_org_id || ''
            const contextOrgId = newPrefilledData.context_org_id || userStore.userData?.context_org_id || ''
            
            // Create a minimal location object with the correct IDs
            const fallbackLocation: MemberLocation = {
              id: newPrefilledData.members_locations_id,
              members_id: membersId,
              originating_partners_id: contextOrgId,
              c__name: newPrefilledData.locationData?.c__name || newPrefilledData.locationData?.site_name || newPrefilledData.locationData?.name || `Location ${newPrefilledData.members_locations_id}`,
              site_name: newPrefilledData.locationData?.site_name || newPrefilledData.locationData?.c__name || newPrefilledData.locationData?.name || `Location ${newPrefilledData.members_locations_id}`,
              // Add minimal required fields with defaults
              sub_account_id: '',
              segment: [],
              street_1: '',
              street_2: '',
              city: '',
              state_id: 0,
              zipcode: '',
              country_id: 0,
              latitude: 0,
              longitude: 0,
              time_zone: '',
              contact: '',
              phone: null,
              notes: '',
              site_id: 0,
              merchant_ids: '',
              created: '',
              updated: '',
              status: 1,
              active: true,
              external_id: null,
              state: '',
              country: '',
              device_active_cnt: 0,
              device_inactive_cnt: 0,
              issues_open_cnt: 0,
              issues_total_cnt: 0,
              c__address: '',
              c__address_multiline: '',
              c__nickname: '',
              c__email: null,
              c__contact_email: null,
              c__lbl: '',
              c__d_state_id: '',
              c__case_summary: '',
              c__location_count: 0,
              c__user_count: 0,
              c__technology_count: 0,
              c__d_status: '',
              c__phone: null,
              _highlighted: false,
              _highlightmap: {},
              avatar: ''
            } as MemberLocation
            
            console.log('🔧 Using fallback location:', fallbackLocation)
            addCaseForm.value.customerLocation = fallbackLocation
          }
        } catch (searchError) {
          console.error('❌ Error searching for location:', searchError)
          // Continue with member users fetch even if location search fails
        }
        
        // Fetch member users for the location
        const membersId = newPrefilledData.members_id || userStore.userData?.context_org_id || ''
        const contextOrgId = newPrefilledData.context_org_id || userStore.userData?.context_org_id || ''
        
        console.log('Using IDs for prefill:', { membersId, contextOrgId })
        
        const memberUsersResponse = await memberStore.fetchMemberUsers({
          members_id: membersId,
          members_locations_id: newPrefilledData.members_locations_id,
          context_org_id: contextOrgId
        })
        
        console.log('Member users response:', memberUsersResponse)
        
        // Find the specific contact in the response
        const selectedContact = memberUsersResponse.find(user => user.id === newPrefilledData.members_users_id)
        
        if (selectedContact) {
          console.log('✅ Found contact:', selectedContact)
          
          // Set the member users directly
          locationMemberUsers.value = memberUsersResponse
          
          // Set the contact
          console.log('🔧 Setting contact directly:', newPrefilledData.members_users_id)
          addCaseForm.value.contact = newPrefilledData.members_users_id
          
          // Fetch location-specific teams
          try {
            const { useMetaAPI } = await import('@/composables/services/useMetaAPI')
            const metaAPI = useMetaAPI()
            
            const teamResponse = await metaAPI.fetchMembersLocationsPartners({
              members_locations_id: newPrefilledData.members_locations_id
            })
            
            console.log('Location teams response:', teamResponse)
            locationPartnerTeams.value = teamResponse.pl__members_partners_teams || []
          } catch (teamError) {
            console.error('Error fetching teams:', teamError)
            locationPartnerTeams.value = []
          }
          
        } else {
          console.warn('⚠️ Contact not found in member users response')
        }
      }
      
    } catch (error) {
      console.error('Error prefilling case form:', error)
    }
  }
}, { immediate: true })

// Watch for customer location changes to fetch member users and location-specific teams
watch(() => addCaseForm.value.customerLocation, async (newLocation) => {
  if (newLocation?.id) {
    console.log('Selected customer location:', newLocation)
    loadingMemberUsers.value = true
    try {
      // Fetch member users for the selected location using the correct API endpoint
      const response = await memberStore.fetchMemberUsers({
        members_id: newLocation.members_id,
        members_locations_id: newLocation.id,
        context_org_id: newLocation.originating_partners_id || userStore.userData?.context_org_id
      })
      console.log('Member users response:', response)
      
      locationMemberUsers.value = response || []

      // Fetch location-specific teams using the new API
      console.log('Fetching location-specific teams for location:', newLocation.id)
      const { useMetaAPI } = await import('@/composables/services/useMetaAPI')
      const metaAPI = useMetaAPI()
      
      const teamResponse = await metaAPI.fetchMembersLocationsPartners({
        members_locations_id: newLocation.id
      })
      
      console.log('Location teams response:', teamResponse)
      locationPartnerTeams.value = teamResponse.pl__members_partners_teams || []
      console.log('Location partner teams set to:', locationPartnerTeams.value)
      
      // If we have prefilled data and member users are now loaded, set the contact
      if (props.prefilledData?.members_users_id && locationMemberUsers.value.length > 0) {
        const contactExists = locationMemberUsers.value.some(user => user.id === props.prefilledData?.members_users_id)
        if (contactExists) {
          console.log('🔧 Setting prefilled contact after member users loaded:', props.prefilledData.members_users_id)
          addCaseForm.value.contact = props.prefilledData.members_users_id
        } else {
          console.warn('⚠️ Prefilled contact not found in member users list:', props.prefilledData.members_users_id)
        }
      }
      
    } catch (error) {
      console.error('Error fetching data for location:', error)
      locationMemberUsers.value = []
      locationPartnerTeams.value = []
    } finally {
      loadingMemberUsers.value = false
    }
  } else {
    // Clear data when no location is selected
    locationMemberUsers.value = []
    locationPartnerTeams.value = []
    
    // Only clear contact if we're not in a prefilled scenario
    if (!props.prefilledData) {
      addCaseForm.value.contact = null
    }
    addCaseForm.value.ownerTeam = null
  }
}, { deep: true })

// Handle dialog visibility changes
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // Reset form when dialog opens
    resetForm()
    
    // Focus the first field after dialog is fully rendered
    setTimeout(() => {
      const caseNameInput = document.getElementById('caseName')
      if (caseNameInput) {
        caseNameInput.focus()
      }
    }, 100)
  }
})

// Reset form data
const resetForm = () => {
  addCaseForm.value = {
    caseName: '',
    ownerTeam: null,
    customerLocation: null,
    contact: null
  }
  customerSearchQuery.value = ''
  // Clear previous search results
  memberStore.clearCustomerLocations()
  // Clear location-specific data
  locationMemberUsers.value = []
  locationPartnerTeams.value = []
}

// Handle save case
const handleSaveCase = async () => {
  if (!addCaseForm.value.caseName.trim() || 
      !addCaseForm.value.customerLocation || 
      !addCaseForm.value.contact || 
      !addCaseForm.value.ownerTeam) {
    console.error('Missing required fields for case creation')
    return
  }

  creatingCase.value = true
  
  try {
    const createParams = {
      display_name: addCaseForm.value.caseName,
      members_id: addCaseForm.value.customerLocation.members_id,
      members_locations_id: addCaseForm.value.customerLocation.id,
      members_users_id: addCaseForm.value.contact,
      owner_partners_teams_id: addCaseForm.value.ownerTeam,
      type: 2, // Default case type
      status: 0, // Default status
      comments: ''
    }
    
    console.log('Creating case with params:', createParams)
    
    // Create the case using the cases store
    const newCase = await casesStore.createCase(createParams)
    
    console.log('Case created successfully:', newCase)
    
    // Extract the case ID from the API response structure
    let caseId = newCase?.id
    if (!caseId && (newCase as any)?.results && Array.isArray((newCase as any).results) && (newCase as any).results.length > 0) {
      caseId = (newCase as any).results[0]?.id
    }
    if (!caseId && (newCase as any)?.issue?.id) {
      caseId = (newCase as any).issue.id
    }
    
    console.log('Extracted case ID:', caseId)
    console.log('Full case object structure:', JSON.stringify(newCase, null, 2))
    
    // Show success toast with case name and action links
    showCaseCreatedToast({ id: caseId }, addCaseForm.value.caseName)
    
    // Emit events
    emit('case-created', newCase)
    emit('update:visible', false)
    
  } catch (error) {
    console.error('Failed to create case:', error)
    // TODO: Show error message to user
    // For now, we'll keep the dialog open so they can try again
  } finally {
    creatingCase.value = false
  }
}

// Handle cancel
const handleCancelCase = () => {
  emit('update:visible', false)
}

// Handle view case action
const handleViewCase = (caseUrl: string) => {
  router.push(caseUrl)
}



// Show success toast with case creation details and action links
const showCaseCreatedToast = (newCase: any, caseName: string) => {
  console.log('Showing case created toast for:', caseName, newCase)
  
  const caseUrl = `/inbox/cases/${newCase.id}`
  const fullUrl = `${window.location.origin}${caseUrl}`
  
  console.log('Case URL:', caseUrl)
  console.log('Full URL:', fullUrl)
  
  // Use the new BravoToast addToastWithActions method
  if (bravoToast.value) {
          bravoToast.value.addToastWithActions({
        severity: 'success',
        summary: `Case Created`,
        detail: `You have created the case '${caseName}'`,
      life: 8000,
      actions: [
        {
          label: 'View',
          url: caseUrl, // Use URL for automatic Ctrl+click support
          style: 'primary'
        },
                  {
            label: 'Copy Link',
            action: () => {
              navigator.clipboard.writeText(fullUrl);
            },
            style: 'secondary'
          }
      ]
    })
  } else {
    // Fallback to regular toast if BravoToast ref not available
    console.warn('BravoToast ref not available, falling back to regular toast')
    toast.add({
      severity: 'success',
      summary: `Case Created`,
      detail: `You have created the case '${caseName}'`,
      life: 5000
    })
  }
}
</script>

<template>
  <BravoDialog
    :visible="props.visible"
    @update:visible="(value) => emit('update:visible', value)"
    header="Add a Case"
    :modal="true"
    :style="{ width: '32rem' }"
    :closable="true"
    position="center"
  >
    <div class="add-case-form">
      <div class="form-field">
        <BravoLabel text="Case Name" forElement="caseName" isRequired />
        <BravoInputText
          id="caseName"
          v-model="addCaseForm.caseName"
          placeholder="Enter case name"
          class="w-full"
          autofocus
        />
      </div>

      <div class="form-field">
        <BravoLabel text="Customer Location" forElement="customerLocation" isRequired />
        <AutoComplete
          id="customerLocation"
          v-model="addCaseForm.customerLocation"
          :suggestions="memberStore.customerLocations"
          :optionLabel="getCleanCustomerLabel"
          placeholder="Type to search customers..."
          :loading="memberStore.loadingCustomerLocations"
          :class="['w-full', { 'p-invalid': !addCaseForm.customerLocation }]"
          @complete="onCustomerFilter"
          :showClear="true"
          :minLength="2"
          fluid
        >
          <template #option="slotProps">
            <div class="customer-option">
              <div class="customer-main-info">
                <div class="customer-name" v-html="slotProps.option.c__name || slotProps.option.site_name"></div>
                <div class="customer-details">
                  <span class="customer-address">{{ slotProps.option.c__address }}</span>
                  <span v-if="slotProps.option.merchant_ids" class="merchant-id">
                    MID: <span v-html="slotProps.option.merchant_ids"></span>
                  </span>
                </div>
              </div>
              <div class="customer-meta">
                <div class="customer-id">#{{ slotProps.option.id }}</div>
                <div v-if="slotProps.option.c__phone" class="customer-phone">
                  {{ slotProps.option.c__phone }}
                </div>
              </div>
            </div>
          </template>
          
          <template #empty>
            <div class="empty-state">
              <div v-if="customerSearchQuery.length < 2">
                Type at least 2 characters to search
              </div>
              <div v-else>
                No customers found for "{{ customerSearchQuery }}"
              </div>
            </div>
          </template>
        </AutoComplete>
      </div>

      <div class="form-field">
        <BravoLabel text="Contact" forElement="contact" isRequired />
        <BravoSelectField
          id="contact"
          v-model="addCaseForm.contact"
          :options="memberUserOptions"
          placeholder="Select contact"
          class="w-full"
          dataTestId="contact-select"
          :disabled="!addCaseForm.customerLocation || loadingMemberUsers"
          :loading="loadingMemberUsers"
        />
      </div>

      <div class="form-field">
        <BravoLabel text="Owner Team" forElement="ownerTeam" isRequired />
        <BravoSelectField
          id="ownerTeam"
          v-model="addCaseForm.ownerTeam"
          :options="ownerTeamOptions"
          placeholder="Select owner team"
          class="w-full"
          dataTestId="owner-team-select"
          :disabled="!addCaseForm.customerLocation"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancelCase"
        />
        <BravoButton
          label="Create Case"
          @click="handleSaveCase"
          :loading="creatingCase"
          :disabled="!addCaseForm.caseName.trim() || 
                    !addCaseForm.customerLocation || 
                    !addCaseForm.contact || 
                    !addCaseForm.ownerTeam || 
                    creatingCase"
        />
      </div>
    </template>
  </BravoDialog>

  <!-- Enhanced BravoToast for action support -->
  <BravoToast ref="bravoToast" group="bravo-actions" position="top-right" />
</template>

<style scoped>
/* Add Case Dialog Styles */
.add-case-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.form-field {
  display: flex;
  flex-direction: column;
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}

.w-full {
  width: 100%;
}

/* Customer search styles (from ChangeCustomerModal) */
.customer-option {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem;
  gap: 1rem;
}

.customer-main-info {
  flex: 1;
  min-width: 0;
}

.customer-name {
  font-weight: 600;
  color: var(--surface-900);
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.customer-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.customer-address {
  font-size: 0.875rem;
  color: var(--surface-600);
  line-height: 1.3;
}

.merchant-id {
  font-size: 0.75rem;
  color: var(--primary-600);
  font-weight: 500;
  font-family: monospace;
}

.customer-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.125rem;
  flex-shrink: 0;
}

.customer-id {
  font-size: 0.75rem;
  color: var(--surface-500);
  font-family: monospace;
}

.customer-phone {
  font-size: 0.75rem;
  color: var(--surface-600);
}

.empty-state {
  padding: 1rem;
  text-align: center;
  color: var(--surface-500);
}


</style> 