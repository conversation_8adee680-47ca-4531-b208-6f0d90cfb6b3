<script setup lang="ts">
import { ref, onMounted, computed, watchEffect, watch, nextTick, onUnmounted } from 'vue';
import { useCasesStore } from '../../../stores/cases';
import { useUserStore } from '@/stores/user';
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoTitle3 from '@services/ui-component-library/components/BravoTypography/BravoTitle3.vue';
import BravoSubhead from '@services/ui-component-library/components/BravoTypography/BravoSubhead.vue';
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue';
import BravoBodyBold from '@services/ui-component-library/components/BravoTypography/BravoBodyBold.vue';
import BravoFilterSelect from '@services/ui-component-library/components/BravoFilterSelect.vue';
import BravoFilterMultiSelect from '@services/ui-component-library/components/BravoFilterMultiSelect.vue';
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue';
import type { Issue } from '../../../services/IssuesAPI';
import type { SidebarItem } from './InboxViewsList.vue';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import { useRoute, RouterLink, useRouter } from 'vue-router';
import Tooltip from 'primevue/tooltip';
import BravoDataTable from '@services/ui-component-library/components/BravoDataTable.vue';
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue';
import { getCaseIcon, getCaseIconLabel } from '../utils/caseHelper';
import { useIssuesAPI } from '@/composables/services/useIssuesAPI';
import { usePubsubEventsStore } from '@/stores/pubsubEvents';
import { useMetaAPI } from '@/composables/services/useMetaAPI';
import { useToast } from 'primevue/usetoast';

// Register the tooltip directive
const vTooltip = Tooltip;

// Define props
interface Props {
    selectedView: SidebarItem | null;
    isYourInbox?: boolean;
    initialLoading?: boolean;
    selectedIssueId?: string;
    sidebarWidth?: number;
}

const props = withDefaults(defineProps<Props>(), {
    isYourInbox: false,
    initialLoading: false,
    selectedIssueId: '',
    sidebarWidth: 280,
});
const emit = defineEmits<{
    (e: 'select-issue', issue: Issue): void;
    (e: 'create-case'): void;
}>();

const casesStore = useCasesStore();
const userStore = useUserStore();
const issuesAPI = useIssuesAPI();
const pubsubEventsStore = usePubsubEventsStore();
const metaAPI = useMetaAPI();
const cases = ref<Issue[]>([]);
const error = ref<string | null>(null);
const isViewLoading = ref(false);
const isViewChanging = ref(false);
const route = useRoute();
const router = useRouter();

// Track which cases have new chat notifications
const caseNotifications = ref<Set<string>>(new Set());

// Track new claimable cases
const hasNewClaimableCases = ref(false);

// User data for owner avatars
const ownerUsers = ref<Map<string, any>>(new Map());
const loadingUsers = ref(false);

// Infinite scroll state
const loadingMoreCases = ref(false);
const currentPage = ref(1);
const totalCases = ref(0);
const casesPerPage = ref(25);
const casesListRef = ref<HTMLElement | null>(null);

// Computed property for checking if there are more cases to load
const hasMoreCases = computed(() => cases.value.length < totalCases.value);

// Check if we have a view selected
const hasSelectedView = computed(() => !!props.selectedView);

// Find the view details in the store based on selectedView ID
const viewDetails = computed(() => {
    if (!props.selectedView) return null;

    // Cast the store to access views
    const storeWithViews = casesStore as any;
    const views = storeWithViews.views || [];

    // Find the view with matching ID
    const foundView = views.find((view: any) => view.id === props.selectedView?.id);

    return foundView;
});

// Handle case selection
const selectCase = (issue: Issue) => {
    // Clear notification for the selected case
    clearNotificationForCase(issue.id);
    emit('select-issue', issue);
};

// Defensive function to ensure we always have a display name
const getCaseDisplayName = (issue: Issue) => {
    return issue.display_name || issue.c__contact_email || issue.m__contact_phone || 'No case name...';
};

// Fetch cases for the selected view with pagination support
const fetchCasesForView = async (loadMore = false) => {
    if (!viewDetails.value) return;

    // Set appropriate loading state
    if (loadMore) {
        loadingMoreCases.value = true;
    } else {
        error.value = null;
    }

    try {
        // Get filter params from the view if they exist
        const filterParams = viewDetails.value.filters || [];

        // Create the params object based on available properties
        const params: any = {
            page: currentPage.value,
            limit: casesPerPage.value,
            start: (currentPage.value - 1) * casesPerPage.value,
        };

        // Use the selected sort instead of view's default sort
        if (selectedSort.value) {
            const [property, direction] = selectedSort.value.split('_');
            params.sort = [{ property: property, direction: direction }];
        }
        if (filterParams.length > 0) {
            params.filter = [];
            for (const filterParam of filterParams) {
                // Skip status filters from view - we'll use user-selected status instead
                if (filterParam.filter_field !== 'status') {
            
                    params.filter.push({
                        property: filterParam.filter_field,
                        value: filterParam.filter_compare_field,
                        operator: filterParam.filter_operator,
                    });
                }
            }
        } else {
            params.filter = [];
        }

        // Add user-selected status filters
        if (selectedStatusFilters.value.length > 0) {
            const allStatusValues = statusFilterOptions.value.map((option) => option.value);
            const unselectedStatuses = allStatusValues.filter(
                (status) => !selectedStatusFilters.value.includes(status)
            );

            // Choose the most efficient filter based on selection
            if (selectedStatusFilters.value.length === allStatusValues.length - 1) {
                // "All except one" → use "ne" (not equals)
                const statusFilter = {
                    property: 'status',
                    value: unselectedStatuses[0],
                    operator: 'ne',
                };
                params.filter.push(statusFilter);
    
            } else if (
                unselectedStatuses.length > 0 &&
                unselectedStatuses.length < selectedStatusFilters.value.length
            ) {
                // "Fewer unselected than selected" → use "ni" (not in) for efficiency
                const statusFilter = {
                    property: 'status',
                    value: unselectedStatuses.length === 1 ? unselectedStatuses[0] : unselectedStatuses,
                    operator: unselectedStatuses.length === 1 ? 'ne' : 'ni',
                };
                params.filter.push(statusFilter);
    
            } else {
                // Regular positive selection → use "eq" or "in"
                const statusFilter = {
                    property: 'status',
                    value:
                        selectedStatusFilters.value.length === 1
                            ? selectedStatusFilters.value[0]
                            : selectedStatusFilters.value,
                    operator: selectedStatusFilters.value.length === 1 ? 'eq' : 'in',
                };
                params.filter.push(statusFilter);

            }
        }



        // Call the API directly to get paginated results
        const result = await issuesAPI.fetchCases(params);

        // Update total count
        totalCases.value = result.totalCount || 0;

        if (loadMore) {
            // Append new cases to existing ones
            cases.value = [...cases.value, ...result.results];
        } else {
            // Replace cases with new ones (initial load or refresh)
            cases.value = result.results;
        }
        
        // Fetch owner users for avatars after cases are loaded
        if (cases.value.length > 0) {
            fetchOwnerUsers();
        }
    } catch (err) {
        console.error('Error fetching cases for view:', err);
        error.value = err instanceof Error ? err.message : 'Failed to load cases';
        // Keep the existing cases on error to prevent showing stale data
        if (!loadMore) {
            cases.value = [];
        }
    } finally {
        if (loadMore) {
            loadingMoreCases.value = false;
        }
    }
};

// Fetch cases for "Your Inbox" with custom owner filter and pagination support
const fetchCasesForYourInbox = async (loadMore = false) => {
    // Set appropriate loading state
    if (loadMore) {
        loadingMoreCases.value = true;
    } else {
        error.value = null;
    }

    try {
        // Create custom filter for owner_users_id = current user id
        const params: any = {
            page: currentPage.value,
            limit: casesPerPage.value,
            start: (currentPage.value - 1) * casesPerPage.value,
            filter: [
                {
                    property: 'owner_users_id',
                    value: userStore.userData?.id || '',
                    operator: 'eq',
                },
            ],
        };

        // Add status filter for Your Inbox - default to Ready and New if no filters selected
        if (selectedStatusFilters.value.length > 0) {
            const allStatusValues = statusFilterOptions.value.map((option) => option.value);
            const unselectedStatuses = allStatusValues.filter(
                (status) => !selectedStatusFilters.value.includes(status)
            );

            // Choose the most efficient filter based on selection
            if (selectedStatusFilters.value.length === allStatusValues.length - 1) {
                // "All except one" → use "ne" (not equals)
                const statusFilter = {
                    property: 'status',
                    value: unselectedStatuses[0],
                    operator: 'ne',
                };
                params.filter.push(statusFilter);

            } else if (
                unselectedStatuses.length > 0 &&
                unselectedStatuses.length < selectedStatusFilters.value.length
            ) {
                // "Fewer unselected than selected" → use "ni" (not in) for efficiency
                const statusFilter = {
                    property: 'status',
                    value: unselectedStatuses.length === 1 ? unselectedStatuses[0] : unselectedStatuses,
                    operator: unselectedStatuses.length === 1 ? 'ne' : 'ni',
                };
                params.filter.push(statusFilter);

            } else {
                // Regular positive selection → use "eq" or "in"
                const statusFilter = {
                    property: 'status',
                    value:
                        selectedStatusFilters.value.length === 1
                            ? selectedStatusFilters.value[0]
                            : selectedStatusFilters.value,
                    operator: selectedStatusFilters.value.length === 1 ? 'eq' : 'in',
                };
                params.filter.push(statusFilter);

            }
        } else {
            // Default filter for "My Open Cases" - only show Ready (2) and New (1) status
            const statusFilter = {
                property: 'status',
                value: ['2', '1'], // Ready and New
                operator: 'in',
            };
            params.filter.push(statusFilter);
        }

        // Add the selected sort
        if (selectedSort.value) {
            const [property, direction] = selectedSort.value.split('_');
            params.sort = [{ property: property, direction: direction }];
        }

        // Call the API directly to get paginated results
        const result = await issuesAPI.fetchCases(params);

        // Update total count
        totalCases.value = result.totalCount || 0;

        if (loadMore) {
            // Append new cases to existing ones
            cases.value = [...cases.value, ...result.results];
        } else {
            // Replace cases with new ones (initial load or refresh)
            cases.value = result.results;
        }
        
        // Fetch owner users for avatars after cases are loaded
        if (cases.value.length > 0) {
            fetchOwnerUsers();
        }
    } catch (err) {
        console.error('Error fetching cases for Your Inbox:', err);
        error.value = err instanceof Error ? err.message : 'Failed to load cases';
        // Keep the existing cases on error to prevent showing stale data
        if (!loadMore) {
            cases.value = [];
        }
    } finally {
        if (loadMore) {
            loadingMoreCases.value = false;
        }
    }
};

// Load more cases for infinite scroll
const loadMoreCases = async () => {
    if (loadingMoreCases.value || !hasMoreCases.value) {
        return;
    }

    currentPage.value += 1;

    if (props.isYourInbox && currentUserId.value) {
        await fetchCasesForYourInbox(true);
    } else if (hasSelectedView.value && viewDetails.value) {
        await fetchCasesForView(true);
    }
};

// Scroll event handler for infinite scroll
const handleScroll = () => {
    const container = casesListRef.value;
    if (!container || loadingMoreCases.value || !hasMoreCases.value) {
        return;
    }

    // Get the actual scrollable element
    const scrollableElement = (container as any).__scrollableElement;
    const useWindowScroll = (container as any).__useWindowScroll;

    let scrollTop: number;
    let scrollHeight: number;
    let clientHeight: number;

    if (useWindowScroll) {
        // Use window scroll
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
    } else if (scrollableElement) {
        // Use the scrollable parent element
        scrollTop = scrollableElement.scrollTop;
        scrollHeight = scrollableElement.scrollHeight;
        clientHeight = scrollableElement.clientHeight;
    } else {
        // Fallback to our container
        scrollTop = container.scrollTop;
        scrollHeight = container.scrollHeight;
        clientHeight = container.clientHeight;
    }

    const scrollPosition = scrollTop + clientHeight;
    const scrollThreshold = scrollHeight - 100;

    if (scrollPosition >= scrollThreshold) {
        loadMoreCases();
    }
};

// Function to find the actual scrollable element
const findScrollableParent = (element: HTMLElement | null): HTMLElement | null => {
    if (!element) return null;

    const isScrollable = (el: HTMLElement) => {
        const style = window.getComputedStyle(el);
        return (
            style.overflowY === 'auto' ||
            style.overflowY === 'scroll' ||
            style.overflow === 'auto' ||
            style.overflow === 'scroll'
        );
    };

    const hasScroll = (el: HTMLElement) => {
        return el.scrollHeight > el.clientHeight;
    };

    let current: HTMLElement | null = element;
    while (current && current !== document.body) {
        if (isScrollable(current) && hasScroll(current)) {
            return current;
        }
        current = current.parentElement;
    }

    return null;
};

// Function to setup scroll listener
const setupScrollListener = () => {
    if (casesListRef.value) {
        // Find the actual scrollable parent
        const scrollableElement = findScrollableParent(casesListRef.value);

        if (scrollableElement) {
            scrollableElement.addEventListener('scroll', handleScroll, { passive: true });
            // Store reference to the scrollable element for cleanup
            (casesListRef.value as any).__scrollableElement = scrollableElement;
        } else {
            window.addEventListener('scroll', handleScroll, { passive: true });
            (casesListRef.value as any).__useWindowScroll = true;
        }

        return true;
    }
    return false;
};

// Track current user ID to avoid refetching on userData refreshes
const currentUserId = ref<string | null>(userStore.userData?.id || null);

// Fetch user data for owner avatars
const fetchOwnerUsers = async () => {
  if (loadingUsers.value || cases.value.length === 0) return;
  
  try {
    loadingUsers.value = true;
    
    // Get unique owner team IDs from all cases to fetch users by team
    const ownerTeamIds = [...new Set(
      cases.value
        .filter(c => (c as any).owner_partners_teams_id)
        .map(c => (c as any).owner_partners_teams_id)
    )];
    
    if (ownerTeamIds.length === 0) {
      // Fallback to partner meta data if no team IDs
      const response = await metaAPI.fetchPartnerMetaData();
      const newUserMap = new Map();
      (response.pl__partners_users || []).forEach((user: any) => {
        const userId = user.val || user.id;
        newUserMap.set(userId, {
          id: userId,
          name: user.lbl || user.name,
          avatar: user.url_avatar || user.avatar,
          email: user.email,
          ...user
        });
      });
      ownerUsers.value = newUserMap;
      return;
    }
    
    // Fetch users by team IDs (like OwnerUserFormField does)
    const newUserMap = new Map();
    
    for (const teamId of ownerTeamIds) {
      try {
        const response = await metaAPI.fetchPartnersTeamsUsers({
          team_ids: teamId,
          search_all_partners: false,
          long_labels: true,
          page: 1,
          start: 0,
          limit: 100
        });
        
        // Store user data with avatars
        (response.pl__partners_teams_users || []).forEach((user: any) => {
          const userId = user.val || user.id;
          newUserMap.set(userId, {
            id: userId,
            name: user.lbl || user.name,
            avatar: user.url_avatar || user.avatar,
            email: user.email,
            ...user
          });
        });
      } catch (teamError) {
        console.warn(`InboxCasesList: Error fetching users for team ${teamId}:`, teamError);
      }
    }
    
    ownerUsers.value = newUserMap;
    
  } catch (error) {
    console.error('InboxCasesList: Error fetching owner users:', error);
  } finally {
    loadingUsers.value = false;
  }
};

// Get owner user data for a case
const getOwnerUser = (caseItem: Issue) => {
  const ownerUserId = (caseItem as any).owner_users_id;
  if (!ownerUserId) return null;
  
  const user = ownerUsers.value.get(ownerUserId);
  
  return user || null;
};



// Check if we should show notification for a case
const shouldShowNotificationForCase = (caseId: string): boolean => {
  // Only show case notifications on Open Chats views (view=HQGXM5 or view=chat)
  const currentView = route.query.view as string
  const isOnOpenChats = route.path === '/inbox' && (currentView === 'HQGXM5' || currentView === 'chat')
  
  if (!isOnOpenChats) {
    return false
  }
  
  // Check if we're on inbox view with cases list shown (including case detail pages)
  const isOnInboxView = route.path.startsWith('/inbox') 
  const hasCasesList = cases.value.length > 0
  
  if (!isOnInboxView || !hasCasesList) {
    return false
  }
  
  // Check if the case is in our current list
  const caseInList = cases.value.some(c => c.id === caseId)
  
  if (!caseInList) {
    return false
  }
  
  // Check if it's not the currently viewed case
  const currentCaseId = route.params.id as string
  const isNotCurrentCase = currentCaseId !== caseId
  
  return isNotCurrentCase
}

// Handle incoming chat notifications
const handleIncomingChatNotification = (caseId: string) => {
  if (shouldShowNotificationForCase(caseId)) {
    caseNotifications.value.add(caseId)
  }
}

// Clear notification when case is selected
const clearNotificationForCase = (caseId: string) => {
  if (caseNotifications.value.has(caseId)) {
    caseNotifications.value.delete(caseId)
  }
}

// Handle claim status events
const handleClaimStatusEvent = (event: any) => {
  if (event.eventData && event.eventData.tiles) {
    const claimableCases = event.eventData.tiles.filter((tile: any) => 
      tile.ownerUserId === null && tile.canClaim === true
    )
    
    if (claimableCases.length > 0) {
      hasNewClaimableCases.value = true
    }
  }
}

// Refresh cases and clear new case notification
const refreshCases = async () => {
  hasNewClaimableCases.value = false
  
  // Reset pagination and fetch fresh data
  currentPage.value = 1
  cases.value = []
  
  if (props.isYourInbox && currentUserId.value) {
    isViewLoading.value = true
    try {
      await fetchCasesForYourInbox()
    } finally {
      isViewLoading.value = false
    }
  } else if (hasSelectedView.value && viewDetails.value) {
    isViewLoading.value = true
    try {
      await fetchCasesForView()
    } finally {
      isViewLoading.value = false
    }
  }
}

// Add sort options and selectedSort before they are used in watch functions
const sortOptions = ref([
    { label: 'Created (Oldest)', value: 'created_ASC' },
    { label: 'Created (Newest)', value: 'created_DESC' },
    { label: 'Updated (Oldest)', value: 'updated_ASC' },
    { label: 'Updated (Newest)', value: 'updated_DESC' },
]);

const selectedSort = ref('updated_DESC');

// Add status filter options and selectedStatusFilters before they are used in watch functions
const statusFilterOptions = ref([
    { label: 'New', value: '1' },
    { label: 'Ready', value: '2' },
    { label: 'Waiting', value: '79' },
    { label: 'Resolved', value: '7' },
    { label: 'Closed', value: '10' },
    { label: 'Cancelled', value: '89' },
]);

const selectedStatusFilters = ref<string[]>([]);

// Add computed property to identify special views where status filter should be hidden
const isSpecialView = computed(() => {
    // Hide status filter for these special views:
    // 1. "My Open Cases" (Your Inbox)
    if (props.isYourInbox) {
        return true;
    }
    
    // 2-4. The three special views from the backend
    if (props.selectedView?.label) {
        const specialViewLabels = [
            "My Team's Unassigned Cases",
            "All Unassigned Cases", 
            "All Open Cases",
            "Open Cases" // Backend name before display rename
        ];
        return specialViewLabels.includes(props.selectedView.label);
    }
    
    return false;
});

// Detect default sort from view configuration
const defaultSortFromView = computed(() => {
    if (!viewDetails.value?.sort_object) {

        return 'updated_DESC'; // Default fallback
    }

    // Get the sort_object from the view (e.g., "created_desc")
    const sortObject = viewDetails.value.sort_object;
    

    // Convert "created_desc" to "created_DESC" format to match our dropdown values
    // Split by underscore, keep property lowercase, make direction uppercase
    const [property, direction] = sortObject.split('_');
    const converted = `${property}_${direction.toUpperCase()}`;

    return converted;
});

// Detect default status filters from view configuration
const defaultStatusFiltersFromView = computed(() => {
    if (!viewDetails.value?.filters || viewDetails.value.filters.length === 0) {
        return []; // No default - empty filter
    }

    // Find status filters in the view's filters
    const statusFilters = viewDetails.value.filters.filter((filter: any) => filter.filter_field === 'status');

    if (statusFilters.length === 0) {
        return []; // No default - empty filter
    }

    // Get all available status values for "ne" handling
    const allStatusValues = statusFilterOptions.value.map((option) => option.value);

    // Extract values from status filters
    const statusValues: string[] = [];
    statusFilters.forEach((filter: any) => {
        const operator = filter.filter_operator;
        const value = filter.filter_compare_field;

        if (operator === 'ne') {
            // "Not equals" - select all OTHER statuses except this one
            if (Array.isArray(value)) {
                // If multiple values to exclude, select all except those
                const excludeValues = value;
                // Convert exclude values to strings for consistent comparison
                const excludeStrings = excludeValues.map(v => String(v));
                statusValues.push(...allStatusValues.filter((status) => !excludeStrings.includes(status)));
            } else {
                // Single value to exclude, select all except this one
                statusValues.push(...allStatusValues.filter((status) => status !== String(value)));
            }
        } else if (operator === 'ni') {
            // "Not in" - select all OTHER statuses except the ones in the array
            const excludeValues = Array.isArray(value) ? value : [value];
            // Convert exclude values to strings for consistent comparison
            const excludeStrings = excludeValues.map(v => String(v));
            statusValues.push(...allStatusValues.filter((status) => !excludeStrings.includes(status)));
        } else {
            // Regular "eq" or "in" - use the values directly
            if (Array.isArray(value)) {
                // Multiple values like ["1", "ready"] - convert to strings
                statusValues.push(...value.map(v => String(v)));
            } else {
                // Single value like "ready" - convert to string
                statusValues.push(String(value));
            }
        }
    });

    return statusValues;
});

// Watch for XMPP events to handle incoming chat notifications
// Try multiple approaches to ensure we catch the events

// Watch for new pubsub events that affect cases in our list
watch(
    () => pubsubEventsStore.events.length,
    (newLength, oldLength) => {
        if (newLength > (oldLength || 0)) {
            const latestEvent = pubsubEventsStore.events[0]
            
            // Check if it's an incoming chat event for a case in our list
            if (latestEvent && latestEvent.eventType === 'incoming_chat' && latestEvent.caseId) {
                handleIncomingChatNotification(latestEvent.caseId)
            }
            
            // Check if it's a claim status event
            if (latestEvent && latestEvent.eventType === 'claim_status') {
                handleClaimStatusEvent(latestEvent)
            }
        }
    }
)

// Watch for route changes to clear notifications when navigating to a case
watch(
    () => route.params.id,
    (newCaseId, oldCaseId) => {
        // Clear notification for the newly selected case
        if (newCaseId && typeof newCaseId === 'string') {
            clearNotificationForCase(newCaseId)
        }
    }
)

// Watch for user ID changes (only when it actually changes, not on refresh)
watch(
    () => userStore.userData?.id,
    async (newUserId) => {
        if (newUserId && newUserId !== currentUserId.value) {
            currentUserId.value = newUserId;
            // Only refetch if we're on "Your Inbox"
            if (props.isYourInbox) {
                isViewLoading.value = true;
                // Reset pagination
                currentPage.value = 1;
                cases.value = [];
                try {
                    await fetchCasesForYourInbox();
                } finally {
                    isViewLoading.value = false;
                }
            }
        }
    }
);

// Watch for changes to the selected view and load data
watch(
    [() => props.selectedView?.id, () => hasSelectedView.value, () => viewDetails.value?.id],
    async (newValues, oldValues) => {
        const [newViewId, newHasSelectedView, newViewDetailsId] = newValues;
        const [oldViewId, oldHasSelectedView, oldViewDetailsId] = oldValues || [];
        
        // Only proceed if the view actually changed, not just the route
        const viewActuallyChanged = oldValues && (
            newViewId !== oldViewId || 
            newHasSelectedView !== oldHasSelectedView ||
            newViewDetailsId !== oldViewDetailsId
        );
        
        // On initial load (oldValues is undefined) or when view actually changes
        if (!oldValues || viewActuallyChanged) {
            // When view changes, immediately show loading
            if (viewActuallyChanged) {
                isViewChanging.value = true;
                isViewLoading.value = true;
                // Reset pagination
                currentPage.value = 1;
                cases.value = [];
                // Clear all notifications when view changes
                caseNotifications.value.clear();
            }
            
            // Set the default sort and status filters from the view when a new view is selected
            if (viewDetails.value) {
                selectedSort.value = defaultSortFromView.value;
                selectedStatusFilters.value = defaultStatusFiltersFromView.value;
            }

            // Handle data loading
            if (props.isYourInbox && currentUserId.value) {
                isViewLoading.value = true;
                // Reset pagination
                currentPage.value = 1;
                try {
                    await fetchCasesForYourInbox();
                } finally {
                    isViewLoading.value = false;
                    isViewChanging.value = false;
                }
            } else if (hasSelectedView.value && viewDetails.value) {
                isViewLoading.value = true;
                // Reset pagination
                currentPage.value = 1;
                try {
                    await fetchCasesForView();
                } finally {
                    isViewLoading.value = false;
                    isViewChanging.value = false;
                }
            }
        }
    },
    { immediate: true }
);

// Watch for changes to the selected sort and refetch cases
watch(selectedSort, async () => {
    // Skip if we're in the middle of a view change - the main watcher will handle it
    if (isViewChanging.value) return;

    // Reset pagination when sort changes
    currentPage.value = 1;
    cases.value = [];

    if (props.isYourInbox && currentUserId.value) {
        isViewLoading.value = true;
        try {
            await fetchCasesForYourInbox();
        } finally {
            isViewLoading.value = false;
        }
    } else if (hasSelectedView.value && viewDetails.value) {
        isViewLoading.value = true;
        try {
            await fetchCasesForView();
        } finally {
            isViewLoading.value = false;
        }
    }
});

// Watch for changes to the selected status filters and refetch cases
watch(
    selectedStatusFilters,
    async () => {
        // Skip if we're in the middle of a view change - the main watcher will handle it
        if (isViewChanging.value) return;

        // Reset pagination when filters change
        currentPage.value = 1;
        cases.value = [];

        if (props.isYourInbox && currentUserId.value) {
            isViewLoading.value = true;
            try {
                await fetchCasesForYourInbox();
            } finally {
                isViewLoading.value = false;
            }
        } else if (hasSelectedView.value && viewDetails.value) {
            isViewLoading.value = true;
            try {
                await fetchCasesForView();
            } finally {
                isViewLoading.value = false;
            }
        }
    },
    { deep: true }
);

// Setup scroll listener when cases are loaded
watch(
    [cases, isViewLoading],
    async ([newCases, loading]) => {
        if (!loading && newCases.length > 0) {
            await nextTick(); // Ensure DOM is updated

            // Try to setup scroll listener
            if (!setupScrollListener()) {
                setTimeout(() => {
                    setupScrollListener();
                }, 100);
            }
        }
    },
    { immediate: false }
);

function handleCreateCase() {
    emit('create-case');
}

function getRelativeTime(updated: string | undefined) {
    if (!updated) return '';
    const updatedDate = new Date(updated.replace(/-/g, '/'));
    const now = new Date();
    const diffMs = now.getTime() - updatedDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    if (diffMins < 60)
        return diffMins.toString().padStart(2, '0') + ':' + ((diffMs / 1000) % 60).toFixed(0).padStart(2, '0');
    const hours = Math.floor(diffMins / 60);
    const mins = diffMins % 60;
    return hours.toString().padStart(2, '0') + ':' + mins.toString().padStart(2, '0');
}

// Add after the existing refs
const isExpanded = ref(false);
const viewMode = ref<'card' | 'table'>('card');

// Add after the existing computed properties
const expandedLeftPosition = computed(() => {
    return props.sidebarWidth + 56;
});

// Add after the existing computed properties
const handleToggleExpand = () => {
    isExpanded.value = !isExpanded.value;
};

const handleToggleViewMode = () => {
    viewMode.value = viewMode.value === 'card' ? 'table' : 'card';
};

const handleSelectCase = (event: any) => {
    if (event.data) {
        // Clear notification for the selected case
        clearNotificationForCase(event.data.id);
        
        // Navigate to the case detail while preserving the view parameter
        router.push({
            name: 'inbox-case-detail',
            params: { id: event.data.id },
            query: route.query.view ? { view: route.query.view } : {},
        });
    }
};

// Table columns configuration
const tableColumns = [
    { field: 'display_name', header: 'Case Name', sortable: true },
    { field: 'location.name', header: 'Location', sortable: true },
    { field: 'status', header: 'Status', sortable: true },
    { field: 'updated', header: 'Last Updated', sortable: true },
];

// Setup and teardown scroll listener
onMounted(async () => {
    await nextTick(); // Ensure DOM is ready

    // Try initial setup (might not work if cases aren't loaded yet)
    setupScrollListener();
});

onUnmounted(() => {
    if (casesListRef.value) {
        // Remove scroll listener from the actual scrollable element
        const scrollableElement = (casesListRef.value as any).__scrollableElement;
        const useWindowScroll = (casesListRef.value as any).__useWindowScroll;

        if (scrollableElement) {
            scrollableElement.removeEventListener('scroll', handleScroll);
        } else if (useWindowScroll) {
            window.removeEventListener('scroll', handleScroll);
        }
    }
});
</script>

<template>
    <div :class="['view-card', { expanded: isExpanded }]" :style="{ '--expanded-left': `${expandedLeftPosition}px` }">
        <div v-if="!hasSelectedView" class="empty-state">Select a view to get started</div>

        <div v-else class="view-content">
            <div class="view-header">
                <BravoTitle1 v-if="!props.initialLoading">{{ selectedView?.label }}</BravoTitle1>
                <BravoSkeleton v-else width="150px" height="24px" class="mb-0" />
                <div class="view-actions">
                    <BravoButton
                        v-if="isExpanded"
                        :icon="viewMode === 'card' ? 'pi pi-table' : 'pi pi-th-large'"
                        severity="secondary"
                        text
                        @click="handleToggleViewMode"
                        :aria-label="viewMode === 'card' ? 'Switch to table view' : 'Switch to card view'"
                        v-tooltip.top="{
                            value: viewMode === 'card' ? 'Table view' : 'Card view',
                            showDelay: 400,
                        }"
                    />
                    <BravoButton
                        :icon="isExpanded ? 'pi pi-chevron-left' : 'pi pi-chevron-right'"
                        severity="secondary"
                        text
                        @click="handleToggleExpand"
                        :aria-label="isExpanded ? 'Collapse view' : 'Expand view'"
                        v-tooltip.top="{
                            value: isExpanded ? 'Collapse view' : 'Expand view',
                            showDelay: 400,
                        }"
                        style="z-index: 1001;"
                    />
                </div>
            </div>

            <div class="filter-container flex justify-between items-center px-2">
                <div class="flex-1">
                    <BravoFilterMultiSelect
                        v-if="!props.initialLoading && !isSpecialView"
                        v-model="selectedStatusFilters"
                        :filterOptions="statusFilterOptions"
                        placeholder="All Statuses"
                        :maxSelectedLabels="1"
                        selectedItemsLabel="{0} Statuses"
                        class="w-36"
                        id="case-status-filter"
                        data-test-id="case-status-filter"
                    />
                    <BravoSkeleton v-else-if="props.initialLoading && !isSpecialView" width="144px" height="32px" border-radius="6px" />
                </div>
                <div class="flex justify-end flex-1">
                    <BravoFilterSelect
                        v-if="!props.initialLoading"
                        v-model="selectedSort"
                        :filterOptions="sortOptions"
                        placeholder="Sort by"
                        class="w-36"
                        id="case-sort"
                        data-test-id="case-sort"
                    />
                    <BravoSkeleton v-else width="144px" height="32px" border-radius="6px" />
                </div>
            </div>

            <div v-if="props.initialLoading || isViewLoading" class="skeleton-loading-state">
                <div v-for="n in 8" :key="n" class="skeleton-case-row">
                    <BravoSkeleton
                        shape="rectangle"
                        width="16px"
                        height="16px"
                        border-radius="6px"
                        class="skeleton-icon"
                    />
                    <div class="skeleton-main">
                        <BravoSkeleton width="70%" height="16px" class="mb-2" />
                        <BravoSkeleton width="50%" height="14px" />
                    </div>
                    <div class="skeleton-time">
                        <BravoSkeleton width="32px" height="12px" class="mb-1" />
                        <BravoSkeleton shape="circle" size="8px" />
                    </div>
                </div>
            </div>

            <div v-else-if="error" class="error-state">
                <p>{{ error }}</p>
                <BravoButton
                    label="Retry"
                    @click="
                        () => {
                            if (props.isYourInbox && currentUserId) {
                                fetchCasesForYourInbox();
                            } else if (hasSelectedView && viewDetails) {
                                fetchCasesForView();
                            }
                        }
                    "
                />
            </div>

            <div v-else-if="cases.length === 0" class="no-cases">
                <p>No cases found for this view</p>
            </div>

            <!-- Card View (existing list) -->
            <div v-else-if="viewMode === 'card'" class="cases-container" ref="casesListRef">
                <ul class="cases-list">
                    <RouterLink
                        v-for="item in cases"
                        :key="item.id"
                        :to="{
                            name: 'inbox-case-detail',
                            params: { id: item.id },
                            query: route.query.view ? { view: route.query.view } : {},
                        }"
                        custom
                        v-slot="{ navigate, href, isActive }"
                    >
                        <li
                            :class="[
                                'case-list-row group',
                                { 'selected-case': route.params.id === item.id || isActive },
                            ]"
                            :tabindex="0"
                            @click="navigate"
                            :href="href"
                        >
                            <div class="case-list-icon flex items-center justify-center text-xl mr-2">
                                <i :class="getCaseIcon(item)" aria-hidden="true"></i>
                            </div>
                            <div class="case-list-main flex-1 min-w-0">
                                <BravoSubhead class="truncate">{{ getCaseDisplayName(item) }}</BravoSubhead>
                                <BravoBody class="case-list-subtitle truncate color-secondary">{{
                                    item.c__location || 'No Location'
                                }}</BravoBody>
                            </div>
                            <div class="case-list-time flex flex-col items-end justify-between min-w-[48px] relative h-full py-1">
                                <BravoTimestamp
                                    v-if="item.updated"
                                    :datetime="item.updated"
                                    length="short"
                                    class="text-xs text-slate-400 font-medium"
                                />
                                <span v-else class="text-xs text-slate-400 font-medium">-</span>
                                <div class="flex items-center gap-1">
                                    <span
                                        v-if="item.status === 'open'"
                                        class="inline-block w-2 h-2 rounded-full bg-green-400"
                                    ></span>
                                    <!-- Owner avatar -->
                                    <div class="owner-avatar-container">
                                        <img
                                            v-if="getOwnerUser(item)?.avatar"
                                            :src="getOwnerUser(item).avatar"
                                            :alt="getOwnerUser(item)?.name || 'Owner'"
                                            class="owner-avatar"
                                            :title="`Assigned to: ${getOwnerUser(item)?.name || 'Unknown'}`"
                                        />
                                        <div
                                            v-else
                                            class="owner-avatar-placeholder"
                                            :title="getOwnerUser(item) ? `Assigned to: ${getOwnerUser(item).name}` : 'Unassigned'"
                                        >
                                            <i class="pi pi-user"></i>
                                        </div>
                                    </div>
                                </div>
                                <!-- Chat notification dot -->
                                <div
                                    v-if="caseNotifications.has(item.id)"
                                    class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center"
                                    :title="`New chat message in case ${item.display_name || item.id}`"
                                >
                                    <i class="pi pi-comment text-white text-xs"></i>
                                </div>
                            </div>
                        </li>
                    </RouterLink>
                </ul>

                <!-- Loading more indicator -->
                <div v-if="loadingMoreCases" class="loading-more-container">
                    <div class="loading-more-spinner">
                        <BravoSkeleton shape="circle" size="24px" class="mr-2" />
                        <span class="loading-more-text">Loading more cases...</span>
                    </div>
                </div>

                <!-- End of cases indicator -->
                <div v-if="!hasMoreCases && cases.length > 0 && !loadingMoreCases" class="end-of-cases">
                    <div class="end-of-cases-text">
                        <i class="pi pi-check-circle mr-2"></i>
                        You've reached the end of the cases list
                    </div>
                </div>

                <!-- New claimable cases notification -->
                <div v-if="hasNewClaimableCases" class="new-cases-notification">
                    <div class="new-cases-content">
                        <i class="pi pi-bell mr-2"></i>
                        New case available to claim
                        <button @click="refreshCases" class="refresh-button">
                            Click here to refresh
                        </button>
                    </div>
                </div>
            </div>

            <!-- Table View -->
            <div v-else class="table-container">
                <BravoDataTable
                    :value="cases"
                    :columns="tableColumns"
                    :paginator="true"
                    :rows="25"
                    :rowsPerPageOptions="[10, 25, 50]"
                    :rowHover="true"
                    dataKey="id"
                    :tableStyle="{ minWidth: '50rem' }"
                    :stripedRows="false"
                    :showGridlines="false"
                    selectionMode="single"
                    @row-select="handleSelectCase"
                    class="cases-table"
                >
                    <template #display_name="{ data }">
                        <div class="flex items-center gap-2 relative">
                            <i :class="getCaseIcon(data)" class="text-lg"></i>
                            <span class="font-semibold">{{ getCaseDisplayName(data) }}</span>
                            <!-- Chat notification dot for table view -->
                            <div
                                v-if="caseNotifications.has(data.id)"
                                class="ml-2 w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center"
                                :title="`New chat message in case ${data.display_name || data.id}`"
                            >
                                <i class="pi pi-comment text-white text-xs"></i>
                            </div>
                        </div>
                    </template>
                    <template #status="{ data }">
                        <span
                            :class="[
                                'px-2 py-1 rounded-full text-xs font-medium',
                                data.status === 'open' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800',
                            ]"
                        >
                            {{ data.status || 'Unknown' }}
                        </span>
                    </template>
                    <template #updated="{ data }">
                        <BravoTimestamp v-if="data.updated" :datetime="data.updated" length="short" />
                        <span v-else>-</span>
                    </template>
                </BravoDataTable>
            </div>
        </div>
    </div>
</template>

<style scoped>
.view-card {
  background: var(--surface-0);
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 0;
  padding-top: 0;
  transition:
    all 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    z-index 0.3s ease;
  position: relative;
  z-index: 1;
  width: 100%;
  min-width: 100px;
}

.view-card.expanded {
    position: fixed;
    top: 0;
    left: var(--expanded-left);
    right: 0;
    bottom: 0;
    z-index: 1000;
    width: auto;
    min-width: 300px;
}

.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: var(--surface-600);
    font-style: italic;
    padding: 2rem;
}

.view-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    margin-top: 0;
    padding-top: 0;
    flex: 1;
    min-width: 0;
}

.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    height: 64px;
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
    position: relative;
    z-index: 1001;
    background: var(--surface-0);
    width: 100%;
    box-sizing: border-box;
}

.view-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
    position: relative;
    z-index: 1001;
}

.view-card.expanded .view-actions {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1002;
}

.filter-container {
    height: 48px;
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 1rem;
    flex: 1;
}

.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    gap: 1rem;
    background-color: var(--red-50);
    color: var(--red-600);
    margin: 1rem;
    border-radius: 4px;
}

.no-cases {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: var(--surface-600);
    flex: 1;
}

.cases-container {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.cases-list {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;
}

.case-list-row {
    display: flex;
    align-items: stretch;
    padding: 0.75rem 0.5rem 0.75rem 0.25rem;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    background: #fff;
    transition:
        background 0.15s,
        border-left 0.15s;
    border-left: 4px solid transparent;
    min-width: 0;
    min-height: 60px;
}

.case-list-row:hover {
    background: #f8fafc;
}

.selected-case {
    background: #e8f1fb;
    border-left: 4px solid #2563eb;
}

.case-list-icon {
    flex: 0 0 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-right: 0.75rem;
    color: #64748b;
}

.case-list-main {
    flex: 1 1 auto;
    min-width: 0;
    display: flex;
    flex-direction: column;
}

.case-list-title {
    font-weight: 700;
    color: #1e293b;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.case-list-subtitle {
    color: var(--text-color-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.case-list-time {
    flex: 0 0 48px;
    text-align: right;
    color: #94a3b8;
    font-size: 0.95rem;
    font-variant-numeric: tabular-nums;
    font-weight: 500;
}

.owner-avatar-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.owner-avatar {
    margin-top: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #e2e8f0;
}

.owner-avatar-placeholder {
    margin-top: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #f1f5f9;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
}

.owner-avatar-placeholder i {
    font-size: 8px;
}

.table-container {
    flex: 1;
    overflow: auto;
    padding: 1rem;
    min-height: 0;
}

.cases-table {
    width: 100%;
}

.cases-table :deep(.p-datatable-tbody tr) {
    cursor: pointer;
}

.cases-table :deep(.p-datatable-tbody tr:hover) {
    background-color: var(--surface-50);
}

.skeleton-loading-state {
    padding: 0;
    overflow-y: auto;
    flex: 1;
}

.skeleton-case-row {
    display: flex;
    align-items: center;
    padding: 0.75rem 0.5rem 0.75rem 0.25rem;
    border-bottom: 1px solid #f1f5f9;
    background: #fff;
    width: 100%;
    min-width: 0;
}

.skeleton-icon {
    flex: 0 0 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.skeleton-main {
    flex: 1 1 auto;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.skeleton-time {
    flex: 0 0 48px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    gap: 0.25rem;
}

.loading-more-container {
    display: flex;
    justify-content: center;
    padding: 1.5rem;
    margin: 1rem 0 0 0;
}

.loading-more-spinner {
    display: flex;
    align-items: center;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
}

.loading-more-text {
    color: var(--text-color-secondary);
}

.end-of-cases {
    display: flex;
    justify-content: center;
    padding: 1.5rem;
    margin: 1rem 0 0 0;
}

.end-of-cases-text {
    display: flex;
    align-items: center;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    font-style: italic;
}

.new-cases-notification {
    position: sticky;
    bottom: 0;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.75rem 1rem;
    margin: 0;
    border-radius: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.new-cases-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.refresh-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background 0.2s ease;
}

.refresh-button:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Chat notification dot styling */
.case-list-time .absolute {
    z-index: 10;
}

.case-list-time .pi-comment {
    font-size: 8px;
    line-height: 1;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    /* Keep original header styling - no changes to header height or padding */
}

@media (max-width: 1000px) {
    .filter-container {
        padding: 0.5rem 0.25rem;
    }
    
    .case-list-icon {
        flex: 0 0 28px;
        font-size: 1.1rem;
        margin-right: 0.5rem;
    }
    
    .case-list-time {
        flex: 0 0 40px;
        font-size: 0.85rem;
    }
}

@media (max-width: 900px) {
    /* Keep original header styling - no changes to header height or padding */
    
    .filter-container {
        height: auto;
        min-height: 48px;
        padding: 0.5rem;
    }
    
    .case-list-icon {
        flex: 0 0 28px;
        font-size: 1.1rem;
        margin-right: 0.5rem;
    }
    
    .case-list-time {
        flex: 0 0 40px;
        font-size: 0.85rem;
    }
}

@media (max-width: 600px) {
    .view-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
        height: auto;
        padding: 0.5rem;
    }
    
    .view-actions {
        justify-content: flex-end;
    }
    
    .filter-container {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }
    
    .case-list-main {
        margin-right: 0.5rem;
    }
    
    .table-container {
        padding: 0.5rem;
    }
}

@media (max-width: 400px) {
    .case-list-icon {
        flex: 0 0 24px;
        font-size: 1rem;
        margin-right: 0.25rem;
    }
    
    .case-list-time {
        flex: 0 0 36px;
        font-size: 0.8rem;
    }
    
    .view-header {
        padding: 0.5rem 0.25rem;
    }
    
    .filter-container {
        padding: 0.5rem 0.25rem;
    }
}
</style>
