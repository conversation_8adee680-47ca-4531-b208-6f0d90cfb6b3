<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'

// Disable automatic attribute inheritance since we manually handle class
defineOptions({
  inheritAttrs: false
})
import <PERSON><PERSON> from 'primevue/button'
import BravoFormField from '@/modules/knowledge/components/BravoFormField.vue'
import AddContact from './AddContact.vue'
import { useMemberStore } from '@/stores/member'
import { useMemberAPI, type MemberUser } from '@/composables/services/useMemberAPI'
import type { Issue } from '@/services/IssuesAPI'
import { getContactDisplayName } from '@/utils/contactHelpers'

interface Props {
  label: string
  fieldName: string
  value: any
  displayValue: string | string[]
  issue?: Issue | null
  isHorizontal?: boolean
  isEditing?: boolean
  isSaving?: boolean
  noValueText?: string
  dataTestId?: string
}

interface Emits {
  (e: 'update', fieldName: string, value: any): void
  (e: 'save', fieldName: string, value: any): void
  (e: 'cancel'): void
  (e: 'add-contact'): void
  (e: 'contact-click', contact: any): void
}

const props = withDefaults(defineProps<Props>(), {
  issue: null,
  isHorizontal: false,
  isEditing: false,
  isSaving: false,
  noValueText: '—',
  dataTestId: ''
})

const emit = defineEmits<Emits>()

// Store instances
const memberStore = useMemberStore()
const memberAPI = useMemberAPI()

// Use the store's loading state and data
const loadingMembersUsers = computed(() => memberStore.loadingMemberUsers)

// Ref to the BravoFormField component
const bravoFormFieldRef = ref<any>(null)

// Flag to prevent auto-save when Add Contact button is clicked
const preventAutoSave = ref(false)

// Add Contact dialog state
const showAddContactDialog = ref(false)

// Computed options for the dropdown - map store data to dropdown format
const contactOptions = computed(() => {
  return memberStore.memberUsers.map((user: MemberUser) => ({
    lbl: getContactDisplayName(user),
    val: user.id,
    id: user.id
  }))
})

// Computed display value with proper user name lookup
const computedDisplayValue = computed(() => {
  if (!props.value) {
    return props.noValueText
  }
  
  // If membersUsers is still loading, show loading state
  if (loadingMembersUsers.value) {
    return 'Loading...'
  }
  
  // Try to find the user in the loaded options
  const user = contactOptions.value.find((user: any) => user.val === props.value)
  if (user) {
    return user.lbl
  }
  
  // If we have a value but no matching user, show empty state instead of raw ID
  return props.noValueText
})

// Get the full contact object for the current value
const selectedContact = computed((): MemberUser | null => {
  if (!props.value) return null
  return memberStore.memberUsers.find((user: MemberUser) => user.id === props.value) || null
})

// Handle field updates
const handleFieldUpdate = (fieldName: string, value: any) => {
  emit('update', fieldName, value)
}

// Handle field save
const handleFieldSave = (fieldName: string, value: any) => {
  // Don't save if we're in the middle of an "Add Contact" action
  if (preventAutoSave.value) {
    return
  }
  
  emit('save', fieldName, value)
}

// Handle Add Contact dialog submission
const handleAddContactSubmit = async (contactData: any) => {
  try {
    console.log('CustomerContactFormField: Adding contact:', contactData)
    
    // Get the required IDs from the current issue
    const membersId = (props.issue as any)?.members_id || props.issue?.member?.id
    const membersLocationsId = props.issue?.members_locations_id || props.issue?.location?.id
    const contextOrgId = props.issue?.owner_partners_id || props.issue?.sponsor_partners_id || props.issue?.member?.context_org_id
    
    if (!membersId || !membersLocationsId || !contextOrgId) {
      console.error('CustomerContactFormField: Missing required member information:', {
        membersId,
        membersLocationsId,
        contextOrgId,
        issue: props.issue
      })
      throw new Error('Missing required member information')
    }
    
    // Create the contact using the API
    const newContact: any = await memberAPI.createContact({
      members_id: membersId,
      members_locations_id: membersLocationsId,
      context_org_id: contextOrgId,
      first_name: contactData.firstName,
      last_name: contactData.lastName,
      email: contactData.email,
      sms_number: contactData.phone
    })
    
    console.log('CustomerContactFormField: Contact created successfully:', newContact)
    
    // Refresh the members users list to show the new contact
    await fetchMembersUsers()
    
    // Close the modal
    showAddContactDialog.value = false
    
    // Auto-select the newly created contact
    const contactId = (newContact as any)?.id
    if (newContact && contactId) {
      // Find the new contact in the refreshed dropdown options
      const newContactOption = contactOptions.value.find(option => option.val === contactId)
      if (newContactOption) {
        console.log('CustomerContactFormField: Auto-selecting new contact:', newContactOption)
        
        // Update the field value
        handleFieldUpdate(props.fieldName, newContactOption.val)
        
        // Save the new selection after a short delay to ensure UI is updated
        setTimeout(() => {
          handleFieldSave(props.fieldName, newContactOption.val)
        }, 200)
      } else {
        console.warn('CustomerContactFormField: New contact not found in dropdown options:', {
          newContactId: contactId,
          availableOptions: contactOptions.value.map(opt => ({ id: opt.val, name: opt.lbl }))
        })
      }
    }
    
  } catch (error) {
    console.error('CustomerContactFormField: Error creating contact:', error)
    // Keep modal open so user can retry or see the error
  }
}

// Handle Add Contact dialog cancel
const handleAddContactCancel = () => {
  showAddContactDialog.value = false
}

// Handle cancel
const handleCancel = () => {
  emit('cancel')
}

// Handle add contact button click
const handleAddContact = () => {
  // Set flag to prevent auto-save
  preventAutoSave.value = true
  
  // Clear the flag after a delay to allow normal behavior to resume
  setTimeout(() => {
    preventAutoSave.value = false
  }, 1000) // 1 second should be enough time for the modal to open
  
  // Show the Add Contact dialog
  showAddContactDialog.value = true
  
  // Still emit the event for parent components that might want to handle it
  emit('add-contact')
}

// Fetch members users based on case data
async function fetchMembersUsers() {
  if (!props.issue) return
  
  try {
    // Extract required data from the case - use same logic as CaseCustomerDetails.vue
    const members_id = (props.issue as any)?.members_id || props.issue?.member?.id
    const members_locations_id = props.issue?.members_locations_id || props.issue?.location?.id
    const context_org_id = props.issue?.owner_partners_id || props.issue?.sponsor_partners_id || props.issue?.member?.context_org_id
    
    if (!members_id || !members_locations_id || !context_org_id) {
      console.warn('CustomerContactFormField: Missing required data for fetching members users:', {
        members_id,
        members_locations_id,
        context_org_id,
        issue: props.issue
      })
      return
    }
    
    // Use the same API call as CaseCustomerDetails.vue - the store handles loading state
    await memberStore.fetchMemberUsers({
      members_id,
      members_locations_id,
      context_org_id
    })
  } catch (error) {
    console.error('CustomerContactFormField: Failed to fetch members users:', error)
  }
}

// Watch for issue changes to fetch members users
watch(() => props.issue, (newIssue, oldIssue) => {
  if (!newIssue) return
  
  // Check if the case has actually changed by comparing key identifiers
  const newMemberId = (newIssue as any)?.members_id || newIssue?.member?.id
  const newLocationId = newIssue?.members_locations_id || newIssue?.location?.id
  const newOrgId = newIssue?.owner_partners_id || newIssue?.sponsor_partners_id || newIssue?.member?.context_org_id
  
  const oldMemberId = oldIssue ? ((oldIssue as any)?.members_id || oldIssue?.member?.id) : null
  const oldLocationId = oldIssue ? (oldIssue?.members_locations_id || oldIssue?.location?.id) : null
  const oldOrgId = oldIssue ? (oldIssue?.owner_partners_id || oldIssue?.sponsor_partners_id || oldIssue?.member?.context_org_id) : null
  
  // Fetch if this is a new case or if any of the key identifiers changed
  const hasChanged = !oldIssue || 
    newMemberId !== oldMemberId || 
    newLocationId !== oldLocationId || 
    newOrgId !== oldOrgId
  
  if (hasChanged && !loadingMembersUsers.value) {
    console.log('CustomerContactFormField: Case changed, fetching new member users:', {
      old: { memberId: oldMemberId, locationId: oldLocationId, orgId: oldOrgId },
      new: { memberId: newMemberId, locationId: newLocationId, orgId: newOrgId }
    })
    // Clear existing member users to prevent showing stale data
    memberStore.clearMemberUsers()
    fetchMembersUsers()
  }
}, { immediate: true })

// Fetch data on component mount
onMounted(async () => {
  if (props.issue && !loadingMembersUsers.value) {
    fetchMembersUsers()
  }
})

// Expose methods for parent component control
defineExpose({
  handleSaveComplete: (success: boolean) => {
    bravoFormFieldRef.value?.handleSaveComplete(success)
  },
  startEditing: () => {
    bravoFormFieldRef.value?.startEditing()
  }
})

// Handle contact name click to open drawer
const handleContactNameClick = () => {
  if (selectedContact.value) {
    emit('contact-click', selectedContact.value)
  }
}

// Handle edit button click
const handleEditContact = () => {
  // Start editing the field
  bravoFormFieldRef.value?.startEditing()
}
</script>

<template>
  <div :class="$attrs.class">
    <BravoFormField
      ref="bravoFormFieldRef"
      :label="label"
      :field-name="fieldName"
      :value="value"
      :display-value="computedDisplayValue"
      input-type="dropdown"
      display-type="text"
      :options="contactOptions"
      option-label="lbl"
      option-value="val"
      :is-loading="loadingMembersUsers"
      :is-horizontal="isHorizontal"
      :is-editing="isEditing"
      :is-saving="isSaving"
      :no-value-text="noValueText"
      :data-test-id="dataTestId"
      @update="handleFieldUpdate"
      @save="handleFieldSave"
      @cancel="handleCancel"
    >
      <!-- Custom display template with clickable contact name and edit button -->
      <template #display="{ displayValue }">
        <div v-if="selectedContact" class="contact-display-container">
          <a 
            href="#"
            class="contact-link"
            @click.prevent.stop="handleContactNameClick"
          >
            {{ displayValue }}
          </a>
          <!-- Edit button for contact fields when editable and not disabled -->
          <Button
            v-if="isEditing === true"
            icon="pi pi-user-edit"
            severity="secondary"
            text
            class="edit-contact-button"
            @click.stop="handleEditContact"
            v-tooltip.top="{ value: 'Change Contact', showDelay: 300 }"
            :data-testid="`${dataTestId || fieldName}-edit-contact-button`"
          />
        </div>
        <span 
          v-else
          class="clickable-value empty-value"
          @click.stop="handleEditContact"
        >
          {{ displayValue || noValueText }}
        </span>
      </template>

      <!-- Custom footer template with Add Contact button -->
      <template #footer>
        <div class="add-contact-footer p-dropdown-panel p-component-overlay">
          <Button 
            label="Add Contact" 
            icon="pi pi-plus"
            severity="secondary" 
            text 
            size="small"
            class="add-contact-button"
            @click="handleAddContact"
            @mousedown.stop
            @click.stop
            :data-testid="`${dataTestId}-add-contact-btn`"
          />
        </div>
      </template>
    </BravoFormField>

    <!-- Add Contact Dialog -->
    <AddContact
      :visible="showAddContactDialog"
      @update:visible="showAddContactDialog = $event"
      :onSubmit="handleAddContactSubmit"
      :onCancel="handleAddContactCancel"
    />
  </div>
</template>

<style scoped>
.add-contact-footer {
  padding: 0.75rem;
  border-top: 1px solid var(--surface-300);
  background-color: var(--surface-50);
}

.add-contact-button {
  width: 100%;
  justify-content: center;
  gap: 0.5rem;
}

.add-contact-button:hover {
  background-color: var(--surface-100);
}

/* Contact display styling */
.contact-display-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.contact-link {
  color: var(--primary-650) !important;
  text-decoration: none !important;
  cursor: pointer !important;
  word-break: break-all;
  line-height: 1.4;
  transition: color 0.2s ease, text-decoration 0.2s ease;
  display: inline-block;
  max-width: 100%;
}

.contact-link:hover {
  color: var(--primary-600) !important;
  text-decoration: underline !important;
}

.contact-link:visited {
  color: var(--primary-650) !important; /* Keep same as normal state */
}

/* Edit button styling for contact fields */
.edit-contact-button {
  opacity: 0;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  padding: 0.25rem;
  min-width: auto;
  width: 2rem;
  height: 2rem;
}

.contact-display-container:hover .edit-contact-button {
  opacity: 1;
}

.edit-contact-button:hover {
  background-color: var(--surface-100);
}

.clickable-value {
  cursor: pointer;
}

.empty-value {
  color: var(--icon-color-primary);
  margin-left: 1px; /* Add 1px left margin for better spacing of em dash */
}
</style> 