<script setup lang="ts">
import type { View } from '@/composables/services/useSettingsAPI'
import { useUserStore } from '@/stores/user'
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import InboxNavigationMenu from './InboxNavigationMenu.vue'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import Tooltip from 'primevue/tooltip'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { Issue } from '../../../services/IssuesAPI'
import { useCasesStore } from '../../../stores/cases'
import { useMemberStore } from '../../../stores/member'
import { useMetaStore } from '../../../stores/meta'
import { usePartnerStore } from '../../../stores/partner'
import { useTasksStore } from '../../../stores/tasks'
import CreateCaseModal from './CreateCaseModal.vue'

// Register the tooltip directive
const vTooltip = Tooltip

// Export the interface so it can be imported by other components
export interface SidebarItem {
  key?: string
  id?: string
  label: string
  count?: number
  icon?: string
  type?: 'item' | 'folder' | 'task'
  isOpen?: boolean
  children?: SidebarItem[]
  badge?: number | string
  items?: SidebarItem[]
  command?: () => void
  active?: boolean
  to?: any
  viewId?: string
}

// Define emits
const emit = defineEmits<{
  'dashboard-selected': [isDashboard: boolean]
  'panel-minimized': [isMinimized: boolean]
}>()

const casesStore = useCasesStore()
const tasksStore = useTasksStore()
const userStore = useUserStore()
const memberStore = useMemberStore()
const metaStore = useMetaStore()
const partnerStore = usePartnerStore()
const isLoading = ref(false)
const initialLoading = ref(false)
const loadError = ref<string | null>(null)
const yourInboxCount = ref(0)
const router = useRouter()
const route = useRoute()

// Add ref to store view counts
const viewCounts = ref<{ [viewId: string]: number }>({})

// Add loading states for different count types
const isLoadingInboxCount = ref(true)
const isLoadingTaskCounts = ref(true)
const isLoadingViewCounts = ref(true)

// Add ref for the panel menu component
const panelMenuRef = ref<any>(null)

// Add collapsible functionality
const isMinimized = ref(false)
const panelWidth = ref(280) // Set starting width to 280px
const wasManuallyToggled = ref(false) // Track if user manually toggled

// Add Case dialog state
const showAddCaseDialog = ref(false)

const yourInboxBadge = computed(() => yourInboxCount.value || '—')

// Add computed properties for task counts
const myTasksCount = computed(() => tasksStore.userTasks?.filter(task => task.status === 'Not Started')?.length || 0)
const teamTasksCount = computed(() => tasksStore.teamTasks?.filter(task => task.status === 'Not Started')?.length || 0)
const allTasksCount = computed(() => tasksStore.allTasks?.filter(task => task.status === 'Not Started')?.length || 0)

const selectedView = ref<SidebarItem | null>(null)

// Define selectItem function before model definition to avoid reference errors
const selectItem = (item: any) => {
  selectedView.value = item
  
  // Emit dashboard selection event
  emit('dashboard-selected', item.id === 'home')
  
  // Auto-expand when selecting an item in minimized mode
  if (isMinimized.value) {
    isMinimized.value = false
    emit('panel-minimized', false)
  }
  
  if (item.type === 'task') {
    router.push({
      path: '/inbox',
      query: { view: item.id, object: 'tasks' }
    })
  } else {
    router.push({
      path: '/inbox',
      query: { view: item.id }
    })
  }
}

// Create stable command functions to avoid recreating them in computed
const createCommandFunction = (itemData: { label: string; id: string; type: string }) => {
  return () => selectItem(itemData)
}

const SPECIAL_VIEWS_ORDER = [
  "My Team's Unassigned Cases",
  'All Unassigned Cases',
  'Open Cases',
  'Open Chats'
];
const SPECIAL_VIEWS_ICONS = [
  'pi pi-star',
  'pi pi-star',
  'pi pi-star',
  'pi pi-comments'
];

// Create the base model structure following PrimeVue PanelMenu patterns
const baseModel = [
    {
    key: 'favorites',
    label: 'Favorites',
    type: 'folder',
    id: 'favorites-panel',
    items: [
        {
        key: 'home',
        label: 'Home',
        icon: 'pi pi-home',
        id: 'home',
        type: 'item'
      }
    ]
  },
  {
    key: 'cases',
    label: 'Cases',
    type: 'folder',
    id: 'cases-panel',
    items: [
      {
        key: 'inbox',
        label: 'My Open Cases',
        icon: 'pi pi-inbox',
        id: 'inbox',
        type: 'item'
      },
    ]
  },
  {
    key: 'tasks',
    label: 'Tasks',
    type: 'folder',
    id: 'tasks-panel',
    items: [
      {
        key: 'my-tasks',
        label: 'My Open Tasks',
        icon: 'pi pi-list',
        id: 'my-tasks',
        type: 'item'
      },
      {
        key: 'team-tasks',
        label: 'My Team\'s Open Tasks',
        icon: 'pi pi-list',
        id: 'team-tasks',
        type: 'item'
      },
      {
        key: 'all-tasks',
        label: 'All Open Tasks',
        icon: 'pi pi-list',
        id: 'all-tasks',
        type: 'item'
      },
    ]
  }
]

// Create a reactive model using ref for better stability
const model = ref<SidebarItem[]>([])



// Function to build the model
const buildModel = () => {
  console.log('Building model with views:', casesStore.views?.map(v => ({ label: v.label, type: v.type, id: v.id })))
  const builtModel = baseModel.map(panel => {
    if (panel.type === 'folder' && panel.items) {
      // For the Favorites panel, just process the items normally
      if (panel.key === 'favorites') {
        return {
          ...panel,
          items: panel.items.map(item => {
            return {
              ...item,
              viewId: item.id, // Add viewId for matching
              command: createCommandFunction({ 
                label: item.label, 
                id: item.id, 
                type: item.type 
              })
            }
          })
        } as SidebarItem
      }
      // For the Cases panel, add custom views from the store
      else if (panel.key === 'cases') {
        const baseItems = panel.items.map(item => {
          return {
            ...item,
            viewId: item.id, // Add viewId for matching
            get badge() { 
              if (item.id === 'inbox') {
                return isLoadingInboxCount.value ? 'loading' : String(yourInboxBadge.value || '—')
              }
              return '—'
            },
            command: createCommandFunction({ 
              label: item.label, 
              id: item.id, 
              type: item.type 
            })
          }
        })
        
        // Add custom views from the store
        const customViews = (casesStore.views || [])
          .filter((view: View) => view.type !== 1)
          .filter((view: View) => view.label !== 'All Cases') // Exclude "All Cases" view to improve performance
          .map((view: View) => {
            console.log('Processing view:', view.label, 'Type:', view.type, 'ID:', view.id)
            
            let icon = 'pi pi-cog'
            const idx = SPECIAL_VIEWS_ORDER.indexOf(view.label)
            if (idx !== -1) {
              icon = SPECIAL_VIEWS_ICONS[idx]
            }
            
            // Override display label for specific views
            let displayLabel = view.label
            if (view.label === 'Open Cases') {
              displayLabel = 'All Open Cases'
              console.log('Renamed "Open Cases" to "All Open Cases"')
            }
            
            return {
              key: view.id,
              label: displayLabel,
              icon,
              get badge() { 
                return isLoadingViewCounts.value ? 'loading' : String(viewCounts.value[view.id] || '—') 
              },
              id: view.id,
              type: 'item',
              viewId: view.id, // Add viewId for matching
              command: createCommandFunction({ id: view.id, label: displayLabel, type: 'item' })
            }
          })
        
        // Sort special views to the top
        const specialViews: any[] = []
        const otherViews: any[] = []
        
        customViews.forEach(view => {
          // Find the index based on the original view label (before renaming)
          const originalView = (casesStore.views || []).find(v => v.id === view.id)
          const originalLabel = originalView?.label || view.label
          const idx = SPECIAL_VIEWS_ORDER.indexOf(originalLabel)
          
          console.log(`Sorting view: ${view.label} (original: ${originalLabel}) - index: ${idx}`)
          
          if (idx !== -1) {
            specialViews[idx] = view
          } else {
            otherViews.push(view)
          }
        })
        
        const sortedSpecialViews = specialViews.filter(Boolean)
        const sortedCustomViews = [...sortedSpecialViews, ...otherViews]
        
        return {
          ...panel,
          items: [...baseItems, ...sortedCustomViews]
        } as SidebarItem
      } else {
        // For other folders (like Tasks), use regular processing
        return {
          ...panel,
          items: panel.items.map(item => {
            return {
              ...item,
              get badge() {
                if (item.id === 'my-tasks') {
                  return isLoadingTaskCounts.value ? 'loading' : String(myTasksCount.value || '—')
                } else if (item.id === 'team-tasks') {
                  return isLoadingTaskCounts.value ? 'loading' : String(teamTasksCount.value || '—')
                } else if (item.id === 'all-tasks') {
                  return isLoadingTaskCounts.value ? 'loading' : String(allTasksCount.value || '—')
                }
                return '—'
              },
              viewId: item.id, // Add viewId for matching
              command: createCommandFunction({ 
                label: item.label, 
                id: item.id, 
                type: item.type === 'item' && item.id.includes('tasks') ? 'task' : item.type 
              })
            }
          })
        } as SidebarItem
      }
    }
    return panel as SidebarItem
  })
  
  model.value = builtModel
}

// Watch for changes that should trigger model rebuild (structural changes only)
watch(
  () => casesStore.views,
  () => {
    buildModel()
  },
  { immediate: true }
)

// Watch for model changes to trigger active state update in navigation menu
watch(
  () => model.value,
  () => {
    // Wait for DOM to update, then trigger active state update
    nextTick(() => {
      setTimeout(() => {
        if (panelMenuRef.value?.updateActiveStates) {
          panelMenuRef.value.updateActiveStates()
        }
      }, 100)
    })
  },
  { deep: true }
)





// Expanded keys for PanelMenu: start with Favorites, Cases and Tasks expanded
const expandedKeys = ref<{ [key: string]: boolean }>({
  favorites: true,
  cases: true,
  tasks: true
})

// Watch for route query changes to set the correct view
watch(
  () => [route.query.view, route.query.object],
  ([view, newObject]) => {
    // Handle home view immediately when present in route
    if (view === 'home' && route.path === '/inbox') {
      let homeItem: SidebarItem | null = null
      model.value.forEach(panel => {
        panel.items?.forEach(item => {
          if (item.id === 'home') {
            homeItem = item
          }
        })
      })
      if (homeItem) {
        selectedView.value = homeItem
      }
      return
    }
    
    // Handle specific view selection
    if (view && view !== 'home' && route.path === '/inbox') {
      // Find the view in all panels
      let foundView: SidebarItem | null = null
      
      model.value.forEach(panel => {
        panel.items?.forEach(item => {
          if (item.id === view) {
            // For task items, ensure we're in task mode
            if (item.id?.includes('tasks') && newObject === 'tasks') {
              foundView = item
            }
            // For non-task items, ensure we're not in task mode
            else if (!item.id?.includes('tasks') && newObject !== 'tasks') {
              foundView = item
            }
          }
        })
      })
      
      if (foundView) {
        selectedView.value = foundView
      }
      return
    }
    
    // Only default to home if there is no view in the query and we're on the exact /inbox route (not child routes)
    if (!view && route.path === '/inbox') {
      if (!newObject || newObject === '') {
        // Default to home view only if we're on the exact /inbox route
        let homeItem: SidebarItem | null = null
        model.value.forEach(panel => {
          panel.items?.forEach(item => {
            if (item.id === 'home') {
              homeItem = item
            }
          })
        })
        if (homeItem) {
          selectedView.value = homeItem
        }
      } else if (newObject === 'tasks') {
        // Default to first task view
        const taskItem = model.value[2].items?.[0]
        if (taskItem) {
          selectedView.value = taskItem
        }
      }
    } else if (!view && route.path !== '/inbox') {
      // Clear selection when on child routes with no view parameter
      selectedView.value = null
      // Force update navigation menu active states
      nextTick(() => {
        if (panelMenuRef.value?.updateActiveStates) {
          panelMenuRef.value.updateActiveStates()
        }
      })
    }
  },
  { immediate: true }
)

// Add a new watch for views loading
watch(
  () => casesStore.views,
  async (newViews) => {
    if (newViews && newViews.length > 0) {
      // If we have a view in the query, try to find and select it
      if (route.query.view) {
        const viewId = route.query.view as string
        const objectType = route.query.object as string
        
        // Check if it's the home view first
        if (viewId === 'home') {
          let homeItem: SidebarItem | null = null
          model.value.forEach(panel => {
            panel.items?.forEach(item => {
              if (item.id === 'home') {
                homeItem = item
              }
            })
          })
          if (homeItem) {
            selectedView.value = homeItem
          }
        } else {
          // Find the view in all panels
          let foundView: SidebarItem | null = null
          
          model.value.forEach(panel => {
            panel.items?.forEach(item => {
              if (item.id === viewId) {
                // For task items, ensure we're in task mode
                if (item.id?.includes('tasks') && objectType === 'tasks') {
                  foundView = item
                }
                // For non-task items, ensure we're not in task mode
                else if (!item.id?.includes('tasks') && objectType !== 'tasks') {
                  foundView = item
                }
              }
            })
          })
          
          if (foundView) {
            selectedView.value = foundView
          }
        }
      }
      await nextTick()
      highlightActiveSidebarItemAfterModelLoaded()
    }
  },
  { immediate: true }
)

// Function to handle view selection after views are loaded
const handleViewsLoaded = async () => {
  // After loading custom views, select the correct view from query
  if (route.query.view) {
    const viewId = route.query.view as string
    const objectType = route.query.object as string
    
    // Check if it's the home view first
    if (viewId === 'home') {
      let homeItem: SidebarItem | null = null
      model.value.forEach(panel => {
        panel.items?.forEach(item => {
          if (item.id === 'home') {
            homeItem = item
          }
        })
      })
      if (homeItem) {
        selectedView.value = homeItem
      }
    } else {
      // Find the view in all panels
      let foundView: SidebarItem | null = null
      
      model.value.forEach(panel => {
        panel.items?.forEach(item => {
          if (item.id === viewId) {
            // For task items, ensure we're in task mode
            if (item.id?.includes('tasks') && objectType === 'tasks') {
              foundView = item
            }
            // For non-task items, ensure we're not in task mode
            else if (!item.id?.includes('tasks') && objectType !== 'tasks') {
              foundView = item
            }
          }
        })
      })
      
      if (foundView) {
        selectedView.value = foundView
      }
    }
  }

  // After loading views, highlight the correct item
  await nextTick()
  highlightActiveSidebarItemAfterModelLoaded()
  
  // Also trigger panel menu active state update
  setTimeout(() => {
    if (panelMenuRef.value?.updateActiveStates) {
      panelMenuRef.value.updateActiveStates()
    }
  }, 200)
}

// Fetch views and my work issues on component mount
onMounted(async () => {
  // Setup resize listener
  window.addEventListener('resize', handleResize)
  // Check initial size
  handleResize()
  
  // Emit initial minimized state
  emit('panel-minimized', isMinimized.value)
  initialLoading.value = true
  loadError.value = null
  try {
    if (route.query.object === 'tasks') {
      expandedKeys.value = { navigation: true, cases: true, tasks: true }
      const view = route.query.view as string | undefined
      const userId = userStore.userData?.id || ''
      if (view === 'team-tasks') {
        await tasksStore.loadTeamTasks()
        const teamTasksItem = model.value[2].items?.find(item => item.id === 'team-tasks')
        if (teamTasksItem) selectedView.value = teamTasksItem
      } else if (view === 'all-tasks') {
        await tasksStore.loadAllTasks()
        const allTasksItem = model.value[2].items?.find(item => item.id === 'all-tasks')
        if (allTasksItem) selectedView.value = allTasksItem
      } else {
        await tasksStore.loadUserTasks(userId)
        const myTasksItem = model.value[2].items?.find(item => item.id === 'my-tasks')
        if (myTasksItem) selectedView.value = myTasksItem
      }
      
      // Fetch both task counts for accurate badges, plus other data
      await Promise.all([
        casesStore.fetchViews(),
        fetchYourInboxCount(),
        fetchTaskCounts() // This will load both user and team tasks
      ])
      await handleViewsLoaded()
      // Fetch case view counts after views are loaded
      await fetchCaseViewCounts()
    } else {
      await Promise.all([
        casesStore.fetchViews(),
        fetchYourInboxCount(),
        fetchTaskCounts()
      ])
      await handleViewsLoaded()
      // Fetch case view counts after views are loaded
      await fetchCaseViewCounts()
      await nextTick()
      highlightActiveSidebarItemAfterModelLoaded()
      await nextTick();
      // Expand the Navigation and Cases panels to show the views
      expandPanelById('navigation-panel')
      expandPanelById('cases-panel')
      
      // Trigger panel menu active state update after everything is loaded
      setTimeout(() => {
        if (panelMenuRef.value?.updateActiveStates) {
          panelMenuRef.value.updateActiveStates()
        }
      }, 300)
      // Only set selected view to 'Home' and update the route if there is no view in the query
      // AND we're on the exact /inbox route (not child routes like /inbox/cases/123)
      // Child routes will have paths like /inbox/cases/123 or /inbox/tasks/456
      if (!route.query.view && route.path === '/inbox') {
        let homeItem: SidebarItem | null = null
        model.value.forEach(panel => {
          panel.items?.forEach(item => {
            if (item.id === 'home') {
              homeItem = item
            }
          })
        })
        if (homeItem) {
          selectedView.value = homeItem
          router.replace({ path: '/inbox', query: { view: 'home' } })
        }
      } else if (!route.query.view && route.path !== '/inbox') {
        // Clear selection when on child routes with no view parameter
        selectedView.value = null
        // Force update navigation menu active states
        setTimeout(() => {
          if (panelMenuRef.value?.updateActiveStates) {
            panelMenuRef.value.updateActiveStates()
          }
        }, 200)
      }
    }
  } catch (err) {
    loadError.value = err instanceof Error ? err.message : 'Failed to load data'
  } finally {
    initialLoading.value = false
  }
})

const isYourInboxSelected = computed(() => selectedView.value?.id === 'inbox')

const handleSelectIssue = (issue: Issue) => {
  const type = selectedView.value?.key === 'tasks' ? 'tasks' : 'cases'
  router.push({
    path: `/inbox/${type}/${issue.id}`,
    query: route.query.view ? { view: route.query.view } : undefined
  })
}

const handleCreateCase = () => {
  showAddCaseDialog.value = true
}

const handleCaseCreated = (newCase: any) => {
  // Optionally refresh the cases list or navigate to the new case
  // You could emit an event here to refresh the parent component
}

const handleSearch = () => {
  // Implement search logic
}

const handleManageViews = () => {
  // Implement manage views logic
}

const handleTogglePanel = () => {
  isMinimized.value = !isMinimized.value
  wasManuallyToggled.value = true
  emit('panel-minimized', isMinimized.value)
}

// Function to get badge value for an item (for minimized view)
const getBadgeValue = (item: SidebarItem) => {
  if (!item.id) return '—'
  
  if (item.id === 'inbox') {
    return isLoadingInboxCount.value ? 'loading' : String(yourInboxBadge.value || '—')
  } else if (item.id === 'my-tasks') {
    return isLoadingTaskCounts.value ? 'loading' : String(myTasksCount.value || '—')
  } else if (item.id === 'team-tasks') {
    return isLoadingTaskCounts.value ? 'loading' : String(teamTasksCount.value || '—')
  } else if (item.id === 'all-tasks') {
    return isLoadingTaskCounts.value ? 'loading' : String(allTasksCount.value || '—')
  } else if (item.id && (viewCounts.value[item.id] !== undefined || isLoadingViewCounts.value)) {
    return isLoadingViewCounts.value ? 'loading' : String(viewCounts.value[item.id] || '—')
  }
  return '—'
}

// Active state management is now handled by InboxNavigationMenu component

// After fetching and building the sidebar model, highlight the correct item
function highlightActiveSidebarItemAfterModelLoaded() {
  const view = route.query.view as string | undefined
  const object = route.query.object as string | undefined
  const path = route.path
  let activeItem: SidebarItem | null = null
  
  // Don't set any active item for direct case/task navigation without view parameter
  if (!view && path.startsWith('/inbox/') && path !== '/inbox') {
    // Clear any active states and return early
    setTimeout(() => {
      if (panelMenuRef.value?.updateActiveStates) {
        panelMenuRef.value.updateActiveStates()
      }
    }, 100)
    return
  }
  
  model.value.forEach(panel => {
    // Check nested items
    panel.items?.forEach(item => {
      // Check for Home item
      if (item.id === 'home' && view === 'home' && path === '/inbox') {
        activeItem = item
      }
      // Match by id for inbox routes
      if (item.id && item.id === view && path === '/inbox') {
        // For task items, ensure we're in task mode
        if (item.id.includes('tasks') && object === 'tasks') {
          activeItem = item
        }
        // For non-task items, ensure we're not in task mode
        else if (!item.id.includes('tasks') && object !== 'tasks') {
          activeItem = item
        }
      }
      // Default to inbox if no view param on inbox route
      if (!view && item.id === 'inbox' && path === '/inbox' && (!object || object === 'cases')) {
        activeItem = item
      }
    })
  })
  
  // Active state is now managed by InboxNavigationMenu
}

// Add after existing computed properties
const sidebarWidth = computed(() => {
  return isMinimized.value ? 68 : 280
})

// Fetch count for "My Open Cases" using the same filter as InboxCasesList
const fetchYourInboxCount = async () => {
  try {
    isLoadingInboxCount.value = true
    // Create the same filter as used in InboxCasesList
    const params: any = {
      filter: [
        {
          property: 'owner_users_id',
          value: userStore.userData?.id || '',
          operator: 'eq'
        },
        {
          property: 'status',
          value: ['2', '1'], // Ready and New
          operator: 'in'
        }
      ],
      sort: [
        {
          property: 'updated',
          direction: 'DESC'
        }
      ]
    }
    
    // Fetch cases with the filter to get the count
    await casesStore.fetchCases(params as any)
    
    // Update the count from the totalCount in the store response
    yourInboxCount.value = casesStore.totalCount || 0
  } catch (err) {
    console.error('Error fetching My Open Cases count:', err)
    yourInboxCount.value = 0
  } finally {
    isLoadingInboxCount.value = false
  }
}

// Fetch task counts for badge display
const fetchTaskCounts = async () => {
  try {
    isLoadingTaskCounts.value = true
    const userId = userStore.userData?.id || ''
    if (userId && !tasksStore.loading) {
      // Load user, team, and all tasks to get accurate counts
      await Promise.all([
        tasksStore.loadUserTasks(userId),
        tasksStore.loadTeamTasks(),
        tasksStore.loadAllTasks()
      ])
    }
  } catch (err) {
    console.error('Error fetching task counts:', err)
  } finally {
    isLoadingTaskCounts.value = false
  }
}

// Fetch case counts for all views
const fetchCaseViewCounts = async () => {
  try {
    isLoadingViewCounts.value = true
    const views = casesStore.views || []
    const userId = userStore.userData?.id || ''
    
    if (!userId || views.length === 0) return
    
    // Use the issues API directly to avoid affecting main store state
    const { useIssuesAPI } = await import('@/composables/services/useIssuesAPI')
    const issuesAPI = useIssuesAPI()
    
    // Fetch counts for each view in parallel
    const countPromises = views
      .filter(view => view.type !== 1) // Skip views with type = 1
      .map(async (view) => {
        try {
          // Create params object based on view filters
          const params: any = {
            page: 1,
            limit: 1, // We only need the count, so minimal data
            start: 0
          }
          
          if (view.filters && view.filters.length > 0) {
            params.filter = []
            for (const filterParam of view.filters) {
              params.filter.push({ 
                property: filterParam.filter_field, 
                value: filterParam.filter_compare_field, 
                operator: filterParam.filter_operator 
              })
            }
          }
          
          // Fetch cases with the filter to get the count
          const result = await issuesAPI.fetchCases(params)
          
          return { viewId: view.id, count: result.totalCount || 0 }
        } catch (err) {
          console.error(`Error fetching count for view ${view.label}:`, err)
          return { viewId: view.id, count: 0 }
        }
      })
    
    // Execute all count fetches in parallel
    const results = await Promise.all(countPromises)
    
    // Update viewCounts with the results
    results.forEach(({ viewId, count }) => {
      viewCounts.value[viewId] = count
    })
  } catch (err) {
    console.error('Error fetching case view counts:', err)
  } finally {
    isLoadingViewCounts.value = false
  }
}

// Watch for user data changes to refresh the count
const currentUserId = ref<string | null>(userStore.userData?.id || null)

watch(
  () => userStore.userData?.id,
  (newUserId) => {
    if (newUserId && newUserId !== currentUserId.value) {
      currentUserId.value = newUserId
      fetchYourInboxCount()
      fetchTaskCounts() // Also refresh task counts when user changes
      fetchCaseViewCounts() // Also refresh case view counts when user changes
    }
  }
)

// Function to expand a specific panel by ID
const expandPanelById = (panelId: string) => {
  // Set the panel as expanded in the expandedKeys
  expandedKeys.value[panelId.replace('-panel', '')] = true
  
  // Wait for DOM update and then find the panel header by data attribute
  nextTick(() => {
    const panelHeader = document.querySelector(`[data-panel-id="${panelId}"]`)
    if (panelHeader && panelHeader instanceof HTMLElement) {
      // Check if the panel is already expanded
      const isExpanded = panelHeader.getAttribute('aria-expanded') === 'true'
      if (!isExpanded) {
        panelHeader.click()
      }
    }
  })
}

// Responsive behavior - auto-minimize at small screen sizes
const handleResize = () => {
  const width = window.innerWidth
  
  // Auto-minimize at 1150px and below to match CSS media query, but only if user hasn't manually toggled
  if (width <= 1150 && !wasManuallyToggled.value && !isMinimized.value) {
    // Only minimize if the model is ready to prevent broken rendering
    if (model.value.length > 0) {
      isMinimized.value = true
      emit('panel-minimized', isMinimized.value)
    }
  }
  // Auto-expand when screen gets larger, but only if it was auto-minimized
  else if (width > 1150 && !wasManuallyToggled.value && isMinimized.value) {
    isMinimized.value = false
    emit('panel-minimized', isMinimized.value)
  }
}

// Add computed property to check if minimized view should be shown
const shouldShowMinimizedIcons = computed(() => {
  return isMinimized.value && model.value.length > 0 && model.value.some(panel => panel.items && panel.items.length > 0)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})


</script>

<template>
  <div 
    class="inbox-views"
    :class="{ minimized: isMinimized }"
  >
    <div v-if="loadError" class="error-message">
      Failed to load views: {{ loadError }}
      <BravoButton
        label="Retry"
        size="small"
        @click="handleViewsLoaded"
        class="retry-button"
      />
    </div>
    
    <div class="panel-menu-wrapper">
      <div class="panel-header">
        <div class="header-content">
          <BravoTitlePage v-if="!isMinimized">Inbox</BravoTitlePage>
          <div v-if="!isMinimized" class="header-actions">
            <BravoButton
              icon="pi pi-plus"
              severity="secondary"
              text
              @click="handleCreateCase"
              aria-label="Create new case"
              v-tooltip.top="{
                value: 'Create Case',
                showDelay: 400
              }"
            />
          </div>
        </div>
      </div>
      
      <!-- Panel content when not minimized -->
      <div v-if="!isMinimized" class="panel-content">
        <InboxNavigationMenu
          ref="panelMenuRef"
          :model="model"
          v-model:expandedKeys="expandedKeys"
        />
      </div>
      
      <!-- Minimized loading state -->
      <div v-else-if="isMinimized && model.length === 0" class="panel-content minimized-loading">
        <div class="minimized-skeleton">
          <BravoSkeleton shape="rectangle" width="40px" height="40px" border-radius="8px" />
          <BravoSkeleton shape="rectangle" width="40px" height="40px" border-radius="8px" />
          <BravoSkeleton shape="rectangle" width="40px" height="40px" border-radius="8px" />
        </div>
      </div>
      
      <!-- Minimized icons view -->
      <div v-if="shouldShowMinimizedIcons" class="panel-content minimized-icons">
        <!-- All folder items -->
        <div v-for="panel in model" :key="panel.key" class="minimized-panel">
          <div 
            v-for="item in panel.items" 
            :key="item.key" 
            class="minimized-icon" 
            :class="{ active: item.active }"
            @click="item.command?.()"
            v-tooltip.right="{
              value: item.label,
              showDelay: 400
            }"
          >
            <i :class="item.icon || 'pi pi-folder'"></i>
            <BravoSkeleton
              v-if="getBadgeValue(item) === 'loading'"
              width="16px"
              height="12px"
              border-radius="4px"
              class="icon-badge-skeleton"
            />
            <BravoBadge 
              v-else-if="getBadgeValue(item) && getBadgeValue(item) !== '—'" 
              :value="getBadgeValue(item)" 
              class="icon-badge"
            />
          </div>
        </div>
      </div>
      
      <div class="panel-footer">
        <BravoButton
          v-if="!isMinimized"
          label="Manage Views"
          icon="pi pi-cog"
          severity="secondary"
          text
          @click="handleManageViews"
          aria-label="Manage Views"
        />
        <BravoButton
          :icon="isMinimized ? 'pi pi-chevron-right' : 'pi pi-chevron-left'"
          text
          severity="secondary"
          @click="handleTogglePanel"
          :aria-label="isMinimized ? 'Expand panel' : 'Collapse panel'"
          v-tooltip.top="{
            value: isMinimized ? 'Expand panel' : 'Collapse panel',
            showDelay: 400
          }"
        />
      </div>
    </div>

    <!-- Create Case Modal -->
    <CreateCaseModal
      v-model:visible="showAddCaseDialog"
      @case-created="handleCaseCreated"
    />
  </div>
</template>

<style scoped>
.inbox-title {
  padding: 1rem;
  padding-top:.5rem;
  margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--surface-900);

}
.inbox-views {
  background: var(--surface-50);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  min-width: 0;

  &:deep(.p-panelmenu-content) {
    background: none !important;
  }


}

.panel-menu-wrapper {
  width: 100%;
  height: 100%;
  background: var(--surface-50);
  padding: 0;
  border-radius: 0;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.5s ease;
}

.panel-header {
  padding: 1rem 1.5rem;
  height: 64px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  flex-shrink: 0;
  transition: all 0.5s ease;
}

.inbox-views.minimized .panel-header {
  padding: 0.5rem;
  justify-content: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  overflow: visible;
  transition: all 0.5s ease;
}

.inbox-views.minimized .header-content {
  justify-content: center;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.5s ease;
  flex-shrink: 0;
}

.panel-content {
  padding: 0.5rem 3px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  transition: all 0.5s ease;
  background: var(--surface-50);

  /* Override global scrollbar styles to match panel background */
  &::-webkit-scrollbar {
    background: var(--surface-50) !important;
  }

  &::-webkit-scrollbar-track {
    background: var(--surface-50) !important;
  }

  &::-webkit-scrollbar-thumb {
    border: 1px solid var(--surface-50) !important;
  }

  &::-webkit-scrollbar-corner {
    background: var(--surface-50) !important;
  }

  /* Firefox scrollbar override */
  scrollbar-color: var(--surface-300) var(--surface-50) !important;
}

.minimized-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  padding: 0.5rem 0;
  width: 100%;
  opacity: 0;
  animation: slideInMinimized 0.4s ease 0.1s forwards;
}

.minimized-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  padding: 0.5rem 0;
  width: 100%;
}

.minimized-skeleton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.6;
}

@keyframes slideInMinimized {
  from {
    opacity: 0;
    transform: translateX(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.minimized-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0 0.25rem;
}

.minimized-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--surface-100);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
  opacity: 1;
}

.minimized-icon:hover {
  background-color: var(--surface-200);
  transform: scale(1.05);
}

.minimized-icon.active {
  background-color: var(--primary-100);
  border: 2px solid var(--primary-500);
}

.minimized-icon.active i {
  color: var(--primary-600);
}

.minimized-icon i {
  font-size: 1.2rem;
  color: var(--surface-600);
}

.icon-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 10;
}

.icon-badge-skeleton {
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 10;
}

.error-message {
  background-color: var(--red-50);
  color: var(--red-600);
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.retry-button {
  align-self: flex-end;
}

.panel-footer {
  padding: 0.5rem;
  display: flex;
  justify-content: space-between;
  position: sticky;
  bottom: 0;
  background: var(--surface-50);
  z-index: 10;
  flex-shrink: 0;
}

.inbox-views.minimized .panel-footer {
  justify-content: center;
  padding: 0.5rem 0;
}

/* Responsive adjustments */
@media (max-width: 1000px) {
  .panel-content {
    padding: 0.5rem 2px;
  }
  
  .panel-header {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 768px) {
  .panel-content {
    padding: 0.25rem 2px;
  }
  
  .panel-header {
    padding: 0.5rem 0.75rem;
  }
}
</style> 