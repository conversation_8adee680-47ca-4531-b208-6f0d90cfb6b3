                                                                                                                    <script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCasesStore } from '../../../stores/cases'
import { useCommunicationsStore } from '../../comms/stores/communications'
import { useUserStore } from '../../../stores/user'
import { useCommsAPI } from '@/composables/services/useCommsAPI'
import { useIssuesAPI } from '@/composables/services/useIssuesAPI'
import type { Issue } from '@/composables/services/useIssuesAPI'
import type { AppConfig as CommsAppConfig } from '@/modules/comms/types'
import type { AvailableComm } from '@/modules/comms/types'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoSelectButton from '@services/ui-component-library/components/BravoSelectButton.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoTabs from '@services/ui-component-library/components/BravoTabs.vue'
import BravoTabList from '@services/ui-component-library/components/BravoTabList.vue'
import BravoTab from '@services/ui-component-library/components/BravoTab.vue'
import BravoTabPanels from '@services/ui-component-library/components/BravoTabPanels.vue'
import BravoTabPanel from '@services/ui-component-library/components/BravoTabPanel.vue'
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue'
import AllActivityTab from './tabs/AllActivityTab.vue'
import EmailChannel from '../../comms/components/channels/EmailChannel.vue'
import ChatChannel from '../../comms/components/channels/ChatChannel.vue'
import SmsChannel from '../../comms/components/channels/SmsChannel.vue'
import VoiceChannel from '../../comms/components/channels/VoiceChannel.vue'
import InteractionLogFilterButton from './CaseActivitiesFilter.vue'

// Define props
interface Props {
  issueId?: string
  onBack?: () => void
  onViewEvents?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  issueId: undefined,
  onBack: () => {},
  onViewEvents: () => {}
})

const casesStore = useCasesStore()
const store = useCommunicationsStore()
const userStore = useUserStore()
const commsAPI = useCommsAPI()
const issuesAPI = useIssuesAPI()
const route = useRoute()
const router = useRouter()

// Get case ID from route parameters
const routeCaseId = computed(() => route.params.id as string)

// Use current issue from the store if no specific issue ID is provided
const issue = computed(() => {
  if (props.issueId) {
    // If we have an issueId prop, we should fetch and display that
    if (casesStore.currentIssue?.id !== props.issueId) {
      casesStore.fetchCurrentIssue(props.issueId)
    }
  }
  return casesStore.currentIssue
})

// Communication panel state
const isInitializing = ref(false)
const error = ref<string | null>(null)
const loadingCommIds = ref<Set<string>>(new Set())
const isAddingComm = ref(false)

// Rename state management
const renamingCommIds = ref<Set<string>>(new Set())
const renameErrors = ref<Map<string, string>>(new Map())

// Filter state management for interaction logs
const showFilter = ref(false)
const currentFilterIndexes = ref<string[]>([])
const refreshingLogs = ref(false)

// Select button for filter type
const selectOptions = ['All', 'Notes']
const selectedOption = ref('All')

// Quick note input state
const quickNoteText = ref('')
const isSavingQuickNote = ref(false)
const quickNoteError = ref<string | null>(null)
const quickNoteInputRef = ref<any>(null)

// Filter update counter to trigger AllActivityTab refresh
const filterUpdateCounter = ref(0)

// Compute active tab from URL query parameter
const selectedTab = computed({
  get: () => {
    const commParam = route.query.comm as string | undefined
    // If we have a comm parameter and it exists in available comms, use it
    if (commParam && availableComms.value.some(comm => comm.id === commParam)) {
      return commParam
    }
    // Otherwise default to all-activity
    return 'all-activity'
  },
  set: (newValue: string) => {
    const query = { ...route.query }
    
    if (newValue === 'all-activity') {
      // Remove comm parameter for all-activity tab
      delete query.comm
    } else {
      // Set comm parameter to the communication ID
      query.comm = newValue
    }
    
    router.replace({ query })
  }
})

// Computed property to get available communications from the case (excluding loading ones)
const availableComms = computed(() => {
  const comms = issue.value?.availableComm || []
  
  // Filter out communications that are still being loaded
  let filteredComms = comms.filter(comm => !loadingCommIds.value.has(comm.id))
  
  // Special case: Hide internal room for specific case sources
  const hideInternalSources = ['web_connect', 'microconnect', 'connect']
  if (issue.value?.source && hideInternalSources.includes(issue.value.source)) {
    filteredComms = filteredComms.filter(comm => comm.object_scope !== 'private')
  }
  
  // Hide Internal/private and public comms for email cases
  if (issue.value?.source === 'email') {
    filteredComms = filteredComms.filter(comm => 
      comm.object_scope !== 'private' && comm.object_scope !== 'public'
    )
  }
  
  return filteredComms
})

// Computed property to get the currently selected communication
const selectedComm = computed(() => {
  if (selectedTab.value === 'all-activity') return null
  return availableComms.value.find(comm => comm.id === selectedTab.value) || null
})

// Computed property to determine the selected communication type
const selectedCommType = computed(() => {
  if (!selectedComm.value) return null
  
  const comm = selectedComm.value
  
  // Determine type based on object_scope and comm_type
  if (comm.object_scope === 'email') return 'email'
  if (comm.comm_type === 0) return 'chat'
  if (comm.comm_type === 1) return 'email'
  if (comm.comm_type === 2) return 'sms'
  if (comm.comm_type === 3) return 'voice'
  
  return 'chat' // default
})

// Computed property to determine when to show the filter
const shouldShowFilter = computed(() => {
  return selectedTab.value === 'all-activity'
})

// Computed property for current filter type based on select button and checkboxes
const currentFilterType = computed(() => {
  if (selectedOption.value === 'Notes') {
    // When Notes button is selected, only show notes regardless of checkboxes
    return ['notes']
  }
  
  // For 'All', use the selected checkboxes from the filter dropdown
  // If no checkboxes are selected, return a filter that will show no results
  return currentFilterIndexes.value.length > 0 ? currentFilterIndexes.value : ['_no_results_']
})

// Computed property for selected filter count (for badge display)
const selectedFilterCount = computed(() => {
  if (selectedOption.value === 'Notes') {
    // When Notes is selected, we're not using the checkboxes, so don't show count
    return 0
  }
  // For 'All' mode, show the count of selected checkboxes
  return currentFilterIndexes.value.length
})

// Computed properties for AllActivityTab filter parameters
const activityTabFilters = computed(() => {
  const filters = []
  
  if (issue.value?.id) {
    filters.push({ property: 'source_object_id', value: issue.value.id })
    filters.push({ property: 'source_object', value: 'issues' })
    
    // Always add filter_type based on select button and checkboxes
    const filterType = currentFilterType.value
    filters.push({ property: 'filter_type', value: filterType })
  }
  
  return filters
})



// Helper function to get communication icon
const getCommIcon = (comm: any) => {
  if (comm.object_scope === 'email') return 'pi pi-envelope'
  if (comm.comm_type === 0) return 'pi pi-comments'
  if (comm.comm_type === 1) return 'pi pi-envelope'
  if (comm.comm_type === 2) return 'pi pi-mobile'
  if (comm.comm_type === 3) return 'pi pi-phone'
  return 'pi pi-comments' // default
}

// Helper function to get communication title
const getCommTitle = (comm: any) => {
  // FIRST: Check for comm_label - if it exists and is not empty, use it
  if (comm.comm_label && comm.comm_label.trim() !== '') {
    return comm.comm_label.trim()
  }
  
  // THEN: Check object_scope and map to appropriate names
  if (comm.object_scope === 'private') {
    return 'Internal'
  }
  
  if (comm.object_scope === 'email') {
    return 'Email'
  }
  
  if (comm.object_scope === 'public') {
    return 'Chat'
  }
  
  // Fallback to original title if no scope matches
  return comm.title || comm.comm_label || `Case # ${comm.object_id?.slice(-6) || 'Unknown'}`
}

// Add communication menu ref and items
const addCommMenu = ref()
const addCommMenuItems = ref([
  {
    label: 'Email',
    icon: 'pi pi-envelope',
    command: () => handleAddCommunication('email')
  }
  // SMS option temporarily hidden
  // {
  //   label: 'SMS',
  //   icon: 'pi pi-mobile',
  //   command: () => handleAddCommunication('sms')
  // }
])

// Handler for adding communication
async function handleAddCommunication(type: string) {
  if (!issue.value?.id) {
    console.error('❌ CaseActivity: No issue ID available for adding communication')
    return
  }

  // Set loading state
  isAddingComm.value = true

  try {
    // Call the addThread API
    const response = await issuesAPI.addThread({
      id: issue.value.id,
      scope: type as 'email' | 'chat' | 'sms' | 'voice'
    })
    
    // Refresh the case data to get the new communication
    await casesStore.fetchCurrentIssue(issue.value.id)
    
    // Find the newly created communication before hiding it
    const allComms = issue.value?.availableComm || []
    const newComm = allComms.find(comm => {
      const commType = comm.object_scope === 'email' ? 'email' : 
                      comm.comm_type === 0 ? 'chat' :
                      comm.comm_type === 1 ? 'email' :
                      comm.comm_type === 2 ? 'sms' :
                      comm.comm_type === 3 ? 'voice' : 'chat'
      return commType === type
    })
    
    if (newComm) {
      // Hide the new communication until it's fully loaded
      loadingCommIds.value.add(newComm.id)
    }
    
    // Re-initialize communications to pick up the new thread
    await initializeCommunications()
    
    // Now that initialization is complete, show the communication and select it
    if (newComm) {
      // Remove from loading set to show the tab
      loadingCommIds.value.delete(newComm.id)
      
      // Wait a tick for the DOM to update
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Select the new communication tab
      selectedTab.value = newComm.id
      
      // Also select it in the store if it's available
      const storeComm = store.activeComms.find(comm => comm.id === newComm.id)
      if (storeComm) {
        await store.selectComm(storeComm.id)
      }
    } else {
      console.warn('⚠️ CaseActivity: Could not find newly created communication of type:', type)
    }

    
  } catch (error) {
    console.error('❌ CaseActivity: Failed to add communication:', error)
    // Clean up loading state on error
    if (issue.value?.availableComm) {
      const allComms = issue.value.availableComm
      const failedComm = allComms.find(comm => {
        const commType = comm.object_scope === 'email' ? 'email' : 
                        comm.comm_type === 0 ? 'chat' :
                        comm.comm_type === 1 ? 'email' :
                        comm.comm_type === 2 ? 'sms' :
                        comm.comm_type === 3 ? 'voice' : 'chat'
        return commType === type
      })
      if (failedComm) {
        loadingCommIds.value.delete(failedComm.id)
      }
    }
    // You could add a toast notification here to inform the user
  } finally {
    // Always clear loading state
    isAddingComm.value = false
  }
}

// Handler for showing the menu
function showAddCommMenu(event: Event) {
  addCommMenu.value.toggle(event)
}

// Handler for renaming communication tabs
async function handleCommRename(payload: { value: string | number; oldName: string; newName: string }) {
  const commId = payload.value as string
  const newName = payload.newName.trim()
  
  if (!newName || newName === payload.oldName) {
    return
  }
  
  // Set loading state
  renamingCommIds.value.add(commId)
  renameErrors.value.delete(commId) // Clear any previous errors
  
  try {
    // Call the store function to update the communication name
    await store.changeCommName(commId, newName)
    
    // Clear any existing errors for this communication
    renameErrors.value.delete(commId)
    
    // Optionally refresh the case data to ensure consistency
    if (issue.value?.id) {
      await casesStore.fetchCurrentIssue(issue.value.id)
    }
    
  } catch (error) {
    console.error('❌ CaseActivity: Failed to rename communication:', error)
    
    // Set error state
    const errorMessage = error instanceof Error ? error.message : 'Failed to rename communication'
    renameErrors.value.set(commId, errorMessage)
    
  } finally {
    // Clear loading state
    renamingCommIds.value.delete(commId)
  }
}

// Helper functions to get loading and error states for specific communications
const isCommRenaming = (commId: string) => renamingCommIds.value.has(commId)
const getCommRenameError = (commId: string) => renameErrors.value.get(commId) || undefined

// Function to clear rename errors (can be called when user starts editing again)
const clearCommRenameError = (commId: string) => {
  renameErrors.value.delete(commId)
}

// Filter handler functions for interaction logs
/**
 * Handle filter change from the filter button
 * This stores the selected filters but doesn't apply them immediately
 * The filters will be applied when the popover is hidden
 * @param filterKeys Array of filter keys to filter by
 */
const onFilterChange = async (filterKeys: string[]) => {
  // Store the current filter keys for use when the popover is hidden
  currentFilterIndexes.value = filterKeys
}

/**
 * Apply filters to interaction logs
 * This will be called when the filter popover is hidden
 * @param filterKeys Array of filter keys to filter by
 */
const applyLogsFilter = async (filterKeys: string[] = []) => {
  // Trigger filter update in AllActivityTab
  await triggerFilterUpdate()
}

/**
 * Refresh interaction logs by clearing current logs and fetching with current filters
 */
const refreshInteractionLogs = async () => {
  refreshingLogs.value = true
  
  try {
    // Trigger refresh in AllActivityTab component
    await triggerFilterUpdate()
  } finally {
    refreshingLogs.value = false
  }
}

/**
 * Trigger filter update in AllActivityTab component
 */
const triggerFilterUpdate = async () => {
  // This will be used to communicate with AllActivityTab
  filterUpdateCounter.value += 1
}

/**
 * Save quick note using the same endpoint as InboxCaseHeader
 */
const saveQuickNote = async () => {
  if (!quickNoteText.value.trim()) return
  
  if (!issue.value?.id) {
    quickNoteError.value = 'No issue selected'
    return
  }

  isSavingQuickNote.value = true
  quickNoteError.value = null

  try {
    await casesStore.addNote(issue.value.id, quickNoteText.value.trim())
    
    // Clear the input on success
    quickNoteText.value = ''
    
    // Trigger filter update to refresh the activity timeline
    await triggerFilterUpdate()

  } catch (err) {
    quickNoteError.value = err instanceof Error ? err.message : 'Failed to save note'
    console.error('❌ CaseActivity: Failed to save quick note:', err)
  } finally {
    isSavingQuickNote.value = false
  }
}

/**
 * Handle Enter key press in quick note input
 */
const handleQuickNoteKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    saveQuickNote()
  }
}

/**
 * Focus the quick note input field
 */
const focusQuickNoteInput = async () => {
  await nextTick()
  
  if (quickNoteInputRef.value) {
    // Try different ways to access the input element
    let input = null
    
    // Method 1: Try $el property
    if (quickNoteInputRef.value.$el) {
      input = quickNoteInputRef.value.$el.querySelector('input')
    }
    
    // Method 2: Try direct element access
    if (!input && quickNoteInputRef.value.querySelector) {
      input = quickNoteInputRef.value.querySelector('input')
    }
    
    // Method 3: Try if the ref itself is the input
    if (!input && quickNoteInputRef.value.tagName === 'INPUT') {
      input = quickNoteInputRef.value
    }
    
    if (input) {
      input.focus()
      return true
    }
  }
  
  // Fallback: find the input in the quick note container
  const quickNoteInput = document.querySelector('.quick-note-input input')
  if (quickNoteInput) {
    (quickNoteInput as HTMLInputElement).focus()
    return true
  }
  
  // Try even more specific selectors
  const anyInput = document.querySelector('.quick-note-container input')
  if (anyInput) {
    (anyInput as HTMLInputElement).focus()
    return true
  }
  
  // Try by ID
  const inputById = document.getElementById('case-activity-quick-note')
  if (inputById) {
    inputById.focus()
    return true
  }
  
  // Try to find any input within the BravoInputText component
  const bravoInput = document.querySelector('#case-activity-quick-note input')
  if (bravoInput) {
    (bravoInput as HTMLInputElement).focus()
    return true
  }
  
  return false
}

// Helper function to get unread count for a specific communication
const getCommUnreadCount = (commId: string): number => {
  const storeComm = store.activeComms.find(comm => comm.id === commId)
  return storeComm?.unreadCount || 0
}



const initializeCommunications = async () => {
  if (!issue.value || !userStore.xmppData) {
    return
  }

  isInitializing.value = true
  error.value = null

  try {
    
    // Create CommsAppConfig from user's xmppData
    const commsAppConfig: CommsAppConfig = {
      clientInstance: 'panel-instance',
      csrfToken: 'panel-token',
      serviceConfig: userStore.xmppData,
      requestId: 'panel-request',
      availableComm: [],
      members_locations_id: 'panel-location',
      members_users_id: 'panel-user',
      members_id: 'panel-member'
    }

    // Initialize the communications store if not already initialized
    if (!store.appConfig) {
      await store.initialize(commsAppConfig)
    }

    // Fetch detailed communication data for each availableComm
    const availableCommsArray = availableComms.value

    for (const availableComm of availableCommsArray) {
      try {
        // Get detailed communication data
        const commData = await commsAPI.getComm({ 
          id: availableComm.id, 
          is_enter: true 
        })
        
        // Create an AvailableComm object with the detailed data
        const detailedAvailableComm: AvailableComm = {
          id: commData.id,
          object: commData.object,
          object_id: commData.object_id,
          object_scope: commData.object_scope,
          comm_type: commData.comm_type,
          comm_label: commData.comm_label,
          comm_status: commData.comm_status,
          comm_state: commData.comm_state,
          object_source: commData.object_source,
          billing_status: commData.billing_status,
          title: commData.title,
          subtitle: commData.subtitle,
          duration_plus: commData.duration_plus,
          duration: commData.duration,
          message_count: 0, // Default value
          user_message_cnt: commData.user_message_cnt,
          external_rpid: commData.external_rpid,
          external_lpid: commData.external_lpid,
          external_id: commData.external_id,
          external_status: commData.external_status,
          created: commData.created,
          updated: commData.updated,
          occupied: commData.occupied,
          completed: commData.completed,
          c__status: commData.c__status,
          c__state: commData.c__state,
          c__avatar: commData.c__avatar,
          unread_count: availableComm.unread_count || 0, // Use original unread count from case data
          participants: commData.participants?.map(p => ({
            object: p.object,
            object_id: p.object_id,
            name: p.name,
            alias: p.alias,
            external_id: p.external_id,
            host: p.host,
            eligible: p.eligible,
            id: p.id,
            presence: p.presence
          })) || [],
          // Add the additional properties we need for email composer
          ...(commData.cc_recipients && { cc_recipients: commData.cc_recipients }),
          ...(commData.last_sender && { last_sender: commData.last_sender })
        }
        
        // Open the communication in the store
        await store.openExistingCommWithoutSelection(detailedAvailableComm)
        
      } catch (commError) {
        console.error('❌ CaseActivity: Failed to fetch comm data for:', availableComm.id, commError)
        // Continue with other communications even if one fails
      }
    }
    
  } catch (err) {
    console.error('❌ CaseActivity: Failed to initialize:', err)
    error.value = err instanceof Error ? err.message : 'Failed to initialize communications'
  } finally {
    isInitializing.value = false
  }
}

const cleanup = () => {
  try {
    // Reset the currently viewed communication
    store.setCurrentlyViewedComm(null)
    
    // Note: The communications store uses setup syntax and doesn't have $reset()
    // The setCurrentlyViewedComm(null) call above is sufficient for our cleanup needs
    // as it handles the unread count clearing logic
  } catch (err) {
    console.error('❌ CaseActivity: Cleanup failed:', err)
  }
}

// Function to handle tab selection
const selectTab = (tabValue: string) => {
  selectedTab.value = tabValue
  
  // If selecting a communication tab, update the store selection
  if (tabValue !== 'all-activity') {
    const selectedCommData = availableComms.value.find(comm => comm.id === tabValue)
    if (selectedCommData && store.activeComms.length > 0) {
      const storeComm = store.activeComms.find(comm => comm.id === selectedCommData.id)
      if (storeComm) {
        store.selectComm(storeComm.id)
      }
    }
  }
}

// Watch for tab changes and update store selection
watch(selectedTab, (newTab, oldTab) => {
  // Update the currently viewed communication in the store
  if (newTab !== 'all-activity') {
    // User is viewing a communication tab
    store.setCurrentlyViewedComm(newTab)
    
    const selectedCommData = availableComms.value.find(comm => comm.id === newTab)
    if (selectedCommData && store.activeComms.length > 0) {
      const storeComm = store.activeComms.find(comm => comm.id === selectedCommData.id)
      if (storeComm) {
        store.selectComm(storeComm.id)
      } else {
        console.warn('⚠️ CaseActivity: Store communication not found for:', selectedCommData.id)
      }
    } else {
      console.warn('⚠️ CaseActivity: Communication data not found for tab:', newTab)
    }
  } else {
    // User is viewing a non-communication tab (All Activity)
    store.setCurrentlyViewedComm(null)
  }
})

// Watch for case changes and reinitialize
watch(() => issue.value, (newCase) => {
  if (newCase) {
    // Reset the currently viewed communication when switching cases
    store.setCurrentlyViewedComm(null)
    initializeCommunications()
  } else {
    cleanup()
  }
}, { immediate: true })

// Watch for URL changes and navigate back to All Activity
watch(() => route.params.id, (newCaseId, oldCaseId) => {
  // Only react if the case ID actually changed (not just initial load)
  if (oldCaseId && newCaseId && newCaseId !== oldCaseId) {
    // Navigate back to All Activity tab (this will clear the comm query param)
    selectedTab.value = 'all-activity'
    
    // Reset the currently viewed communication
    store.setCurrentlyViewedComm(null)
    
    // Clean up existing communications
    cleanup()
    
    // The issue watcher above will handle reinitializing when the new case loads
  }
})

// Watch for store selected communication changes and sync tab selection
watch(() => store.selectedComm?.id, (newSelectedId) => {
  // Only sync tab selection if the user has manually selected a communication tab
  // Don't auto-jump from "All Activity" to a communication tab
  if (newSelectedId && availableComms.value.length > 0) {
    const commExists = availableComms.value.find(comm => comm.id === newSelectedId)
    
    // Only sync if we're already on a communication tab (not on all-activity)
    if (commExists && selectedTab.value !== newSelectedId && 
        selectedTab.value !== 'all-activity') {
      // selectedTab.value = newSelectedId // Commented out to prevent auto-jumping
    }
  }
})

// Watch for select button changes to trigger filtering
watch(selectedOption, async (newValue) => {
  // Focus the quick note input when Notes is selected
  if (newValue === 'Notes') {
    // Use multiple attempts with increasing delays to ensure the input is rendered
    setTimeout(() => {
      focusQuickNoteInput()
    }, 100)
    setTimeout(() => {
      focusQuickNoteInput()
    }, 200)
    setTimeout(() => {
      focusQuickNoteInput()
    }, 300)
  }
  
  triggerFilterUpdate()
})

// Watch for filter checkbox changes to trigger filtering
watch(currentFilterIndexes, (newFilters) => {
  // Only trigger update if we're on "All" mode (not "Notes" mode)
  if (selectedOption.value === 'All') {
    triggerFilterUpdate()
  }
})

// Watch for quick note text changes to clear errors
watch(quickNoteText, () => {
  if (quickNoteError.value) {
    quickNoteError.value = null
  }
})

// Watch for active comms changes to ensure proper initialization
watch(() => store.activeComms.length, (newLength, oldLength) => {
  // If we have active comms and a selected tab that's a communication, ensure it's selected in store
  if (newLength > 0 && selectedTab.value !== 'all-activity') {
    const selectedCommData = availableComms.value.find(comm => comm.id === selectedTab.value)
    if (selectedCommData) {
      const storeComm = store.activeComms.find(comm => comm.id === selectedCommData.id)
      if (storeComm && store.selectedComm?.id !== storeComm.id) {
        store.selectComm(storeComm.id)
      }
    }
  }
})

// Watch for available communications changes to handle deep linking
watch(() => availableComms.value.length, (newLength) => {
  // If we have a comm query parameter and communications are now available, try to select it
  const commParam = route.query.comm as string | undefined
  if (commParam && newLength > 0) {
    const targetComm = availableComms.value.find(comm => comm.id === commParam)
    if (targetComm) {
      // The selectedTab computed property will automatically handle this
      // but we need to ensure the store is updated when the comm becomes available
      const storeComm = store.activeComms.find(comm => comm.id === commParam)
      if (storeComm) {
        store.selectComm(storeComm.id)
      }
    } else {
      console.warn('📋 CaseActivity: Deep link comm not found, falling back to all-activity:', commParam)
      // If the comm doesn't exist, redirect to all-activity
      selectedTab.value = 'all-activity'
    }
  }
})

onBeforeUnmount(() => {
  cleanup()
})
</script>

<template>
  <div class="issue-details-container">
    <div v-if="issue" class="h-full">
      <BravoTabs v-model:value="selectedTab" class="activity-tabs" scrollable>
        <div class="tabs-header">
          <BravoTabList>
            <BravoTab value="all-activity">Activity</BravoTab>
            <BravoTab
              v-for="comm in availableComms"
              :key="comm.id"
              :value="comm.id"
              :name="getCommTitle(comm)"
              :renameable="true"
              :rename-loading="isCommRenaming(comm.id)"
              :rename-error="getCommRenameError(comm.id)"
              :badge-value="getCommUnreadCount(comm.id)"
              :badge-visible="getCommUnreadCount(comm.id) > 0"
              badge-severity="primary"
              @rename="handleCommRename"
            >
              <i :class="[getCommIcon(comm), 'mr-2 text-sm']"></i>
              {{ getCommTitle(comm) }}
            </BravoTab>
          </BravoTabList>
          <div class="tabs-header-actions">
            <BravoButton
              text
              severity="secondary"
              :icon="isAddingComm ? 'pi pi-spin pi-spinner' : 'pi pi-plus'"
              :disabled="isAddingComm"
              :aria-label="isAddingComm ? 'Adding Communication...' : 'Add Communication'"
              @click="showAddCommMenu"
            />
            <BravoMenu 
              ref="addCommMenu" 
              :model="addCommMenuItems" 
              :popup="true" 
            />
          </div>
        </div>
        
        <!-- Filter and refresh buttons for Activity tab -->
        <div class="filter-refresh-container" v-if="shouldShowFilter">
          <div class="filter-controls-left">
            <BravoSelectButton
              :options="selectOptions"
              v-model="selectedOption"
              size="small"
            />
            <InteractionLogFilterButton
              v-if="selectedOption === 'All'"
              :selected-count="selectedFilterCount"
              @update:filters="onFilterChange"
              @popover:hide="() => {}"
              @popover:show="() => {}"
            />
            <div v-if="selectedOption === 'Notes'" class="quick-note-container">
              <BravoInputText
                ref="quickNoteInputRef"
                v-model="quickNoteText"
                placeholder="Add a Note"
                class="quick-note-input"
                id="case-activity-quick-note"
                autocomplete="off"
                :disabled="isSavingQuickNote"
                @keydown="handleQuickNoteKeydown"
                :loading="isSavingQuickNote"
              />
              <div v-if="quickNoteError" class="quick-note-error">
                {{ quickNoteError }}
              </div>
            </div>
          </div>
          <BravoButton
            icon="pi pi-refresh"
            class="refresh-button"
            severity="secondary"
            :loading="refreshingLogs"
            @click="refreshInteractionLogs"
            aria-label="Refresh interaction logs"
            v-tooltip.bottom="{ value: 'Refresh', showDelay: 400 }"
            data-testid="refresh-logs-button"
          />
        </div>
        
        <BravoTabPanels>
          <BravoTabPanel value="all-activity">
            <AllActivityTab 
              :issue-id="issue.id" 
              :filters="activityTabFilters"
              :filter-update-counter="filterUpdateCounter"
            />
          </BravoTabPanel>
          <BravoTabPanel
            v-for="comm in availableComms"
            :key="comm.id"
            :value="comm.id"
          >
            <div class="comm-content">
              <!-- Error state -->
              <div 
                v-if="error"
                class="h-full flex items-center justify-center text-red-600"
              >
                <div class="text-center">
                  <div class="text-lg font-medium">Error</div>
                  <div class="mt-2">{{ error }}</div>
                </div>
              </div>

              <!-- Loading state -->
              <div 
                v-else-if="isInitializing"
                class="h-full flex items-center justify-center bg-white"
              >
                <div class="text-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <div class="mt-3 text-gray-600 text-sm">Loading communication data...</div>
                </div>
              </div>

              <!-- Communication content -->
              <template v-else-if="store.selectedComm && selectedTab === comm.id">
                <EmailChannel 
                  v-if="selectedCommType === 'email'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <ChatChannel 
                  v-else-if="selectedCommType === 'chat'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <SmsChannel 
                  v-else-if="selectedCommType === 'sms'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <VoiceChannel 
                  v-else-if="selectedCommType === 'voice'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <div v-else class="h-full flex items-center justify-center text-gray-500">
                  <div class="text-center">
                    <div class="text-lg font-medium">Unsupported Communication Type</div>
                    <div class="mt-2">{{ selectedCommType }}</div>
                  </div>
                </div>
              </template>

              <!-- Fallback loading -->
              <div 
                v-else
                class="h-full flex items-center justify-center bg-white"
              >
                <div class="text-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  <div class="mt-3 text-gray-600 text-sm">
                    <template v-if="selectedCommType">
                      Connecting to {{ selectedCommType }}...
                    </template>
                    <template v-else>
                      Loading communication...
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </BravoTabPanel>
        </BravoTabPanels>
      </BravoTabs>
    </div>
  </div>
</template>

<style scoped>
.issue-details-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 180px;
}

.activity-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-width: 0;
}

/* Target the BravoTabs and BravoTabPanel to ensure full height */
:deep(.bravo-tabs) {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-width: 0;
}

:deep(.bravo-tabpanels) {
  flex: 1;
  overflow: hidden;
  padding-bottom: 0 !important; /* Remove any bottom padding */
  min-height: 0;
}

:deep(.p-tabpanels) {
  padding-bottom: 0 !important; /* Remove any bottom padding from PrimeVue default */
}

:deep(.bravo-tabpanel) {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0; /* Remove all padding to ensure full height */
  min-height: 0;
}

/* BravoTabs customizations */
:deep(.bravo-tablist) {
  border-bottom-color: var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
  padding-right: 4.5rem; /* Increased space for scroll buttons + plus button */
  flex-shrink: 0;
}

:deep(.p-tablist-next-button) {
  margin-right: 2rem; /* Move scroll button away from plus button */
}

:deep(.p-tablist-prev-button) {
  margin-right: 0.0rem; /* Small gap between prev and next buttons */
}

.tabs-header {
  position: relative;
  padding: 0.61rem 1rem 0rem 1rem;
  background: white;
  flex-shrink: 0;
}

.tabs-header-actions {
  position: absolute;
  top: 0.61rem;
  right: 0.5rem; /* Moved closer to edge to avoid scroll buttons */
  display: flex;
  align-items: center;
  z-index: 2;
}

:deep(.p-tablist-viewport) {
  overflow: hidden;
}

.comm-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0; /* Remove padding to eliminate bottom space */
  min-height: 0;
}

/* Filter and refresh buttons container */
.filter-refresh-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
  padding: .5rem 1rem;
  margin-top: 0.5rem;
  flex-shrink: 0;
  min-width: 0;
}

.filter-controls-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
  overflow: visible;
}

/* Make the select button group more flexible */
.filter-controls-left :deep(.p-selectbutton) {
  flex-shrink: 0;
  min-width: 0;
}

.filter-controls-left :deep(.p-selectbutton .p-button) {
  min-width: 0;
  padding: 0.5rem 0.75rem;
  white-space: nowrap;
}

/* Make the filter button more flexible */
.filter-controls-left :deep(.p-button) {
  flex-shrink: 1;
  min-width: 0;
  overflow: hidden;
  max-width: 100%;
}

.filter-controls-left :deep(.p-button .p-button-label) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* Make InteractionLogFilterButton more flexible */
.filter-controls-left :deep(.interaction-log-filter-button) {
  flex-shrink: 1;
  min-width: 0;
  max-width: 100%;
}

.quick-note-container {
  flex: 1;
  position: relative;
  min-width: 0;
  max-width: none;
  overflow: visible;
}

.quick-note-input {
  width: 100%;
  min-width: 0;
}

.quick-note-input :deep(.p-inputtext) {
  min-width: 0;
  width: 100%;
}

.quick-note-error {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  color: var(--red-600);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 4px;
  z-index: 10;
}

.filter-refresh-container .refresh-button {
  margin-left: auto;
  flex-shrink: 0;
  min-width: 40px;
}

.refresh-button {
  padding: 0.5rem;
  transition: all 0.2s ease;
}

/* Responsive adjustments for panel width */
/* When panel gets narrow (around 400px) */
@media (max-width: 450px) {
  .filter-controls-left {
    gap: 0.5rem;
    justify-content: flex-start;
  }
  
  .filter-controls-left :deep(.p-selectbutton .p-button) {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .quick-note-container {
    min-width: 0;
    flex: 1;
  }
  
  .refresh-button {
    padding: 0.375rem;
    min-width: 36px;
  }
}

/* When panel gets very narrow (around 350px) */
@media (max-width: 380px) {
  .filter-refresh-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .filter-controls-left {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    justify-content: flex-start;
    align-self: flex-start;
  }
  
  .filter-controls-left :deep(.p-selectbutton .p-button) {
    padding: 0.25rem 0.375rem;
    font-size: 0.8rem;
  }
  
  .quick-note-container {
    min-width: 0;
    flex: 1;
    max-width: 100%;
    align-self: stretch;
  }
  
  .refresh-button {
    align-self: flex-start;
    margin-left: 0;
    padding: 0.375rem;
  }
}

/* When panel gets extremely narrow (around 280px) */
@media (max-width: 320px) {
  .filter-refresh-container {
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;
  }
  
  .filter-controls-left {
    flex-direction: row;
    align-items: center;
    gap: 0.25rem;
    justify-content: flex-start;
    align-self: flex-start;
  }
  
  .filter-controls-left :deep(.p-selectbutton .p-button) {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
  }
  
  .quick-note-container {
    min-width: 0;
    width: 100%;
    margin-top: 0.25rem;
    align-self: stretch;
  }
  
  .refresh-button {
    align-self: flex-start;
    padding: 0.25rem;
    min-width: 32px;
    margin-left: 0;
  }
}

/* Panel-width responsive adjustments - these target the panel's actual width */
/* Very narrow panels (like when CaseActivity panel is compressed) */
.issue-details-container {
  container-type: inline-size;
}

@container (max-width: 400px) {
  .filter-refresh-container {
    gap: 0.375rem;
  }
  
  .filter-controls-left {
    gap: 0.375rem;
    justify-content: flex-start;
  }
  
  .filter-controls-left :deep(.p-selectbutton .p-button) {
    padding: 0.25rem 0.375rem;
    font-size: 0.75rem;
    min-width: 0;
  }
  
  .quick-note-container {
    min-width: 0;
    flex: 1;
  }
  
  .refresh-button {
    padding: 0.25rem;
    min-width: 32px;
  }
}

@container (max-width: 300px) {
  .filter-refresh-container {
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;
  }
  
  .filter-controls-left {
    flex-direction: row;
    gap: 0.25rem;
    align-items: center;
    justify-content: flex-start;
    align-self: flex-start;
  }
  
  .filter-controls-left :deep(.p-selectbutton) {
    flex-shrink: 0;
  }
  
  .filter-controls-left :deep(.p-selectbutton .p-button) {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
  }
  
  .quick-note-container {
    width: 100%;
    margin-top: 0.25rem;
    align-self: stretch;
  }
  
  .refresh-button {
    align-self: flex-start;
    margin-left: 0;
  }
}

/* Fallback for browsers that don't support container queries */
@supports not (container-type: inline-size) {
  /* Use viewport-based media queries as fallback */
  @media (max-width: 600px) {
    .filter-refresh-container {
      gap: 0.375rem;
    }
    
    .filter-controls-left {
      gap: 0.375rem;
      justify-content: flex-start;
    }
    
    .filter-controls-left :deep(.p-selectbutton .p-button) {
      padding: 0.25rem 0.375rem;
      font-size: 0.75rem;
    }
  }
}

/* Original responsive adjustments for screen width */
@media (max-width: 900px) {
  .tabs-header {
    padding: 0.5rem 0.75rem 0rem 0.75rem;
  }
  
  .tabs-header-actions {
    right: 0.25rem;
  }
}

@media (max-width: 600px) {
  .tabs-header {
    padding: 0.5rem;
  }
  
  .tabs-header-actions {
    right: 0.25rem;
    top: 0.5rem;
  }
  
  :deep(.bravo-tablist) {
    padding-right: 3rem;
  }
}

@media (max-width: 400px) {
  .tabs-header {
    padding: 0.25rem;
  }
  
  .tabs-header-actions {
    right: 0.125rem;
    top: 0.25rem;
  }
}
</style> 