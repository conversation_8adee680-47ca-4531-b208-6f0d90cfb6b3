<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import Button from 'primevue/button'
import <PERSON>Form<PERSON>ield from '@/modules/knowledge/components/BravoFormField.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoConfirmDialog from '@services/ui-component-library/components/BravoConfirmDialog.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoInputGroup from '@services/ui-component-library/components/BravoInputGroup.vue'
import InputGroupAddon from 'primevue/inputgroupaddon'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { useMemberAPI } from '@/composables/services/useMemberAPI'
import { useMemberStore } from '@/stores/member'
import { getContactDisplayName } from '@/utils/contactHelpers'

const props = defineProps<{
  contact?: any
  mode?: 'detail' | 'list'
  issue?: any
  showBackButton?: boolean
}>()

const emit = defineEmits<{
  'email-contact': [email: string]
  'call-contact': [phone: string]
  'field-change': [field: string, value: any]
  'save': [changes: Record<string, any>]
  'cancel': []
  'contact-updated': [contact: any]
  'contact-click': [contact: any]
  'back-to-list': []
}>()

// Responsive width tracking
const containerRef = ref<HTMLElement | null>(null)
const containerWidth = ref(0)
const isWideMode = computed(() => containerWidth.value >= 300)

// Track saving state per field
const savingFields = ref<Set<string>>(new Set())

// Track field refs for controlling edit/display mode
const fieldRefs = ref<Record<string, any>>({})

// Member API instance
const memberAPI = useMemberAPI()

// Member store instance (for list mode)
const memberStore = useMemberStore()

// Confirm dialog instance
const confirm = useConfirm()

// Toast instance
const toast = useToast()

// Local reactive copy of contact data
const localContact = ref<any>(null)

// Determine the current mode
const currentMode = computed(() => props.mode || 'detail')

// List mode data
const memberId = computed(() => props.issue?.member?.id)
const locationId = computed(() => props.issue?.location?.id)
const orgId = computed(() => props.issue?.member?.context_org_id)

// Map MemberUser to Contact interface for display (for list mode)
const contacts = computed(() => {
  return memberStore.memberUsers.map(user => ({
    id: user.id,
    name: getContactDisplayName(user),
    title: '', // Could be added if available in the API response
    department: '', // Could be added if available in the API response
    email: user.email,
    phone: user.sms_number || '', // Show phone if available
    status: user.active ? 'Active' : 'Inactive'
  }))
})

const loadingContacts = computed(() => memberStore.loadingMemberUsers)
const contactsError = computed(() => memberStore.memberUsersError)

// Search functionality
const searchTerm = ref('')

// Filtered contacts based on search term
const filteredContacts = computed(() => {
  if (!searchTerm.value.trim()) {
    return contacts.value
  }
  
  const searchLower = searchTerm.value.toLowerCase().trim()
  return contacts.value.filter(contact => {
    const name = contact.name.toLowerCase()
    const email = contact.email?.toLowerCase() || ''
    
    return name.includes(searchLower) || email.includes(searchLower)
  })
})

// Watch for contact prop changes and update local copy
watch(() => props.contact, (newContact) => {
  if (newContact) {
    localContact.value = { ...newContact }
  }
}, { immediate: true, deep: true })

// Check if fields should be editable (for now, all fields except Contact ID are editable)
const isFieldsEditable = computed(() => true)

// Map UI field names to API field names
function getApiFieldName(uiFieldName: string): string {
  const fieldMapping: Record<string, string> = {
    'phone': 'sms_number'  // UI shows 'phone' but API expects 'sms_number'
  }
  return fieldMapping[uiFieldName] || uiFieldName
}

// Get field value from local contact data
function getFieldValue(fieldName: string): any {
  if (!localContact.value) return null
  
  switch (fieldName) {
    case 'first_name':
      return localContact.value.first_name || ''
    case 'last_name':
      return localContact.value.last_name || ''
    case 'email':
      return localContact.value.email || ''
    case 'phone':
      return localContact.value.sms_number || ''
    case 'contact_id':
      return localContact.value.id || ''
    default:
      return localContact.value[fieldName] || ''
  }
}

// Get display value for read-only mode
function getDisplayValue(fieldName: string): string {
  const value = getFieldValue(fieldName)
  
  if (value === null || value === undefined || value === '') {
    return '—'
  }
  
  return String(value)
}

// Handle field updates
function handleFieldUpdate(fieldName: string, value: any) {
  emit('field-change', fieldName, value)
}

// Handle field save
async function handleFieldSave(fieldName: string, value: any) {
  if (!props.contact?.id) {
    console.error('ContactDrawerPanel: No contact ID available for saving changes')
    return false
  }

  savingFields.value.add(fieldName)
  
  try {
    // Get the API field name (maps 'phone' to 'sms_number')
    const apiFieldName = getApiFieldName(fieldName)
    
    // Clean phone number - remove all non-digits for sms_number field
    let finalValue = value
    if (apiFieldName === 'sms_number' && value) {
      finalValue = value.replace(/\D/g, '')
    }
    
    console.log('🔧 Updating contact field:', {
      fieldName,
      apiFieldName,
      originalValue: value,
      finalValue,
      contactId: props.contact.id
    })
    
    // Create the update parameters for the API call
    const updateParams: any = {
      id: props.contact.id,
      context_org_id: props.contact.context_org_id || ''
    }
    
    // Set the specific field being updated
    if (fieldName === 'first_name') {
      updateParams.first_name = finalValue
    } else if (fieldName === 'last_name') {
      updateParams.last_name = finalValue
    } else if (fieldName === 'email') {
      updateParams.email = finalValue
    } else if (fieldName === 'phone') {
      updateParams.sms_number = finalValue
    }
    
    // Call the updateContact method from useMemberAPI
    await memberAPI.updateContact(updateParams)
    
    console.log('✅ Contact field updated successfully')
    
    // Update local contact data immediately
    if (localContact.value) {
      if (fieldName === 'phone') {
        // For phone field, update sms_number
        localContact.value.sms_number = finalValue
      } else {
        localContact.value[fieldName] = value
      }
      
      // Update the full_name if first or last name changed
      if (fieldName === 'first_name' || fieldName === 'last_name') {
        const firstName = localContact.value.first_name || ''
        const lastName = localContact.value.last_name || ''
        localContact.value.full_name = `${firstName} ${lastName}`.trim()
        localContact.value.name = localContact.value.full_name
      }
    }
    
    // Manually trigger the switch back to display mode after successful save
    fieldRefs.value[fieldName]?.handleSaveComplete(true)
    
    // Emit the updated contact to parent component
    emit('contact-updated', localContact.value)
    
    // Emit the save event to parent component for local state updates
    const updateData: any = {
      id: props.contact?.id,
      [fieldName]: value
    }
    emit('save', updateData)
    
    return true
  } catch (error) {
    console.error('❌ ContactDrawerPanel: Save failed for field', fieldName, ':', error)
    
    // Notify the field component of the failure
    fieldRefs.value[fieldName]?.handleSaveComplete(false)
    
    return false
  } finally {
    savingFields.value.delete(fieldName)
  }
}

// Handle cancel
function handleCancel() {
  emit('cancel')
}

const handleEmailContact = (email: string) => {
  emit('email-contact', email)
}

const handleCallContact = (phone: string) => {
  emit('call-contact', phone)
}

// Handle delete contact button click
const handleDeleteContact = () => {
  if (!localContact.value?.id) {
    console.error('No contact ID available for deletion')
    return
  }

  const contactName = localContact.value.name || localContact.value.full_name || 'this contact'
  
  confirm.require({
    message: `Are you sure you want to delete ${contactName}?`,
    header: 'Delete Contact',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    rejectClass: 'p-button-secondary',
    acceptLabel: 'Delete',
    rejectLabel: 'Cancel',
    accept: async () => {
      await performContactDelete()
    }
  })
}

// Perform the actual contact deletion
const performContactDelete = async () => {
  if (!localContact.value?.id) {
    console.error('No contact ID available for deletion')
    return
  }

  try {
    const contactName = localContact.value.name || localContact.value.full_name || 'Contact'
    
    console.log('🗑️ Deleting contact:', {
      contactId: localContact.value.id,
      contactName: contactName,
      fullContact: localContact.value
    })
    
    // Call the delete API
    await memberAPI.deleteContact({
      id: localContact.value.id
    })
    
    console.log('✅ Contact deleted successfully')
    
    // Show success toast notification
    toast.add({
      severity: 'success',
      summary: 'Contact Deleted',
      detail: `${contactName} has been successfully deleted.`,
      life: 5000
    })
    
    // Emit contact-updated event to refresh the contact list
    emit('contact-updated', { ...localContact.value, deleted: true })
    
    // Close the drawer (handled by parent component)
    emit('cancel')
    
  } catch (error: any) {
    console.error('❌ Failed to delete contact:', error)
    
    // Show error toast notification
    toast.add({
      severity: 'error',
      summary: 'Delete Failed',
      detail: error?.message || 'Failed to delete contact. Please try again.',
      life: 5000
    })
  }
}

// List mode functions
async function loadMemberUsers() {
  if (memberId.value && locationId.value && orgId.value) {
    console.log('ContactDrawerPanel: Loading member users for list mode:', {
      members_id: memberId.value,
      members_locations_id: locationId.value,
      context_org_id: orgId.value
    })
    
    try {
      await memberStore.fetchMemberUsers({
        members_id: memberId.value,
        members_locations_id: locationId.value,
        context_org_id: orgId.value
      })
    } catch (error) {
      console.error('ContactDrawerPanel: Error loading member users:', error)
    }
  }
}

// Handle contact click in list mode
const handleContactClick = (contact: any) => {
  // Find the original member user data
  const memberUser = memberStore.memberUsers.find(u => u.id === contact.id)
  if (memberUser) {
    emit('contact-click', memberUser)
  }
}

// Watch for changes in the required IDs and reload contacts (for list mode)
watch([memberId, locationId, orgId], () => {
  if (currentMode.value === 'list') {
    loadMemberUsers()
  }
}, { immediate: false })

// Watch for mode changes
watch(currentMode, (newMode) => {
  if (newMode === 'list') {
    loadMemberUsers()
  }
}, { immediate: true })

// Handle back button click
const handleBackToList = () => {
  emit('back-to-list')
}



// ResizeObserver for responsive behavior
let resizeObserver: ResizeObserver | null = null

// Set up responsive behavior on component mount
onMounted(async () => {
  await nextTick()
  if (containerRef.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        containerWidth.value = entry.contentRect.width
      }
    })
    resizeObserver.observe(containerRef.value)
    
    // Set initial width
    containerWidth.value = containerRef.value.offsetWidth
  }
})

// Clean up ResizeObserver
onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>

<template>
  <!-- List Mode -->
  <div v-if="currentMode === 'list'" ref="containerRef" class="contact-list">
    <div class="contact-list-header mb-6">
      <BravoTitle1>All Contacts</BravoTitle1>
      <p class="text-slate-600 text-sm mt-2">
        <template v-if="searchTerm.trim()">
          {{ filteredContacts.length }} of {{ contacts.length }} contact{{ contacts.length !== 1 ? 's' : '' }} found
        </template>
        <template v-else>
          {{ contacts.length }} contact{{ contacts.length !== 1 ? 's' : '' }} found
        </template>
      </p>
    </div>

    <!-- Search Bar -->
    <div class="contact-search mb-4">
      <BravoInputGroup>
        <InputGroupAddon>
          <i class="pi pi-search"></i>
        </InputGroupAddon>
        <BravoInputText
          v-model="searchTerm"
          placeholder="Search contacts by name or email..."
          class="w-full"
        />
      </BravoInputGroup>
    </div>

    <div v-if="loadingContacts" class="text-slate-500 italic">Loading contacts...</div>
    <div v-else-if="contactsError" class="text-red-500">{{ contactsError }}</div>
    <div v-else-if="contacts.length === 0" class="text-slate-500 italic">No contacts available</div>
    <div v-else-if="filteredContacts.length === 0 && searchTerm.trim()" class="text-slate-500 italic text-center py-8">
      No contacts found matching "{{ searchTerm }}"
    </div>
    <div v-else class="contact-list-items">
      <div 
        v-for="contact in filteredContacts" 
        :key="contact.id"
        class="bg-white border border-slate-200 rounded p-4 cursor-pointer hover:bg-slate-50 transition-colors mb-3"
        @click="handleContactClick(contact)"
      >
        <div class="flex items-start justify-between">
          <div class="flex items-center gap-3">
            <div class="contact-avatar">
              <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="text-blue-600 font-semibold">
                  {{ contact.name.charAt(0).toUpperCase() }}
                </span>
              </div>
            </div>
            <div>
              <div class="font-medium text-slate-700">{{ contact.name }}</div>
              <div class="text-sm text-slate-500 mt-1">
                <span v-if="contact.title" class="mr-2">{{ contact.title }}</span>
                <span v-if="contact.department">{{ contact.department }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="contact.email" class="mt-3 flex items-center gap-2 text-sm text-slate-600">
          <i class="pi pi-envelope text-slate-400"></i>
          <a :href="`mailto:${contact.email}`" class="text-blue-600 hover:text-blue-800">
            {{ contact.email }}
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Detail Mode -->
  <div v-else-if="currentMode === 'detail' && localContact" ref="containerRef" class="contact-details">
    <!-- Back Button -->
    <div v-if="showBackButton" class="contact-back-button mb-4">
      <BravoButton
        icon="pi pi-arrow-left"
        label="Back to List"
        severity="secondary"
        size="small"
        text
        @click="handleBackToList"
      />
    </div>

    <!-- Contact Header -->
    <div class="contact-header mb-6">
      <div class="flex items-center gap-3 mb-3">
        <div class="contact-avatar">
          <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <span class="text-blue-600 font-semibold text-lg">
              {{ (localContact.name || localContact.full_name || 'Contact').charAt(0).toUpperCase() }}
            </span>
          </div>
        </div>
        <div>
          <h4 class="text-xl font-semibold text-slate-800">{{ localContact.name || localContact.full_name || 'Contact' }}</h4>
        </div>
      </div>
    </div>

    <!-- Contact Information Fields -->
    <div class="contact-form-fields">
      <!-- First Name -->
      <BravoFormField
        :ref="el => { if (el) fieldRefs['first_name'] = el }"
        label="First Name"
        field-name="first_name"
        :value="getFieldValue('first_name')"
        :display-value="getDisplayValue('first_name')"
        input-type="text"
        display-type="text"
        :is-horizontal="isWideMode"
        :is-editing="isFieldsEditable"
        :is-saving="savingFields.has('first_name')"
        no-value-text="—"
        data-test-id="contact-first-name"
        @update="handleFieldUpdate"
        @save="handleFieldSave"
        @cancel="handleCancel"
        class="contact-form-field"
      />

      <!-- Last Name -->
      <BravoFormField
        :ref="el => { if (el) fieldRefs['last_name'] = el }"
        label="Last Name"
        field-name="last_name"
        :value="getFieldValue('last_name')"
        :display-value="getDisplayValue('last_name')"
        input-type="text"
        display-type="text"
        :is-horizontal="isWideMode"
        :is-editing="isFieldsEditable"
        :is-saving="savingFields.has('last_name')"
        no-value-text="—"
        data-test-id="contact-last-name"
        @update="handleFieldUpdate"
        @save="handleFieldSave"
        @cancel="handleCancel"
        class="contact-form-field"
      />

      <!-- Email -->
      <BravoFormField
        :ref="el => { if (el) fieldRefs['email'] = el }"
        label="Email"
        field-name="email"
        :value="getFieldValue('email')"
        :display-value="getDisplayValue('email')"
        input-type="email"
        display-type="text"
        :is-horizontal="isWideMode"
        :is-editing="isFieldsEditable"
        :is-saving="savingFields.has('email')"
        no-value-text="—"
        data-test-id="contact-email"
        @update="handleFieldUpdate"
        @save="handleFieldSave"
        @cancel="handleCancel"
        class="contact-form-field"
      />

      <!-- Phone -->
      <BravoFormField
        :ref="el => { if (el) fieldRefs['phone'] = el }"
        label="Phone"
        field-name="phone"
        :value="getFieldValue('phone')"
        :display-value="getDisplayValue('phone')"
        input-type="phone"
        display-type="text"
        :is-horizontal="isWideMode"
        :is-editing="isFieldsEditable"
        :is-saving="savingFields.has('phone')"
        no-value-text="—"
        data-test-id="contact-phone"
        @update="handleFieldUpdate"
        @save="handleFieldSave"
        @cancel="handleCancel"
        class="contact-form-field"
      />

      <!-- Contact ID (Read-only) -->
      <BravoFormField
        :ref="el => { if (el) fieldRefs['contact_id'] = el }"
        label="Contact ID"
        field-name="contact_id"
        :value="getFieldValue('contact_id')"
        :display-value="getDisplayValue('contact_id')"
        input-type="text"
        display-type="text"
        :is-horizontal="isWideMode"
        :is-editing="false"
        :is-saving="false"
        no-value-text="—"
        data-test-id="contact-id"
        @update="handleFieldUpdate"
        @save="handleFieldSave"
        @cancel="handleCancel"
        class="contact-form-field contact-id-field"
      />
    </div>

    <!-- Action Buttons -->
    <div class="contact-actions mt-8 pt-6 border-t border-slate-200">
      <div class="flex flex-col gap-2">
        <Button 
          v-if="getFieldValue('phone')"
          icon="pi pi-phone" 
          label="Call Contact" 
          class="w-full"
          @click="() => handleCallContact(getFieldValue('phone'))"
        />
      </div>
    </div>

    <!-- Delete Button - Separated at bottom -->
    <div class="contact-delete-section mt-8 pt-6 border-t border-slate-200">
      <BravoButton 
        icon="pi pi-trash" 
        label="Delete Contact" 
        severity="secondary"
        class="w-full"
        @click="handleDeleteContact"
      />
    </div>

    <!-- Confirmation Dialog (managed by useConfirm) -->
    <BravoConfirmDialog />
  </div>
</template>

<style scoped>
/* List Mode Styles */
.contact-list {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-list-header {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
}

.contact-list-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-search {
  width: 100%;
}

.contact-search :deep(.p-inputgroup-addon) {
  background-color: var(--surface-100, #f8f9fa);
  border-color: var(--surface-300, #dee2e6);
  color: var(--text-color-secondary, #6c757d);
}

/* Detail Mode Styles */
.contact-details {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-back-button {
  display: flex;
  align-items: center;
}

.contact-header {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
}

.contact-avatar {
  flex-shrink: 0;
}

.contact-form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-form-field {
  width: 100%;
  max-width: 500px;
}

.contact-id-field {
  /* Contact ID field styling - read-only appearance */
}

.contact-id-field :deep(.field-value) {
  /* Slightly muted appearance for read-only field */
  opacity: 0.8;
}

.contact-actions {
  margin-top: 2rem;
}

/* Responsive adjustments for wide mode */
@media (min-width: 300px) {
  .contact-form-field {
    max-width: 500px;
  }
}
</style> 