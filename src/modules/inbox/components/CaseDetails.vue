<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Tabs from 'primevue/tabs'
import TabList from 'primevue/tablist'
import Tab from 'primevue/tab'
import TabPanels from 'primevue/tabpanels'
import TabPanel from 'primevue/tabpanel'
import CaseDetailsPanel from './CaseDetailsPanel.vue'
import CaseCopilotPanel from './CaseCopilotPanel.vue'
import CaseJourneysPanel from './CaseJourneysPanel.vue'
import type { Issue } from '../../../services/IssuesAPI'
import type { Journey } from '@/composables/services/useAdminJourneysAPI'

const route = useRoute()
const router = useRouter()

const props = withDefaults(defineProps<{
  issue?: Issue
  loading?: boolean
}>(), {
  issue: undefined,
  loading: false
})

const emit = defineEmits<{
  'journey-added': [journey: Journey]
  'contact-click': [contact: any]
}>()

// Map tab values to query parameter values
const tabToQueryMap = {
  '0': undefined, // details - default, no query param
  '1': 'copilot',
  '2': 'journeys'
}

const queryToTabMap = {
  copilot: '1',
  journeys: '2',
  undefined: '0' // default tab
}

// Compute active tab from URL query parameter
const activeTab = computed({
  get: () => {
    const tabParam = route.query.tab as string | undefined
    return queryToTabMap[tabParam as keyof typeof queryToTabMap] || '0'
  },
  set: (newValue: string) => {
    const query = { ...route.query }
    const tabParam = tabToQueryMap[newValue as keyof typeof tabToQueryMap]
    
    if (tabParam) {
      query.tab = tabParam
    } else {
      delete query.tab
    }
    
    router.replace({ query })
  }
})

function handleJourneyAdded(journey: Journey) {
  emit('journey-added', journey)
}

function handleContactClick(contact: any) {
  emit('contact-click', contact)
}
</script>

<template>
  <div class="right-panel">
    <div class="tab-list-container">
      <Tabs v-model:value="activeTab" class="right-panel-tabs">
        <div class="tabs-header">
          <TabList>
            <Tab value="0">Details</Tab>
            <Tab value="1">Copilot</Tab>
            <Tab value="2">Journeys</Tab>
          </TabList>
        </div>
        <TabPanels>
          <TabPanel value="0">
            <CaseDetailsPanel 
              :issue="props.issue"
              :loading="props.loading"
              @contact-click="handleContactClick"
            />
          </TabPanel>
          
          <TabPanel value="1">
            <CaseCopilotPanel 
              :issue="props.issue"
              :loading="props.loading"
            />
          </TabPanel>
          
          <TabPanel value="2">
            <CaseJourneysPanel 
              :issue="props.issue"
              :loading="props.loading"
              @journey-added="handleJourneyAdded"
            />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </div>
  </div>
</template>

<style scoped>
.right-panel {
  border-left: 1px solid var(--border-color);
  background: var(--surface-0);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  width: 100%;
  min-width: 160px;
}

.tab-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-width: 0;
}

.right-panel-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-width: 0;
}

/* Target the Tabs and TabPanel to ensure full height */
:deep(.p-tabs) {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-width: 0;
}

:deep(.p-tabpanels) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 0 !important;
  min-height: 0;
}

:deep(.p-tabpanel) {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding-bottom: 0 !important;
  min-height: 0;
}

/* Ensure the child components take full height */
:deep(.p-tabpanel > *) {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* PrimeVue Tabs customizations */
:deep(.p-tablist) {
  border-bottom-color: var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
  flex-shrink: 0;
}

.tabs-header {
  padding: 0.61rem 1rem 0rem 1rem; /* Padding around tab buttons */
  flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .right-panel {
    border-left: none;
    border-top: 1px solid var(--border-color);
  }
  
  .tabs-header {
    padding: 0.5rem 0.75rem 0rem 0.75rem;
  }
}

@media (max-width: 600px) {
  .tabs-header {
    padding: 0.5rem;
  }
}

@media (max-width: 400px) {
  .tabs-header {
    padding: 0.25rem;
  }
}
</style> 