<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useCasesStore } from '../../../stores/cases'
import InboxViews from '../components/InboxViewsList.vue'
import InboxHome from '../components/InboxHome.vue'
import InboxCasesList from '../components/InboxCasesList.vue'
import TaskList from '../components/TaskList.vue'
import CreateCaseModal from '../components/CreateCaseModal.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import ZeroStateSearchSvg from '@/assets/zero-state-search.svg'
import { useUserStore } from '@/stores/user'
import { useCasesStore as useViewsStore } from '../../../stores/cases'
import type { SidebarItem } from '../components/InboxViewsList.vue'

const casesStore = useCasesStore()
const userStore = useUserStore()
const viewsStore = useViewsStore()
const route = useRoute()

// Determine if home is selected based on route query
const isHomeSelected = computed(() => route.query.view === 'home')

// Determine the object type (cases or tasks)
const objectType = computed(() => route.query.object || '')

// Get the selected view info
const selectedView = computed((): SidebarItem | null => {
  const viewId = route.query.view as string
  if (!viewId || viewId === 'home') return null
  
  // First check for built-in task and case views
  const builtInViews: { [key: string]: string } = {
    'inbox': 'My Open Cases',
    'my-tasks': 'My Open Tasks',
    'team-tasks': 'My Team\'s Open Tasks',
    'all-tasks': 'All Open Tasks'
  }
  
  if (builtInViews[viewId]) {
    return {
      id: viewId,
      label: builtInViews[viewId],
      key: viewId,
      type: 'item' as const
    }
  }
  
  // Find the view in the store for custom views
  const views = viewsStore.views || []
  const foundView = views.find((view: any) => view.id === viewId)
  
  if (foundView) {
    // Convert store view to SidebarItem format
    return {
      id: foundView.id,
      label: foundView.label,
      key: foundView.id,
      type: 'item' as const
    }
  }
  
  // Fallback for truly unknown views
  return {
    id: viewId,
    label: 'Unknown View',
    key: viewId,
    type: 'item' as const
  }
})

// Determine if "Your Inbox" is selected
const isYourInboxSelected = computed(() => route.query.view === 'inbox')

// Determine task type for TaskList
const taskType = computed(() => {
  if (objectType.value === 'tasks') {
    const viewId = route.query.view as string
    if (viewId === 'my-tasks') return 'user'
    if (viewId === 'team-tasks') return 'team'
    if (viewId === 'all-tasks') return 'all'
  }
  return 'user'
})

// Modal visibility state
const showCreateCaseModal = ref(false)

// Panel state
const isPanelMinimized = ref(false)

// Determine if no list panel should be shown (neither cases nor tasks)
const noListPanel = computed(() => {
  const hasView = !!route.query.view
  const isHome = isHomeSelected.value
  const isTasks = objectType.value === 'tasks'
  
  // No list panel when:
  // 1. Home is selected, OR
  // 2. No view is selected (direct case navigation)
  return isHome || !hasView
})

const handleDashboardSelected = (isDashboard: boolean) => {
  // This event is still useful for other behaviors, but route determines display
}

const handlePanelMinimized = (isMinimized: boolean) => {
  isPanelMinimized.value = isMinimized
}

// Handle create case button click
const handleCreateCase = () => {
  showCreateCaseModal.value = true
}

// Handle case creation completion
const handleCaseCreated = (newCase: any) => {
  // Refresh cases list after new case is created
  casesStore.fetchCases({ page: 1, limit: 25 })
}

// Handle issue selection from cases list
const handleSelectIssue = (issue: any) => {
  // Navigation will be handled by the InboxCasesList component
}

onMounted(async () => {
  // Fetch views and cases first since they don't depend on the current issue
  await casesStore.fetchViews();
  await casesStore.fetchCases({ page: 1, limit: 25 });
  
  // Fetch the current issue last
  if (casesStore.issueId) {
    await casesStore.fetchCurrentIssue(casesStore.issueId);
  }
})

onUnmounted(() => {
  casesStore.clearCurrentIssue();
})
</script>

<template>
  <div class="inbox-view">
    <div class="inbox-content" :class="{ 'home-selected': isHomeSelected, 'panel-minimized': isPanelMinimized, 'no-list-panel': noListPanel }">
      <InboxViews @dashboard-selected="handleDashboardSelected" @panel-minimized="handlePanelMinimized" />
      
      <!-- Show InboxCasesList when a cases view is selected (not home, not tasks) -->
      <div v-if="!isHomeSelected && objectType !== 'tasks' && route.query.view" class="cases-list-panel">
        <InboxCasesList
          :selectedView="selectedView"
          :isYourInbox="isYourInboxSelected"
          :initialLoading="false"
          @select-issue="handleSelectIssue"
          @create-case="handleCreateCase"
        />
      </div>
      
      <!-- Show TaskList when tasks view is selected -->
      <div v-else-if="!isHomeSelected && objectType === 'tasks'" class="tasks-list-panel">
        <TaskList
          :selectedView="selectedView"
          :taskType="taskType"
          @select-issue="handleSelectIssue"
          @create-case="handleCreateCase"
        />
      </div>
      
      <!-- Show full-width home when home is selected -->
      <div v-if="isHomeSelected" class="dashboard-content">
        <InboxHome />
      </div>
      
      <!-- Show normal main content when home is not selected -->
      <div v-else class="main-content">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
          <template v-if="!Component">
            <div class="zero-state-container">
              <BravoZeroStateScreen
                title="Get Started"
                message="Select a case or task from the sidebar to get started."
                :imageSrc="ZeroStateSearchSvg"
                imageAlt="Get started"
                buttonLabel="Create New Case"
                :actionHandler="handleCreateCase"
              />
            </div>
          </template>
        </router-view>
      </div>
    </div>

    <!-- Create Case Modal -->
    <CreateCaseModal
      v-model:visible="showCreateCaseModal"
      @case-created="handleCaseCreated"
    />
  </div>
</template>

<style scoped>
.inbox-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  min-width: 600px; /* Minimum width to prevent layout breaking */
  overflow-x: auto; /* Add horizontal scroll when needed */
}

.inbox-content {
  display: grid;
  grid-template-columns: minmax(240px, 300px) minmax(280px, 350px) 2fr;
  flex: 1;
  min-height: 0;
  gap: 0;
  transition: grid-template-columns 0.5s ease;
}

.inbox-content.no-list-panel:not(.home-selected) {
  grid-template-columns: minmax(240px, 300px) 2fr;
}

.inbox-content.no-list-panel.panel-minimized:not(.home-selected) {
  grid-template-columns: 60px 2fr;
}

.inbox-content.panel-minimized {
  grid-template-columns: 60px minmax(280px, 350px) 2fr;
}

.inbox-content.home-selected {
  grid-template-columns: minmax(240px, 300px) 1fr;
}

.inbox-content.home-selected.panel-minimized {
  grid-template-columns: 60px 1fr;
}

.cases-list-panel,
.tasks-list-panel {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 0;
  border-right: 1px solid var(--border-color);
}

.main-content {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.dashboard-content {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 0;
  grid-column: 2;
}

/* Responsive breakpoints - more conservative to keep panels visible longer */
@media (max-width: 1600px) {
  .inbox-content {
    grid-template-columns: minmax(220px, 280px) minmax(260px, 330px) 2fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(260px, 330px) 2fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: minmax(220px, 280px) 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 1400px) {
  .inbox-content {
    grid-template-columns: minmax(200px, 260px) minmax(240px, 300px) 1.8fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(240px, 300px) 1.8fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: minmax(200px, 260px) 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 1200px) {
  .inbox-content {
    grid-template-columns: minmax(180px, 240px) minmax(200px, 250px) 1.5fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(200px, 250px) 1.5fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: minmax(180px, 240px) 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 1150px) {
  .inbox-content {
    grid-template-columns: 60px minmax(200px, 250px) 1.5fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(200px, 250px) 1.5fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: 60px 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 1100px) {
  .inbox-content {
    grid-template-columns: 60px minmax(190px, 240px) 1.2fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(190px, 240px) 1.2fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: 60px 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 1000px) {
  .inbox-content {
    grid-template-columns: 60px minmax(180px, 220px) 1fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(180px, 220px) 1fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: 60px 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 900px) {
  .inbox-content {
    grid-template-columns: 60px minmax(170px, 210px) 1fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(170px, 210px) 1fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: 60px 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 850px) {
  .inbox-content {
    grid-template-columns: 60px minmax(160px, 200px) 1fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(160px, 200px) 1fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: 60px 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

@media (max-width: 800px) {
  .inbox-content {
    grid-template-columns: 60px minmax(140px, 180px) 1fr;
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(140px, 180px) 1fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: 60px 1fr;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
}

/* Keep all panels visible with horizontal scroll at small sizes */
@media (max-width: 600px) {
  .inbox-view {
    min-width: 800px; /* Force minimum width to maintain all panels */
  }
  
  .inbox-content {
    grid-template-columns: 60px minmax(120px, 160px) 1fr;
    min-width: 800px; /* Ensure grid doesn't collapse */
  }
  
  .inbox-content.panel-minimized {
    grid-template-columns: 60px minmax(120px, 160px) 1fr;
  }
  
  .inbox-content.home-selected {
    grid-template-columns: 60px 1fr;
    min-width: 400px;
  }
  
  .inbox-content.home-selected.panel-minimized {
    grid-template-columns: 60px 1fr;
  }
  
  .inbox-content.no-list-panel:not(.home-selected) {
    grid-template-columns: 60px 1fr;
    min-width: 400px;
  }
  
  .inbox-content.no-list-panel.panel-minimized:not(.home-selected) {
    grid-template-columns: 60px 1fr;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
}

.error-container {
  color: var(--red-500);
  padding: 1rem;
  border-radius: 4px;
  background-color: var(--red-50);
}

.issue-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.issue-field {
  margin-bottom: 0.75rem;
}

.issue-field:last-child {
  margin-bottom: 0;
}

.issue-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  color: var(--surface-600);
  padding: 2rem;
}

.empty-child-route {
  text-align: center;
  padding: 2rem;
  color: var(--surface-600);
}

.zero-state-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 2rem;
}
</style> 