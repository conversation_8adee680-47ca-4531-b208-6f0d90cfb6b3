// Status mapping for case statuses
export const statusMap = {
  '1': { label: 'New', state: 'new' },
  '2': { label: 'Ready', state: 'ready' },
  '3': { label: 'Scheduling', state: 'ready' },
  '4': { label: 'Scheduled', state: 'ready' },
  '5': { label: 'En Route', state: 'ready' },
  '6': { label: 'In Progress', state: 'ready' },
  '7': { label: 'Resolved', state: 'resolved' },
  '9': { label: 'Pending Close', state: 'resolved' },
  '10': { label: 'Closed', state: 'closed' },
  '79': { label: 'Waiting', state: 'waiting' },
  '89': { label: 'Canceled', state: 'closed' },
  '99': { label: 'Deleted', state: 'closed' }
} as const

// Type definitions for status info
export interface StatusInfo {
  label: string
  state: 'new' | 'ready' | 'resolved' | 'closed' | 'waiting'
}

/**
 * Get status information (label and state) for a given status code
 * @param status - The status code as string or undefined
 * @returns StatusInfo object with label and state
 */
export function getStatusInfo(status: string | undefined): StatusInfo {
  if (!status) return { label: 'Unknown', state: 'new' }
  return statusMap[status as keyof typeof statusMap] || { label: status, state: 'new' }
}

/**
 * Check if a case status represents a resolved state
 * @param status - The status code as string or undefined
 * @returns boolean indicating if the case is resolved
 */
export function isResolvedStatus(status: string | undefined): boolean {
  const statusInfo = getStatusInfo(status)
  return statusInfo.state === 'resolved'
}

/**
 * Check if a case status represents a closed state
 * @param status - The status code as string or undefined
 * @returns boolean indicating if the case is closed
 */
export function isClosedStatus(status: string | undefined): boolean {
  const statusInfo = getStatusInfo(status)
  return statusInfo.state === 'closed'
}

/**
 * Check if a case status represents a waiting state
 * @param status - The status code as string or undefined
 * @returns boolean indicating if the case is waiting
 */
export function isWaitingStatus(status: string | undefined): boolean {
  const statusInfo = getStatusInfo(status)
  return statusInfo.state === 'waiting'
}

/**
 * Check if a case composer should be disabled based on the case status
 * @param status - The status code as string or undefined
 * @returns boolean indicating if the composer should be disabled
 */
export function isCaseComposerDisabled(status: string | undefined): boolean {
  if (!status) return false
  const statusInfo = getStatusInfo(status)
  // Disable composer for resolved, closed (including canceled and deleted) states
  return statusInfo.state === 'resolved' || statusInfo.state === 'closed'
}

/**
 * Get the appropriate PrimeIcon class for a case source
 * @param issue - The issue/case object containing source information
 * @returns PrimeIcon class string
 */
export function getCaseIcon(issue: any): string {
  const source = issue?.source;
  if (source === 'api') return 'pi pi-code';
  if (source === 'relay') return 'pi pi-user';
  if (source === 'bots') return 'pi pi-android';
  if (source === 'connect') return 'pi pi-mobile';
  if (source === 'email') return 'pi pi-envelope';
  if (source === 'micro_connect') return 'pi-desktop';
  if (source === 'other') return 'pi pi-question-circle';
  if (source === 'phone') return 'pi pi-phone';
  if (source === 'onsite_agent') return 'pi pi-wifi';
  if (source === 'sms') return 'pi pi-mobile';
  if (source === 'salesforce') return 'pi pi-cloud';
  if (source === 'scheduler') return 'pi pi-calendar';
  if (source === 'voicemail') return 'pi pi-volume-up';
  if (source === 'web_connect') return 'pi pi-comments';
  return 'pi pi-user';
}

/**
 * Get the human-readable label for a case source
 * @param issue - The issue/case object containing source information
 * @returns Human-readable source label
 */
export function getCaseIconLabel(issue: any): string {
  const source = issue?.source;
  if (source === 'api') return 'API';
  if (source === 'relay') return 'User';
  if (source === 'bots') return 'Bots';
  if (source === 'connect') return 'Connect';
  if (source === 'email') return 'Email';
  if (source === 'micro_connect') return 'In-App Chat';
  if (source === 'other') return 'Other';
  if (source === 'phone') return 'Phone';
  if (source === 'onsite_agent') return 'Proactive Agent';
  if (source === 'sms') return 'SMS';
  if (source === 'salesforce') return 'Salesforce';
  if (source === 'scheduler') return 'Scheduler';
  if (source === 'voicemail') return 'Voicemail';
  if (source === 'web_connect') return 'Web Chat';
  return 'Case';
}

/**
 * Get team display name from case
 * @param issue - Case/issue object containing team ID
 * @param partnerTeams - Array of partner teams from store
 * @returns Team display name or fallback to team ID
 */
export function getCaseTeamName(issue: any, partnerTeams: any[]): string {
  const teamId = issue?.owner_partners_teams_id
  if (!teamId) return '—'
  
  const team = partnerTeams.find(t => t.val === teamId)
  return team?.lbl || teamId
}

/**
 * Filter available communications based on case source, following the same logic as CaseActivity.vue
 * @param availableComms Array of available communications
 * @param caseSource The source of the case (e.g., 'email', 'web_connect', 'microconnect', 'connect')
 * @returns Filtered array of available communications
 */
export function getFilteredAvailableComms(availableComms: any[], caseSource?: string) {
  if (!availableComms || availableComms.length === 0) {
    return []
  }

  let filteredComms = [...availableComms]

  // Special case: Hide internal room for specific case sources
  const hideInternalSources = ['web_connect', 'microconnect', 'connect']
  if (caseSource && hideInternalSources.includes(caseSource)) {
    filteredComms = filteredComms.filter(comm => comm.object_scope !== 'private')
  }

  // Hide Internal/private and public comms for email cases
  if (caseSource === 'email') {
    filteredComms = filteredComms.filter(comm => 
      comm.object_scope !== 'private' && comm.object_scope !== 'public'
    )
  }

  return filteredComms
}

/**
 * Get the first available communication ID after filtering, for use in navigation links
 * @param availableComms Array of available communications
 * @param caseSource The source of the case
 * @returns The ID of the first available communication, or null if none available
 */
export function getDefaultCommId(availableComms: any[], caseSource?: string): string | null {
  const filteredComms = getFilteredAvailableComms(availableComms, caseSource)
  return filteredComms.length > 0 ? filteredComms[0].id : null
}

 