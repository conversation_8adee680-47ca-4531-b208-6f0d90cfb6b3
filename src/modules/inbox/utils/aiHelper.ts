/**
 * AI-related helper functions for sentiment analysis and scoring
 */

/**
 * Get BravoTag severity for sentiment emotion
 * @param emotion - The emotion string from sentiment analysis
 * @returns Severity level for BravoTag styling
 */
export function getSentimentSeverity(emotion: string): 'success' | 'info' | 'warn' | 'danger' | undefined {
  if (!emotion) return undefined
  const emotionLower = emotion.toLowerCase()
  
  // Positive emotions
  if (emotionLower.includes('joy') || emotionLower.includes('happy') || emotionLower.includes('satisfied') || emotionLower.includes('positive')) {
    return 'success'
  }
  
  // Negative emotions
  if (emotionLower.includes('anger') || emotionLower.includes('frustrated') || emotionLower.includes('annoyed') || emotionLower.includes('upset')) {
    return 'danger'
  }
  
  // Warning emotions
  if (emotionLower.includes('concern') || emotionLower.includes('worried') || emotionLower.includes('anxious') || emotionLower.includes('disappointed')) {
    return 'warn'
  }
  
  // Default to info for neutral or unknown emotions
  return 'info'
}

/**
 * Get color for sentiment score based on score ranges
 * @param score - The sentiment score (0-100)
 * @returns CSS color value for the score
 */
export function getScoreColor(score: number): string {
  if (score >= 0 && score <= 15) {
    return '#dc3545' // Red
  } else if (score >= 16 && score <= 25) {
    return '#fd7e14' // Orange
  } else if (score >= 26 && score <= 40) {
    return '#ffc107' // Yellow
  } else if (score >= 41 && score <= 60) {
    return '#6c757d' // Grey
  } else if (score >= 61 && score <= 80) {
    return '#28a745' // Light green
  } else if (score >= 81 && score <= 100) {
    return '#155724' // Dark green
  } else {
    return '#6c757d' // Default grey for out of range values
  }
}

/**
 * Get text color for sentiment score to ensure readability
 * @param score - The sentiment score (0-100)
 * @returns CSS color value for the text
 */
export function getScoreTextColor(score: number): string {
  // Use white text for darker backgrounds, black for lighter ones
  if (score >= 0 && score <= 25) {
    return '#ffffff' // White text for red and orange
  } else if (score >= 26 && score <= 40) {
    return '#000000' // Black text for yellow
  } else if (score >= 41 && score <= 60) {
    return '#ffffff' // White text for grey
  } else if (score >= 61 && score <= 80) {
    return '#ffffff' // White text for light green
  } else if (score >= 81 && score <= 100) {
    return '#ffffff' // White text for dark green
  } else {
    return '#ffffff' // Default white for grey
  }
}

/**
 * Format sentiment score for display
 * @param score - The sentiment score (0-100)
 * @returns Formatted score string
 */
export function formatSentimentScore(score: number): string {
  return `${Math.round(score)} / 100`
}

/**
 * Format key indicators for tooltip display
 * @param keyIndicators - Array of key indicator strings
 * @returns Formatted string for tooltip
 */
export function formatKeyIndicators(keyIndicators: string[]): string {
  if (!keyIndicators || keyIndicators.length === 0) {
    return 'No key indicators available'
  }
  
  return keyIndicators.map(indicator => `• ${indicator}`).join('\n')
}

/**
 * Format confidence value for tooltip display
 * @param confidence - The confidence value (0-1)
 * @returns Formatted confidence string
 */
export function formatConfidence(confidence: number): string {
  const percentage = Math.round(confidence * 100)
  return `Confidence: ${percentage}%`
} 