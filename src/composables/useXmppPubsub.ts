import { ref, computed, onUnmounted } from 'vue'
import { xml } from '@xmpp/client'
import { useUserStore } from '@/stores/user'
import { xmppService } from '@/modules/comms/services/xmpp'
import { usePubsubEventsStore } from '@/stores/pubsubEvents'
import { useRouter } from 'vue-router'

export interface PubsubEvent {
  id: string
  timestamp: Date
  node: string
  publisher?: string
  items: any[]
  rawStanza?: string
  eventType: 'notification' | 'presence' | 'typing' | 'other'
}

export interface PubsubConnectionState {
  status: 'disconnected' | 'connecting' | 'connected' | 'authenticated' | 'error'
  error?: string
  lastConnected?: Date
  reconnectAttempts: number
}

// Notification settings
export interface NotificationSettings {
  enabled: boolean
  soundEnabled: boolean
  browserNotifications: boolean
  showPresenceUpdates: boolean
  showTypingIndicators: boolean
}

export const useXmppPubsub = () => {
  const userStore = useUserStore()
  const pubsubEventsStore = usePubsubEventsStore()
  const router = useRouter()
  
  // State
  const connectionState = ref<PubsubConnectionState>({
    status: 'disconnected',
    reconnectAttempts: 0
  })
  const events = ref<PubsubEvent[]>([])
  const isConnected = computed(() => connectionState.value.status === 'authenticated')
  const maxEvents = ref(1000) // Keep last 1000 events
  const maxReconnectAttempts = ref(10) // Increased attempts
  const reconnectDelay = ref(5000) // Increased base delay
  
  // Notification settings
  const notificationSettings = ref<NotificationSettings>({
    enabled: true,
    soundEnabled: true,
    browserNotifications: true, // Re-enabled for actual messages
    showPresenceUpdates: false, // Usually too noisy
    showTypingIndicators: true
  })
  
  // Audio for notifications
  const messageAudio = ref<HTMLAudioElement | null>(null)
  
  // Initialize audio
  if (typeof window !== 'undefined') {
    messageAudio.value = new Audio('/message.mp3')
    messageAudio.value.preload = 'auto'
    messageAudio.value.volume = 0.5 // Slightly quieter for pubsub notifications
    
    // Add error handling for audio loading
    messageAudio.value.addEventListener('error', (e) => {
      console.error('XMPP PubSub: Audio loading error:', e)
    })
    
    messageAudio.value.addEventListener('canplaythrough', () => {
      console.log('XMPP PubSub: Audio loaded and ready to play')
    })
    
    // Try to load the audio file
    messageAudio.value.load()
  }
  
  // Track if we've set up our stanza handler
  const stanzaHandlerSetup = ref(false)
  
  // Connection management
  let reconnectTimeout: ReturnType<typeof setTimeout> | null = null
  
  // Extract case ID, actual event type, and event data from event items
  const extractEventData = (event: PubsubEvent): { caseId: string | null; actualEventType: string | null; eventData: any | null } => {
    if (event.items && event.items.length > 0) {
      for (const item of event.items) {
        if (item.payload && item.payload.length > 0) {
          for (const payload of item.payload) {
            if (payload.name === 'json' && payload.children && payload.children.length > 0) {
              try {
                const jsonContent = payload.children[0]
                if (typeof jsonContent === 'string') {
                  const parsedJson = JSON.parse(jsonContent)
                  return {
                    caseId: parsedJson.object === 'issues' && parsedJson.object_id ? parsedJson.object_id : null,
                    actualEventType: parsedJson.event_type || null,
                    eventData: parsedJson.event_data || null
                  }
                }
              } catch (error) {
                console.warn('Failed to parse event JSON:', error)
              }
            }
          }
        }
      }
    }
    return { caseId: null, actualEventType: null, eventData: null }
  }

  const addEvent = (event: PubsubEvent) => {
    events.value.unshift(event)
    
    // Keep only the most recent events
    if (events.value.length > maxEvents.value) {
      events.value = events.value.slice(0, maxEvents.value)
    }
    
    // Store in pubsub events store (exclude presence events)
    if (event.eventType !== 'presence') {
      const { caseId, actualEventType, eventData } = extractEventData(event)
      pubsubEventsStore.addEvent({
        id: event.id,
        timestamp: event.timestamp,
        eventType: actualEventType || event.eventType, // Use the actual event type from JSON, fallback to original
        caseId: caseId || undefined,
        eventData: eventData || undefined,
        node: event.node,
        rawEvent: event
      })
    }
    
    // Trigger notifications for new events
    triggerNotificationForEvent(event)
  }
  
  const clearEvents = () => {
    events.value = []
  }
  
  const generateEventId = () => {
    return `pubsub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
  
  const determineEventType = (node: string, items?: any[]): PubsubEvent['eventType'] => {
    // First try to extract event type from JSON content in items
    if (items && items.length > 0) {
      for (const item of items) {
        if (item.payload && item.payload.length > 0) {
          for (const payload of item.payload) {
            if (payload.name === 'json' && payload.children && payload.children.length > 0) {
              try {
                const jsonContent = payload.children[0]
                if (typeof jsonContent === 'string') {
                  const parsedJson = JSON.parse(jsonContent)
                  if (parsedJson.event_type) {
                    // Map specific event types to our categories
                    switch (parsedJson.event_type) {
                      case 'incoming_chat':
                        return 'notification'
                      case 'claim_status':
                        return 'notification'
                      case 'typing':
                        return 'typing'
                      default:
                        return 'other'
                    }
                  }
                }
              } catch (error) {
                console.warn('XMPP PubSub: Failed to parse JSON content:', error)
              }
            }
          }
        }
      }
    }
    
    // Fallback to node-based detection
    if (node.includes('notification')) return 'notification'
    if (node.includes('presence')) return 'presence'
    if (node.includes('typing')) return 'typing'
    return 'other'
  }
  
  // Notification functions
  const playNotificationSound = () => {
    if (!notificationSettings.value.soundEnabled || !messageAudio.value) return
    
    try {
      messageAudio.value.currentTime = 0
      messageAudio.value.play().catch(error => {
        console.warn('XMPP PubSub: Could not play notification sound:', error)
        if (error.name === 'NotAllowedError') {
          console.warn('XMPP PubSub: Audio autoplay blocked by browser. User interaction required.')
        }
      })
    } catch (error) {
      console.warn('XMPP PubSub: Could not play notification sound:', error)
    }
  }
  
  const showBrowserNotification = (title: string, body: string, icon?: string, clickUrl?: string) => {
    if (!notificationSettings.value.browserNotifications) {
      return
    }
    
    // Check if browser supports notifications
    if (!('Notification' in window)) {
      console.warn('XMPP PubSub: Browser does not support notifications')
      return
    }
    
    // Check permission status
    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        body,
        icon: icon || '/favicon.ico',
        tag: 'xmpp-pubsub', // Prevents duplicate notifications
        requireInteraction: false,
        silent: false // Let the notification make sound
      })
      
      // Add click handler to route to specific URL
      if (clickUrl) {
        notification.addEventListener('click', () => {
          window.focus()
          // Use Vue Router to navigate
          try {
            router.push(clickUrl)
          } catch (error) {
            // Fallback to window.location if router navigation fails
            console.warn('XMPP PubSub: Router navigation failed, using window.location:', error)
            window.location.href = clickUrl
          }
          notification.close()
        })
      }
      
      // Auto-close after 5 seconds
      setTimeout(() => notification.close(), 5000)
    } else if (Notification.permission === 'default') {
      // Request permission
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          showBrowserNotification(title, body, icon, clickUrl)
        }
      })
    } else {
      console.warn('XMPP PubSub: Browser notifications permission denied')
    }
  }
  
  const checkIfNoopEvent = (event: PubsubEvent): boolean => {
    // Check if this is a "noop" event that shouldn't trigger notifications
    
    // Check for IQ subscription confirmation messages (administrative traffic)
    if (event.node === 'iq-pubsub' && event.items && event.items.length > 0) {
      for (const item of event.items) {
        // Check if this is an IQ result (subscription confirmation)
        if (item.iqType === 'result' && item.pubsub) {
          // Check if the pubsub content contains subscription confirmations
          if (item.pubsub.includes('subscription') && 
              (item.pubsub.includes('subscription="subscribed"') || 
               item.pubsub.includes('subscription="unsubscribed"'))) {
            return true
          }
        }
      }
    }
    
    // Look for noop indicators in the event data
    if (event.items && event.items.length > 0) {
      for (const item of event.items) {
        if (item.payload && item.payload.length > 0) {
          for (const payload of item.payload) {
            if (payload.name === 'json' && payload.children && payload.children.length > 0) {
              try {
                const jsonContent = payload.children[0]
                if (typeof jsonContent === 'string') {
                  const parsedJson = JSON.parse(jsonContent)
                  
                  // Check for noop indicators
                  if (parsedJson.event_type === 'noop' || 
                      parsedJson.type === 'noop' ||
                      parsedJson.action === 'noop' ||
                      (parsedJson.message && parsedJson.message.toLowerCase().includes('noop'))) {
                    return true
                  }
                  
                  // Check for empty or meaningless content
                  if (parsedJson.event_type === 'heartbeat' ||
                      parsedJson.event_type === 'keepalive' ||
                      parsedJson.event_type === 'ping') {
                    return true
                  }
                  
                  // For incoming_chat events, check if there's actual message content
                  if (parsedJson.event_type === 'incoming_chat') {
                    // If there's no message body or it's empty, consider it a noop
                    if (!parsedJson.message_body || 
                        parsedJson.message_body.trim() === '' ||
                        parsedJson.message_body.toLowerCase().includes('noop')) {
                      return true
                    }
                  }
                }
              } catch (error) {
                // If we can't parse JSON, assume it's not a noop
                continue
              }
            }
          }
        }
      }
    }
    
    // Check the node name for noop indicators
    if (event.node && (event.node.includes('noop') || event.node.includes('heartbeat'))) {
      return true
    }
    
    // For chat messages, check if there's actual content
    if (event.eventType === 'notification' && event.node === 'chat') {
      const messageBody = event.items[0]?.body
      if (!messageBody || messageBody.trim() === '' || messageBody.toLowerCase().includes('noop')) {
        return true
      }
    }
    
    return false
  }

  const triggerNotificationForEvent = (event: PubsubEvent) => {
    if (!notificationSettings.value.enabled) return
    
    // Skip presence events unless specifically enabled
    if (event.eventType === 'presence' && !notificationSettings.value.showPresenceUpdates) {
      return
    }
    
    // Skip typing events unless specifically enabled
    if (event.eventType === 'typing' && !notificationSettings.value.showTypingIndicators) {
      return
    }
    
    // Skip events from current user (similar to existing chat system)
    const currentUserJid = userStore.xmppData?.resource
    if (event.publisher && currentUserJid && event.publisher.includes(currentUserJid)) {
      return
    }
    
    // Filter out "noop" events - check if this is a meaningful event
    const isNoopEvent = checkIfNoopEvent(event)
    if (isNoopEvent) {
      return
    }
    
    // Skip user logged_in events - these are not relevant for notifications
    if (event.items && event.items.length > 0) {
      for (const item of event.items) {
        if (item.payload && item.payload.length > 0) {
          for (const payload of item.payload) {
            if (payload.name === 'json' && payload.children && payload.children.length > 0) {
              try {
                const jsonContent = payload.children[0]
                if (typeof jsonContent === 'string') {
                  const parsedJson = JSON.parse(jsonContent)
                  if (parsedJson.event_type === 'user_logged_in') {
                    return
                  }
                }
              } catch (error) {
                // Ignore parsing errors
              }
            }
          }
        }
      }
    }
    
    // Generate notification content based on event type
    let title = 'XMPP Event'
    let body = ''
    let clickUrl = ''
    
    switch (event.eventType) {
      case 'notification':
        if (event.node === 'chat') {
          const messageBody = event.items[0]?.body
          const senderName = event.publisher?.split('@')[0] || 'Unknown'
          title = `New Chat Message`
          body = messageBody ? messageBody : `New message from ${senderName}`
          
          // Play sound for chat messages
          playNotificationSound()
        } else {
          // Try to get the actual event type from JSON content
          let actualEventType = 'notification'
          let parsedEventData = null
          if (event.items && event.items.length > 0) {
            for (const item of event.items) {
              if (item.payload && item.payload.length > 0) {
                for (const payload of item.payload) {
                  if (payload.name === 'json' && payload.children && payload.children.length > 0) {
                    try {
                      const jsonContent = payload.children[0]
                      if (typeof jsonContent === 'string') {
                        parsedEventData = JSON.parse(jsonContent)
                        if (parsedEventData.event_type) {
                          actualEventType = parsedEventData.event_type
                          break
                        }
                      }
                    } catch (error) {
                      // Ignore parsing errors
                    }
                  }
                }
              }
            }
          }
          
          // Handle claim_status events specifically
          if (actualEventType === 'claim_status' && parsedEventData) {
            // Check if this is a claimable chat (canClaim: true)
            let shouldNotify = false
            
            if (parsedEventData.event_data && parsedEventData.event_data.tiles) {
              // Find the tile that matches the object_id (issue ID)
              const relevantTile = parsedEventData.event_data.tiles.find((tile: any) => 
                tile.issueId === parsedEventData.object_id
              )
              
              // Only notify if canClaim is true
              if (relevantTile && relevantTile.canClaim === true) {
                shouldNotify = true
              }
            }
            
            if (shouldNotify) {
              title = 'New Chat Available'
              body = 'A new chat has started and is ready to be claimed'
              
              // Set click URL to route to inbox with the issue ID
              if (parsedEventData.object_id) {
                clickUrl = `/inbox/cases/${parsedEventData.object_id}?view=chat`
              }
              
              // Play sound for claim status notifications
              playNotificationSound()
              
              // Show browser notification immediately for claim_status events
              showBrowserNotification(title, body, undefined, clickUrl)
              
              // Return early to avoid duplicate notifications
              return
            } else {
              // This is a claim_status event but not claimable (canClaim: false)
              // Skip notification by returning early
              return
            }
          } else if (actualEventType === 'incoming_chat' && parsedEventData) {
            // Handle incoming_chat events specifically
            let messageContent = ''
            let senderName = 'Unknown'
            
            // Extract clean message content from the parsed data
            if (parsedEventData.message_body) {
              messageContent = parsedEventData.message_body
            } else if (parsedEventData.message) {
              messageContent = parsedEventData.message
            }
            
            // Extract sender name from context or fallback to publisher
            if (parsedEventData.context && parsedEventData.context.from_name) {
              senderName = parsedEventData.context.from_name
            } else if (parsedEventData.from_name) {
              senderName = parsedEventData.from_name
            } else if (event.publisher) {
              senderName = event.publisher.split('@')[0] || 'Unknown'
            }
            
            // Clean up the message content - remove any JSON formatting, decode if needed
            if (messageContent) {
              try {
                // Try to decode URL-encoded content
                messageContent = decodeURIComponent(messageContent.replace(/\+/g, ' '))
              } catch (error) {
                // If decoding fails, use the original content
              }
              
              // Remove any remaining JSON artifacts or special formatting
              messageContent = messageContent.replace(/^["']|["']$/g, '') // Remove quotes
              messageContent = messageContent.trim()
              
              // Handle the "comm.xyz: message" format - extract just the message part
              const commPrefixMatch = messageContent.match(/^comm\.[^:]+:\s*(.*)$/)
              if (commPrefixMatch && commPrefixMatch[1]) {
                messageContent = commPrefixMatch[1].trim()
              }
            }
            
            title = 'New Chat Message'
            body = messageContent ? messageContent : `New message from ${senderName}`
            
            // Set click URL to route to inbox with the issue ID
            if (parsedEventData.object_id) {
              clickUrl = `/inbox/cases/${parsedEventData.object_id}?view=chat`
            }
            
            // Play sound for incoming chat notifications
            playNotificationSound()
          } else {
            // Try to get issue information for better context
            let issueId = ''
            if (event.items && event.items.length > 0) {
              for (const item of event.items) {
                if (item.payload && item.payload.length > 0) {
                  for (const payload of item.payload) {
                    if (payload.name === 'json' && payload.children && payload.children.length > 0) {
                      try {
                        const jsonContent = payload.children[0]
                        if (typeof jsonContent === 'string') {
                          const parsedJson = JSON.parse(jsonContent)
                          if (parsedJson.object === 'issues' && parsedJson.object_id) {
                            issueId = parsedJson.object_id.substring(0, 8) // Show first 8 chars
                            break
                          }
                        }
                      } catch (error) {
                        // Ignore parsing errors
                      }
                    }
                  }
                }
              }
            }
            
            title = `New ${actualEventType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`
            body = issueId 
              ? `Issue ${issueId} - ${actualEventType.replace('_', ' ')}`
              : `Event on ${event.node} from ${event.publisher?.split('@')[0] || 'Unknown'}`
          }
        }
        break
        
      case 'typing':
        const senderName = event.publisher?.split('@')[0] || 'Unknown'
        const isComposing = event.items[0]?.composing
        title = 'Typing Indicator'
        body = `${senderName} is ${isComposing ? 'typing' : 'no longer typing'}...`
        break
        
      case 'presence':
        const userName = event.publisher?.split('@')[0] || 'Unknown'
        const presenceType = event.items[0]?.type
        title = 'Presence Update'
        body = `${userName} is now ${presenceType === 'unavailable' ? 'offline' : 'online'}`
        break
        
      default:
        // Try to get the actual event type from JSON content for 'other' events
        let actualEventType = event.eventType
        if (event.items && event.items.length > 0) {
          for (const item of event.items) {
            if (item.payload && item.payload.length > 0) {
              for (const payload of item.payload) {
                if (payload.name === 'json' && payload.children && payload.children.length > 0) {
                  try {
                    const jsonContent = payload.children[0]
                    if (typeof jsonContent === 'string') {
                      const parsedJson = JSON.parse(jsonContent)
                      if (parsedJson.event_type) {
                        actualEventType = parsedJson.event_type
                        break
                      }
                    }
                  } catch (error) {
                    // Ignore parsing errors
                  }
                }
              }
            }
          }
        }
        
        // Try to get issue information for better context
        let issueId = ''
        if (event.items && event.items.length > 0) {
          for (const item of event.items) {
            if (item.payload && item.payload.length > 0) {
              for (const payload of item.payload) {
                if (payload.name === 'json' && payload.children && payload.children.length > 0) {
                  try {
                    const jsonContent = payload.children[0]
                    if (typeof jsonContent === 'string') {
                      const parsedJson = JSON.parse(jsonContent)
                      if (parsedJson.object === 'issues' && parsedJson.object_id) {
                        issueId = parsedJson.object_id.substring(0, 8) // Show first 8 chars
                        break
                      }
                    }
                  } catch (error) {
                    // Ignore parsing errors
                  }
                }
              }
            }
          }
        }
        
        title = 'XMPP Event'
        body = issueId 
          ? `Issue ${issueId} - ${actualEventType.replace('_', ' ')}`
          : `New ${actualEventType.replace('_', ' ')} event on ${event.node}`
    }
    
    // Show browser notification
    showBrowserNotification(title, body, undefined, clickUrl)
  }
  
  // Settings management
  const updateNotificationSettings = (newSettings: Partial<NotificationSettings>) => {
    notificationSettings.value = { ...notificationSettings.value, ...newSettings }
  }
  
  const testNotificationSound = () => {
    playNotificationSound()
  }
  
  const testBrowserNotification = () => {
    showBrowserNotification('Test Notification', 'This is a test notification from XMPP PubSub monitor')
  }
  
  const handleStanza = (stanza: any) => {
    try {
      // Handle IQ error responses (including PubSub subscription errors)
      if (stanza.is('iq') && stanza.attrs.type === 'error') {
        const errorElement = stanza.getChild('error')
        if (errorElement) {
          console.warn('XMPP PubSub: Received IQ error response:', {
            from: stanza.attrs.from,
            to: stanza.attrs.to,
            id: stanza.attrs.id,
            errorType: errorElement.attrs.type,
            errorCondition: errorElement.children.map((child: any) => child.name).join(', '),
            fullStanza: stanza.toString()
          })
        }
        return // Don't process error stanzas further
      }
      
      // Handle message stanzas
      if (stanza.is('message')) {
        const pubsubEvent = stanza.getChild('event', 'http://jabber.org/protocol/pubsub#event')
        if (pubsubEvent) {
          // This is a pubsub event message
          const items = pubsubEvent.getChild('items')
          if (items) {
            const node = items.attrs.node
            const itemElements = items.getChildren('item')
            
            const parsedItems = itemElements.map((item: any) => ({
              id: item.attrs.id,
              payload: item.children
            }))
            
            const event: PubsubEvent = {
              id: generateEventId(),
              timestamp: new Date(),
              node: node || 'unknown',
              publisher: stanza.attrs.from,
              items: parsedItems,
              rawStanza: stanza.toString(),
              eventType: determineEventType(node || '', parsedItems)
            }
            
            addEvent(event)
            return // Don't process further if this was a pubsub event
          }
        } else {
          // This is a regular chat message
          const body = stanza.getChildText('body')
          const from = stanza.attrs.from
          const type = stanza.attrs.type || 'chat'
          
          // Skip empty messages or system messages
          if (body || stanza.getChild('composing') || stanza.getChild('paused')) {
            const event: PubsubEvent = {
              id: generateEventId(),
              timestamp: new Date(),
              node: 'chat',
              publisher: from,
              items: [{
                type: type,
                body: body,
                composing: !!stanza.getChild('composing'),
                paused: !!stanza.getChild('paused'),
                thread: stanza.getChildText('thread'),
                subject: stanza.getChildText('subject')
              }],
              rawStanza: stanza.toString(),
              eventType: stanza.getChild('composing') || stanza.getChild('paused') ? 'typing' : 'notification'
            }
            
            addEvent(event)
          }
        }
      }
      
      // Handle presence updates (but skip MUC room presence)
      if (stanza.is('presence')) {
        const from = stanza.attrs.from
        const type = stanza.attrs.type
        
        // Skip MUC presence (conference rooms) but allow user presence
        // MUC rooms typically have 'conference' in the domain
        const isFromUser = from && !from.includes('conference')
        
        if (isFromUser) {
          const event: PubsubEvent = {
            id: generateEventId(),
            timestamp: new Date(),
            node: 'presence',
            publisher: from,
            items: [{
              type: type || 'available',
              show: stanza.getChildText('show'),
              status: stanza.getChildText('status'),
              priority: stanza.getChildText('priority')
            }],
            rawStanza: stanza.toString(),
            eventType: 'presence'
          }
          
          addEvent(event)
        }
      }
      
      // Handle IQ stanzas (could contain pubsub responses)
      if (stanza.is('iq')) {
        const pubsub = stanza.getChild('pubsub', 'http://jabber.org/protocol/pubsub')
        if (pubsub) {
          const event: PubsubEvent = {
            id: generateEventId(),
            timestamp: new Date(),
            node: 'iq-pubsub',
            publisher: stanza.attrs.from,
            items: [{ iqType: stanza.attrs.type, pubsub: pubsub.toString() }],
            rawStanza: stanza.toString(),
            eventType: 'other'
          }
          
          addEvent(event)
        }
      }
      
    } catch (error) {
      console.error('XMPP PubSub: Error handling stanza:', error)
      
      // Add error event
      const errorEvent: PubsubEvent = {
        id: generateEventId(),
        timestamp: new Date(),
        node: 'error',
        items: [{ error: error instanceof Error ? error.message : 'Unknown error', stanza: stanza.toString() }],
        rawStanza: stanza.toString(),
        eventType: 'other'
      }
      
      addEvent(errorEvent)
    }
  }
  
  const setupStanzaHandler = (): boolean => {
    if (stanzaHandlerSetup.value) {
      return true
    }
    
    // Access the existing XMPP client
    const client = (xmppService as any).client
    const connected = (xmppService as any).connected
    
    if (!client || !connected) {
      console.warn('XMPP PubSub: XMPP service not ready', {
        hasClient: !!client,
        connected: connected,
        serviceKeys: Object.keys(xmppService || {})
      })
      return false
    }
    
    // Add our stanza handler to the existing client
    client.on('stanza', handleStanza)
    stanzaHandlerSetup.value = true
    
    return true
  }
  
  const connect = async () => {
    if (!userStore.xmppData) {
      console.error('XMPP PubSub: No XMPP data available')
      connectionState.value = { ...connectionState.value, status: 'error', error: 'No XMPP configuration available' }
      return
    }

    try {
      connectionState.value = {
        status: 'connecting',
        lastConnected: new Date(),
        reconnectAttempts: 0
      }
      
      // Check if main XMPP service is already initialized
      const isXmppReady = (xmppService as any).connected && (xmppService as any).client
      
      if (isXmppReady) {
        const success = setupStanzaHandler()
        
        if (success) {
          connectionState.value = {
            status: 'authenticated',
            lastConnected: new Date(),
            reconnectAttempts: 0
          }
          
          // Subscribe to configured pubsub node if available
          setTimeout(async () => {
            try {
              const configuredNode = userStore.xmppData?.pubsub_node
              if (configuredNode) {
                await subscribeToNodes([configuredNode])
              }
            } catch (error) {
              console.warn('XMPP PubSub: Node subscription failed (this is expected in some environments):', error instanceof Error ? error.message : 'Unknown error')
            }
          }, 1000)
        }
      } else {
        // Initialize the main XMPP service ourselves
        if (!userStore.xmppData) {
          throw new Error('No XMPP configuration available')
        }
        
        // Create a minimal config for XMPP initialization
        const xmppConfig = {
          clientInstance: 'pubsub-instance',
          csrfToken: 'pubsub-token',
          serviceConfig: userStore.xmppData,
          requestId: 'pubsub-request',
          availableComm: [],
          members_locations_id: 'pubsub-location',
          members_users_id: 'pubsub-user',
          members_id: 'pubsub-member'
        }
        
        await xmppService.initialize(xmppConfig)
        
        const success = setupStanzaHandler()
        
        if (success) {
          connectionState.value = {
            status: 'authenticated',
            lastConnected: new Date(),
            reconnectAttempts: 0
          }
          
          // Subscribe to configured pubsub node if available
          setTimeout(async () => {
            try {
              const configuredNode = userStore.xmppData?.pubsub_node
              if (configuredNode) {
                await subscribeToNodes([configuredNode])
              }
            } catch (error) {
              console.warn('XMPP PubSub: Node subscription failed (this is expected in some environments):', error instanceof Error ? error.message : 'Unknown error')
            }
          }, 2000)
        } else {
          throw new Error('Failed to set up stanza handler after XMPP initialization')
        }
      }
      
    } catch (error) {
      console.error('XMPP PubSub: Connection setup failed:', error)
      connectionState.value = { 
        ...connectionState.value, 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Connection setup failed' 
      }
    }
  }
  
  const disconnect = async () => {
    // Clear reconnection timeout
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout)
      reconnectTimeout = null
    }
    
    // Remove our stanza handler from the existing client
    if (stanzaHandlerSetup.value) {
      const client = (xmppService as any).client
      if (client) {
        client.removeListener('stanza', handleStanza)
      }
      stanzaHandlerSetup.value = false
    }
    
    connectionState.value = { status: 'disconnected', reconnectAttempts: 0 }
  }
  
  const attemptReconnection = () => {
    if (connectionState.value.reconnectAttempts >= maxReconnectAttempts.value) {
      connectionState.value = { 
        ...connectionState.value, 
        status: 'error', 
        error: 'Max reconnection attempts reached' 
      }
      return
    }
    
    // Clear any existing timeout
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout)
      reconnectTimeout = null
    }
    
    const delay = reconnectDelay.value * (connectionState.value.reconnectAttempts + 1)
    connectionState.value = { 
      ...connectionState.value, 
      reconnectAttempts: connectionState.value.reconnectAttempts + 1 
    }
    
    reconnectTimeout = setTimeout(async () => {
      try {
        await connect()
      } catch (error) {
        console.error('XMPP PubSub: Reconnection attempt failed:', error)
      }
    }, delay)
  }
  
  const subscribeToNodes = async (nodes: string[]) => {
    const client = (xmppService as any).client
    const connected = (xmppService as any).connected
    
    if (!client || !connected) {
      console.warn('XMPP PubSub: Cannot subscribe - main XMPP service not connected')
      return
    }
    
    for (const node of nodes) {
      try {
        // Use pubsub_service if available, otherwise fall back to service_name
        const pubsubService = userStore.xmppData?.pubsub_service || userStore.xmppData?.service_name
        
        const subscribeIq = xml(
          'iq',
          { type: 'set', to: pubsubService },
          xml('pubsub', { xmlns: 'http://jabber.org/protocol/pubsub' },
            xml('subscribe', {
              node,
              jid: client.jid?.toString()
            })
          )
        )
        
        await client.send(subscribeIq)
        
        // Add small delay between subscriptions to avoid overwhelming server
        await new Promise(resolve => setTimeout(resolve, 200))
        
      } catch (error) {
        console.warn(`XMPP PubSub: Failed to subscribe to node ${node}:`, {
          message: error instanceof Error ? error.message : 'Unknown error',
          error
        })
        // Don't fail the whole process if one subscription fails
      }
    }
  }
  
  const unsubscribeFromNodes = async (nodes: string[]) => {
    const client = (xmppService as any).client
    const connected = (xmppService as any).connected
    
    if (!client || !connected) {
      console.warn('XMPP PubSub: Cannot unsubscribe - main XMPP service not connected')
      return
    }
    
    for (const node of nodes) {
      try {
        const unsubscribeIq = xml(
          'iq',
          { type: 'set', to: userStore.xmppData?.service_name },
          xml('pubsub', { xmlns: 'http://jabber.org/protocol/pubsub' },
            xml('unsubscribe', {
              node,
              jid: client.jid?.toString()
            })
          )
        )
        
        await client.send(unsubscribeIq)
        
      } catch (error) {
        console.error(`XMPP PubSub: Failed to unsubscribe from node ${node}:`, error)
      }
    }
  }
  
  // Cleanup on unmount
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    // State
    connectionState: computed(() => connectionState.value),
    events: computed(() => events.value),
    isConnected,
    
    // Actions
    connect,
    disconnect,
    clearEvents,
    subscribeToNodes,
    unsubscribeFromNodes,
    
    // Configuration
    maxEvents,
    maxReconnectAttempts,
    reconnectDelay,
    
    // Notification settings
    notificationSettings,
    updateNotificationSettings,
    testNotificationSound,
    testBrowserNotification
  }
} 