import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export type CaseStatus = 'new' | 'ready' | 'active' | 'closed';

export interface Issue {
  id: string;
  display_name: string;
  reference_num?: string;
  c__name?: string;
  c__contact_email?: string | null;
  c__location?: string | null;
  m__contact_phone?: string;
  members_locations_id?: string;
  sponsor_partners_id?: string;
  owner_partners_id?: string;
  owner_partners_teams_id?: string;
  sponsor_partners_teams_id?: string;
  collaborator_partners_teams_ids?: string[];
  escalated?: boolean;
  c__d_escalation_reasons?: string;
  availableComm: any[]; // We'll type this more specifically once we see the data
  source?: string;
  source_data?: {
    source: string;
  };
  journey_id?: string | null;
  journey_workflow_id?: string | null;
  object_source?: string;
  updated?: string;
  location?: { 
    name?: string;
    phone?: string;
    site_name?: string;
  };
  status?: string;
  resolution?: number;
  c__d_status?: string;
  c__d_resolution?: string;
  memberUser?: {
    first_name?: string;
    last_name?: string;
    full_name?: string;
    email?: string;
    sms_number?: string;
    [key: string]: any;
  };
  member?: {
    email?: string;
    name?: string;
    [key: string]: any;
  };
  // Additional fields that exist in the actual data
  short_description?: string;
  description?: string;
  // Additional fields for token replacement
  c__technology_label?: string;
  scheduled_time?: string;
  survey_name?: string;
  c__contact_name?: string;
  c__contact_sms?: string;
  c__location_phone?: string;
  c__owner_user_name?: string;
  c__owner_user_email?: string;
  c__technician_name?: string;
  c__owner_partner_name?: string;
  c__owner_partner_team_id?: string;
}

export interface FetchCasesParams {
  page?: number;
  limit?: number;
  sort?: Array<{
    property: string;
    direction: 'ASC' | 'DESC';
  }>;
  filter?: Array<{
    property: string;
    value: any;
    operator?: string
  }>;
  start?: number;
  status?: CaseStatus;
}

export interface IssueResponse {
  success: boolean;
  message?: string;
  issues?: {
    totalCount: number;
    totalOpenCount?: number;
    results: Issue[];
  };
}

export interface FetchCasesResult {
  results: Issue[];
  totalCount: number;
}

export interface UpdateCaseParams {
  id: string;
  resolution?: string;
  isResolved?: number;
  members_users_id?: string;
  customer_survey_checklist?: string;
  owner_users_id?: string | null;
  idr_resolution?: string;
  req_multi_select_test?: string[];
  journey_id?: string | null;
  journey_workflow_id?: string | null;
  members_id?: string;
  members_locations_id?: string;
  c__name?: string;
  display_name?: string;
}

export interface UpdateCaseResponse {
  success: boolean;
  message?: string;
  issues?: Issue;
}

export interface CreateCaseParams {
  display_name: string;
  members_id: string;
  members_locations_id: string;
  members_users_id: string;
  owner_partners_teams_id: string;
  type?: number;
  status?: number;
  comments?: string;
}

export interface CreateCaseResponse {
  success: boolean;
  message?: string;
  issues?: Issue;
}

export interface DeleteCaseParams {
  id: string;
}

export interface DeleteCaseResponse {
  success: boolean;
  message?: string;
}

export interface EscalateCaseParams {
  id: string;
  escalated: boolean;
  _escalation_team_id: string;
  _escalation_user_id?: string;
  escalation_reasons: string;
  escalation_notes?: string;
}

export interface EscalateCaseResponse {
  success: boolean;
  message?: string;
  issues?: Issue;
}

export interface DeEscalateCaseParams {
  id: string;
  escalated: boolean;
  _escalation_team_id: string;
  _escalation_user_id?: string;
  escalation_reasons: string;
  escalation_notes?: string;
  _deescalation_team_id?: string;
  _deescalation_user_id?: string;
  deescalation_notes?: string;
}

export interface DeEscalateCaseResponse {
  success: boolean;
  message?: string;
  issues?: Issue;
}

export interface EventPresetGroup {
  val: string
  lbl: string
  id: string
}

export interface EventPreset {
  val: string
  lbl: string
  reference: string
  id: string
}

export interface DurationOptionsResponse {
  success: boolean
  pl__issues_events_preset_groups: EventPresetGroup[]
  pl__issues_events_presets: EventPreset[]
  current_server_time: string
}

export interface FetchEventPresetsParams {
  filter?: Array<{
    property: string;
    value: string;
  }>;
  page?: number;
  start?: number;
  limit?: number;
}

export interface SchemaField {
  layout_id: string;
  creator_user_id: string | null;
  position: number;
  type: number;
  field: string;
  data: any;
  created: string;
  updated: string;
  metadata: any;
  c__creator_user_id: string | null;
  c__condition: string;
  _highlighted: boolean;
  _highlightmap: Record<string, any>;
  conditions: any[];
  object: string;
  fieldType?: string;
  decimalPlaces?: number;
  required: boolean;
  requiredResolve: boolean;
  readOnly: boolean;
  lbl: string;
  options?: Array<{
    id?: string;
    lbl: string;
    val: string;
    default?: boolean;
  }>;
}

export interface CaseMeta {
  alt_schema: Record<string, SchemaField[]>;
  schema: SchemaField[];
  control: any;
}

export interface IssueWithSchemaResponse {
  success: boolean;
  message?: string;
  issues?: {
    totalCount: number;
    results: Issue[];
  };
  case_meta?: CaseMeta;
  current_server_time?: string;
}

// Add Thread (Start Communication Channel) types
export interface AddThreadParams {
  id: string; // Issue ID
  scope: 'email' | 'chat' | 'sms' | 'voice';
}

export interface AddThreadResponse {
  success: boolean;
  // TODO: Define the actual response structure when we know what the API returns
  [key: string]: any;
}

/**
 * Composable that provides access to Issues API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */
export function useIssuesAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async fetchIssue(issueId: string): Promise<Issue> {
      const data = await httpClient.get<IssueResponse>('admin/v4/issues/', {
        'sAction': 'listingCases',
        'id': issueId
      });
      
      if (data.success && data.issues?.results[0]) {
        return data.issues.results[0];
      }
      
      throw new Error(data.message || 'Failed to fetch issue');
    },

    async fetchIssueWithSchema(issueId: string): Promise<{ issue: Issue; schema: SchemaField[]; caseMeta?: CaseMeta }> {
      const data = await httpClient.get<IssueWithSchemaResponse>('admin/v4/issues/', {
        'sAction': 'listing',
        'sort': JSON.stringify([{ property: 'updated', direction: 'DESC' }]),
        'filter': JSON.stringify([
          { property: 'id', value: issueId },
          { property: 'members_id', value: '_no_filter_' },
          { property: 'technicians_id', value: '_no_filter_' },
          { property: 'technicians_users_id', value: '_no_filter_' },
          { property: 'sponsor_partners_id', value: '_no_filter_' },
          { property: 'members_locations_id', value: '_no_filter_' },
          { property: 'dict_id', value: '_no_filter_' }
        ])
      });
      
      console.log('📋 IssuesAPI: fetchIssueWithSchema response for issueId:', issueId, {
        success: data.success,
        hasIssues: !!data.issues,
        hasResults: !!data.issues?.results,
        resultsLength: data.issues?.results?.length || 0,
        hasFirstResult: !!data.issues?.results?.[0],
        hasCaseMeta: !!data.case_meta,
        hasSchema: !!data.case_meta?.schema,
        schemaLength: data.case_meta?.schema?.length || 0,
        hasAltSchema: !!data.case_meta?.alt_schema,
        altSchemaKeys: data.case_meta?.alt_schema ? Object.keys(data.case_meta.alt_schema) : [],
        message: data.message
      });
      
      // Debug: Log the raw case_meta for cases with no schema
      if (data.case_meta && (!data.case_meta.schema || data.case_meta.schema.length === 0)) {
        console.log('🐛 IssuesAPI: Case has no schema fields. Raw case_meta:', data.case_meta);
        console.log('🐛 IssuesAPI: Case details:', {
          caseId: issueId,
          caseDisplayName: data.issues?.results?.[0]?.display_name,
          caseStatus: data.issues?.results?.[0]?.status,
          caseLocationId: data.issues?.results?.[0]?.members_locations_id,
          rawCase: data.issues?.results?.[0]
        });
      }
      
      if (data.success && data.issues?.results[0]) {
        let schema = data.case_meta?.schema || [];
        
        // Fallback: If main schema is empty, try to use alt_schema.issues
        if (schema.length === 0 && data.case_meta?.alt_schema?.issues) {
          schema = data.case_meta.alt_schema.issues;
          console.log('📋 IssuesAPI: Using alt_schema fallback, found', schema.length, 'fields');
        }
        
        return {
          issue: data.issues.results[0],
          schema: schema,
          caseMeta: data.case_meta
        };
      }
      
      // More detailed error message
      const errorDetails = [];
      if (!data.success) {
        errorDetails.push(`API call failed (success: ${data.success})`);
      }
      if (!data.issues?.results?.[0]) {
        errorDetails.push(`No issue found with ID: ${issueId}`);
      }
      
      const detailMessage = errorDetails.length > 0 ? ` - ${errorDetails.join(', ')}` : '';
      throw new Error((data.message || 'Failed to fetch issue with schema') + detailMessage);
    },

    async fetchViews(): Promise<any> {
      const data = await httpClient.get<IssueResponse>('admin/v4/settings/', {
        'sAction': 'viewsListing',
        page: '1',
        start: '0',
        limit: '50',
      });
    
      if (data.success && data.issues?.results) {
        return data.issues.results;
      }
      
      throw new Error(data.message || 'Failed to fetch issue');
    },

    async fetchCases(
      params: FetchCasesParams,
    ): Promise<FetchCasesResult> {
      const data = await httpClient.get<IssueResponse>('admin/v4/issues/', {
        'sAction': 'listingCases',
        page: params.page?.toString() || '1',
        start: params.start?.toString() || '0',
        limit: params.limit?.toString() || '25',
        sort: params.sort
          ? JSON.stringify(
            params.sort.map(s => ({
              property: s.property,
              direction: s.direction,
            }))
          )
          : '[{"property":"updated","direction":"DESC"}]',
        filter: params.filter
          ? JSON.stringify(
            params.filter.map(f => ({
              property: f.property,
              value: f.value,
              operator: f.operator, 
            }))
          )
          : '[]',
      });
    
      if (data.success && data.issues?.results) {
        return {
          results: data.issues.results,
          totalCount: data.issues.totalCount || 0
        };
      }
      
      throw new Error(data.message || 'Failed to fetch issue');
    },

    async fetchMyWorkIssues(fullObject = false): Promise<any> {
      const data = await httpClient.get<any>('admin/v4/issues/', {
        sAction: 'myWork',
        page: '1',
        start: '0',
        limit: '25',
        sort: '[{"property":"created","direction":"DESC"}]'
      });

      if (data.success && data.issues?.ready) {
        return fullObject ? data.issues.ready : data.issues.ready.results;
      }
      throw new Error(data.message || 'Failed to fetch my work issues');
    },

    async addNote(issues_id: string, note: string): Promise<any> {
      const payload = new URLSearchParams();
      payload.append('issues_log', JSON.stringify([
        {
          issues_id,
          notes: note,
          diff_log: '',
          visibility: '1,3,4'
        }
      ]));
      payload.append('stringify', 'false');
      const data = await httpClient.post<any>(
        'admin/v4/issues/',
        payload,
        { sAction: 'putLog' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      if (data.success && data.issues_log?.results?.[0]) {
        return data.issues_log.results[0];
      }
      throw new Error(data.message || 'Failed to add note');
    },

    async fetchMemberCases(memberId: string, locationId: string, orgId: string): Promise<{ cases: Issue[]; totalCount: number; totalOpenCount: number }> {
      const data = await httpClient.get<IssueResponse>('admin/v4/issues/', {
        'sAction': 'listingCases',
        'limit': '5',
        'page': '1',
        'start': '0',
        'totalOpenCount': '1',
        'sort': JSON.stringify([
          { property: 'updated', direction: 'DESC' },
          { property: 'status', direction: 'ASC' }
        ]),
        'filter': JSON.stringify([
          { property: 'members_id', value: memberId },
          { property: 'context_org_id', value: orgId },
          { property: 'members_locations_id', value: locationId }
        ])
      });
      
      if (data.success && data.issues?.results) {
        return {
          cases: data.issues.results,
          totalCount: data.issues.totalCount,
          totalOpenCount: data.issues.totalOpenCount || 0
        };
      }
      
      throw new Error(data.message || 'Failed to fetch member cases');
    },

    async updateCase(params: UpdateCaseParams): Promise<Issue> {
      const payload = new URLSearchParams();
      payload.append('issues', JSON.stringify([params]));
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<UpdateCaseResponse>(
        'admin/v4/issues/',
        payload,
        { sAction: 'put' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success && data.issues) {
        return data.issues;
      }
      
      throw new Error(data.message || 'Failed to update case');
    },

    async createCase(params: CreateCaseParams): Promise<Issue> {
      // Create case payload based on the example API call
      const casePayload = {
        type: params.type || 2, // Default case type
        comments: params.comments || "",
        id: "boomtown.model.issues.IssuesModel-3", // This seems to be a temp ID for new cases
        parent_id: "",
        reference_num: "",
        display_name: params.display_name,
        members_id: params.members_id,
        members_locations_id: params.members_locations_id,
        members_users_id: params.members_users_id,
        technicians_users_id: "",
        members_devices_id: "",
        sponsor_partners_teams_id: "",
        owner_partners_teams_id: params.owner_partners_teams_id,
        owner_users_id: null,
        reviewer_users_id: "",
        technicians_id: "",
        lookup_users: [],
        users_id: "",
        partners_id: "",
        partners_teams_id: "",
        partners_users_id: "",
        active_users_role: "",
        cc_users_id: "",
        remote_id: "",
        scheduled_time_ts: "",
        m__preferred_time_frame: "",
        m__alternate_time_frame: "",
        category: "",
        details: "",
        status: params.status || 0, // Default status
        _transition: "",
        canWatch: false,
        canClaim: false,
        watching: false,
        escalated: false,
        _escalation_team_id: "",
        _escalation_user_id: "",
        _escalation_set_owner: false,
        _issue_resolved_alert: "",
        job: 0,
        resolution: "0",
        receivable: 0,
        payable: 0,
        subtotal: "",
        comp: 0,
        payout_total: 0,
        created: null,
        updated: "",
        resolved: "",
        reviewed: "",
        last_receivable_event: "",
        last_payable_event: "",
        special_notes: "",
        special_notes_visibility: "",
        tech_support_minutes: 0,
        total_tech_support_minutes: 0,
        c__member_rating_nps: "",
        c__member_rating_score: "",
        c__d_status: "",
        _stage_complete: false,
        _stage_previous: false,
        snooze_until_preset_group: "now",
        snooze_until_preset: "now_p4_hrs",
        snooze_until_ts: null,
        snooze_reason_tags: "",
        snooze_alert_collaborators: false,
        snooze_notes: "",
        customer_survey_sms: false,
        customer_survey_checklist: null,
        _activeChat: false,
        _viewing: false,
        unclaimed: false,
        source: "",
        issue_status_cls: "",
        issue_created: "Invalid date",
        issue_scheduled: "Not Set",
        _merchantId: "",
        _title: "",
        _label: "",
        isParentIssue: false,
        switcherTag: "?",
        c__last_interaction_event: { typeLabel: "", age: "" },
        c__name: "", // This will be populated from the customer location
        req_multi_select_test: []
      };

      const payload = new URLSearchParams();
      payload.append('issues', JSON.stringify([casePayload]));
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<CreateCaseResponse>(
        'admin/v4/issues/',
        payload,
        { sAction: 'put' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success && data.issues) {
        return data.issues;
      }
      
      throw new Error(data.message || 'Failed to create case');
    },

    async deleteCase(params: DeleteCaseParams): Promise<DeleteCaseResponse> {
      const payload = new URLSearchParams();
      payload.append('issues', JSON.stringify([{ id: params.id }]));
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<DeleteCaseResponse>(
        'admin/v4/issues/',
        payload,
        { sAction: 'delete' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.message || 'Failed to delete case');
    },

    async escalateCase(params: EscalateCaseParams): Promise<EscalateCaseResponse> {
      const payload = new URLSearchParams();
      payload.append('id', params.id);
      payload.append('escalated', params.escalated.toString());
      payload.append('_escalation_team_id', params._escalation_team_id);
      if (params._escalation_user_id) {
        payload.append('_escalation_user_id', params._escalation_user_id);
      }
      payload.append('escalation_reasons', params.escalation_reasons);
      if (params.escalation_notes) {
        payload.append('escalation_notes', params.escalation_notes);
      }
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<EscalateCaseResponse>(
        'admin/v4/issues/',
        payload,
        { sAction: 'escalateUpdate' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.message || 'Failed to escalate case');
    },

    async deEscalateCase(params: DeEscalateCaseParams): Promise<DeEscalateCaseResponse> {
      const payload = new URLSearchParams();
      payload.append('id', params.id);
      payload.append('escalated', params.escalated.toString());
      
      // For de-escalation, we clear the escalation fields
      payload.append('_escalation_team_id', '');
      payload.append('_escalation_user_id', '');
      payload.append('escalation_reasons', '');
      payload.append('escalation_notes', '');
      
      // Add new team/user assignment if provided
      if (params._deescalation_team_id) {
        payload.append('_deescalation_team_id', params._deescalation_team_id);
      }
      if (params._deescalation_user_id) {
        payload.append('_deescalation_user_id', params._deescalation_user_id);
      }
      if (params.deescalation_notes) {
        payload.append('deescalation_notes', params.deescalation_notes);
      }
      
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<DeEscalateCaseResponse>(
        'admin/v4/issues/',
        payload,
        { sAction: 'escalateUpdate' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.message || 'Failed to de-escalate case');
    },

    async fetchEventPresets(params: FetchEventPresetsParams = {}): Promise<DurationOptionsResponse> {
      const data = await httpClient.get<DurationOptionsResponse>('admin/v4/issues/', {
        sAction: 'getEventPresets',
        page: params.page?.toString() || '1',
        start: params.start?.toString() || '0',
        limit: params.limit?.toString() || '100',
        filter: params.filter ? JSON.stringify(params.filter) : '[]'
      });
      
      if (data.success) {
        return data;
      }
      
      throw new Error('Failed to fetch event presets');
    },

    async changeCustomer(caseId: string, customer: { members_id: string; members_locations_id: string; c__name: string }): Promise<Issue> {
      const payload = new URLSearchParams();
      payload.append('issues', JSON.stringify([{
        id: caseId,
        members_id: customer.members_id,
        members_locations_id: customer.members_locations_id,
        c__name: customer.c__name
      }]));
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<UpdateCaseResponse>(
        'admin/v4/issues/',
        payload,
        { sAction: 'put' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success && data.issues) {
        return data.issues;
      }
      
      throw new Error(data.message || 'Failed to change customer');
    },

    /**
     * Start a new communication channel for an issue
     */
    async addThread(params: AddThreadParams): Promise<AddThreadResponse> {
      const data = await httpClient.get<AddThreadResponse>('admin/v4/issues/', {
        sAction: 'addThread',
        id: params.id,
        scope: params.scope
      });
      
      if (data.success) {
        return data;
      }
      
      throw new Error('Failed to add thread');
    }
  };
} 