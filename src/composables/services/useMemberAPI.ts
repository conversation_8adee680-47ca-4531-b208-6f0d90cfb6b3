import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface MemberProduct {
  id: string;
  name: string;
  status: string;
  created: string;
  updated: string;
  label?: string;
  lbl?: string;
  make?: string;
  model?: string;
  c__d_status?: string;
  c__d_active?: string;
  c__d_type?: string;
  // Add other fields as needed
}

export interface MemberProductsResponse {
  success: boolean;
  message?: string;
  members_devices?: {
    results: MemberProduct[];
  };
}

export interface FetchMemberProductsParams {
  memberId: string;
  locationId: string;
  orgId: string;
  limit?: number;
}

export interface MemberLocation {
  id: string;
  sub_account_id: string;
  members_id: string;
  originating_partners_id: string;
  segment: any[];
  street_1: string;
  street_2: string;
  city: string;
  state_id: number;
  zipcode: string;
  country_id: number;
  latitude: number;
  longitude: number;
  time_zone: string;
  contact: string;
  phone: string | null;
  notes: string;
  site_name: string;
  site_id: number;
  merchant_ids: string;
  created: string;
  updated: string;
  status: number;
  active: boolean;
  external_id: string | null;
  state: string;
  country: string;
  device_active_cnt: number;
  device_inactive_cnt: number;
  issues_open_cnt: number;
  issues_total_cnt: number;
  c__address: string;
  c__address_multiline: string;
  c__name: string;
  c__nickname: string;
  c__email: string | null;
  c__contact_email: string | null;
  c__lbl: string;
  c__d_state_id: string;
  c__case_summary: string;
  c__location_count: number;
  c__user_count: number;
  c__technology_count: number;
  c__d_status: string;
  c__phone: string | null;
  _highlighted: boolean;
  _highlightmap: Record<string, string>;
  avatar: string;
  // Add other fields as needed
}

export interface CustomerLocationsResponse {
  success: boolean;
  message?: string;
  members_locations?: {
    results: MemberLocation[];
    totalCount: number;
  };
  oFields?: string[];
  rid?: string | null;
}

export interface MemberUser {
  id: string;
  members_id: string;
  members_locations_id: string;
  first_name: string;
  last_name: string;
  email: string;
  full_name: string;
  public_name: string;
  active: boolean;
  status: number;
  sms_number?: string;
  // Add other fields as needed
}

export interface MemberUsersResponse {
  success: boolean;
  message?: string;
  members_users?: {
    results: MemberUser[];
    totalCount: number;
  };
  oFields?: string[];
  rid?: string | null;
}

export interface FetchMemberUsersParams {
  members_id: string;
  members_locations_id: string;
  context_org_id: string;
}

export interface UpdateMemberParams {
  id: string;
  context_org_id: string;
  [key: string]: any; // Allow any additional fields to be updated
}

export interface UpdateMemberResponse {
  success: boolean;
  message?: string;
  members?: any;
}

export interface UpdateLocationParams {
  id: string;
  context_org_id: string;
  [key: string]: any; // Allow any additional fields to be updated
}

export interface UpdateLocationResponse {
  success: boolean;
  message?: string;
  members_locations?: any;
}

export interface CreateContactParams {
  members_id: string;
  members_locations_id: string;
  context_org_id: string;
  first_name: string;
  last_name: string;
  email: string;
  sms_number?: string;
}

export interface CreateContactResponse {
  success: boolean;
  message?: string;
  members_users?: {
    results: MemberUser[];
    totalCount: number;
  };
}

export interface UpdateContactParams {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  sms_number?: string;
  context_org_id: string;
}

export interface UpdateContactResponse {
  success: boolean;
  message?: string;
  members_users?: any;
}

export interface DeleteContactParams {
  id: string;
}

export interface DeleteContactResponse {
  success: boolean;
  message?: string;
}

/**
 * Composable that provides access to Member API functions.
 */
export function useMemberAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async fetchMemberProducts(params: FetchMemberProductsParams): Promise<MemberProduct[]> {
      const data = await httpClient.get<MemberProductsResponse>('admin/v4/members_devices/', {
        'sAction': 'listingProductRollup',
        'limit': params.limit?.toString() || '5',
        'page': '1',
        'start': '0',
        'filter': JSON.stringify([
          { property: 'members_id', value: params.memberId },
          { property: 'context_org_id', value: params.orgId },
          { property: 'members_locations_id', value: params.locationId }
        ])
      });
      
      if (data.success && data.members_devices?.results) {
        return data.members_devices.results;
      }
      
      throw new Error(data.message || 'Failed to fetch member products');
    },

    async searchCustomerLocations(query: string): Promise<MemberLocation[]> {
      const data = await httpClient.get<CustomerLocationsResponse>('admin/v4/members/', {
        'sAction': 'listingLocations',
        'totalLocationCount': '1',
        'query': query,
        'fields': JSON.stringify([
          'site_name',
          'c__name',
          'street_1',
          'merchant_ids',
          'c__email',
          'c__contact_email',
          'phone'
        ]),
        'page': '1',
        'start': '0',
        'limit': '25'
      });
      
      if (data.success && data.members_locations?.results) {
        return data.members_locations.results;
      }
      
      throw new Error(data.message || 'Failed to search customer locations');
    },

    async fetchMemberUsers(params: FetchMemberUsersParams): Promise<MemberUser[]> {
      const data = await httpClient.get<MemberUsersResponse>('admin/v4/members/', {
        'sAction': 'listingUsers',
        'page': '1',
        'start': '0',
        'limit': '25',
        'sort': JSON.stringify([
          { property: 'last_name', direction: 'ASC' },
          { property: 'first_name', direction: 'ASC' }
        ]),
        'filter': JSON.stringify([
          { property: 'members_id', value: params.members_id },
          { property: 'members_locations_id', value: params.members_locations_id },
          { property: 'context_org_id', value: params.context_org_id }
        ])
      });
      
      if (data.success && data.members_users?.results) {
        return data.members_users.results;
      }
      
      throw new Error(data.message || 'Failed to fetch member users');
    },

    async updateMember(params: UpdateMemberParams): Promise<UpdateMemberResponse> {
      const payload = new URLSearchParams();
      payload.append('members', JSON.stringify([params]));
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<UpdateMemberResponse>(
        'admin/v4/members/',
        payload,
        { sAction: 'put' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.message || 'Failed to update member');
    },

    async updateLocation(params: UpdateLocationParams): Promise<UpdateLocationResponse> {
      const payload = new URLSearchParams();
      payload.append('members_locations', JSON.stringify([params]));
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<UpdateLocationResponse>(
        'admin/v4/members/',
        payload,
        { sAction: 'putLocation' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.message || 'Failed to update location');
    },

    async createContact(params: CreateContactParams): Promise<MemberUser> {
      // Clean phone number - remove all non-digits
      const cleanPhoneNumber = params.sms_number ? params.sms_number.replace(/\D/g, '') : ''
      
      // Create the member user object that matches the API format
      const memberUser = {
        members_id: params.members_id,
        members_locations_id: params.members_locations_id,
        id: 'boomtown.model.members.MembersUsersModel-6', // Temporary ID for new record
        context_org_id: '', // Set to empty string to match working example
        contact_id: '',
        first_name: params.first_name,
        last_name: params.last_name,
        email: params.email,
        sms_number: cleanPhoneNumber,
        refresh_minutes: '',
        status: 0,
        present: 0,
        m__alert_frequency: '',
        _merge: '',
        merge_ids: '',
        created: '',
        _source: '',
        has_info: false
      };

      const payload = new URLSearchParams();
      payload.append('members_users', JSON.stringify([memberUser]));
      payload.append('stringify', 'false'); // Add this flag like other methods
      
      console.log('🔧 Creating contact with payload:', {
        memberUser,
        formData: payload.toString()
      })
      
      const data = await httpClient.post<CreateContactResponse>(
        'admin/v4/members/',
        payload,
        { sAction: 'putUser' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success && data.members_users?.results?.[0]) {
        return data.members_users.results[0];
      }
      
      throw new Error(data.message || 'Failed to create contact');
    },

    async updateContact(params: UpdateContactParams): Promise<UpdateContactResponse> {
      // Clean phone number - remove all non-digits for sms_number field
      const cleanPhoneNumber = params.sms_number ? params.sms_number.replace(/\D/g, '') : undefined
      
      // Create the member user object for update
      const memberUser: any = {
        id: params.id,
        context_org_id: params.context_org_id
      }
      
      // Only include fields that are being updated
      if (params.first_name !== undefined) {
        memberUser.first_name = params.first_name
      }
      if (params.last_name !== undefined) {
        memberUser.last_name = params.last_name
      }
      if (params.email !== undefined) {
        memberUser.email = params.email
      }
      if (cleanPhoneNumber !== undefined) {
        memberUser.sms_number = cleanPhoneNumber
      }

      const payload = new URLSearchParams();
      payload.append('members_users', JSON.stringify([memberUser]));
      payload.append('stringify', 'false');
      
      console.log('🔧 Updating contact with payload:', {
        memberUser,
        formData: payload.toString()
      })
      
      const data = await httpClient.post<UpdateContactResponse>(
        'admin/v4/members/',
        payload,
        { sAction: 'putUser' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.message || 'Failed to update contact');
    },

    async deleteContact(params: DeleteContactParams): Promise<DeleteContactResponse> {
      // Validate that we have a contact ID
      if (!params.id || params.id.trim() === '') {
        throw new Error('Contact ID is required for deletion')
      }

      // Create the member user object for deletion - only need the ID
      const memberUser = {
        id: params.id
      }

      const payload = new URLSearchParams();
      // Use the same format as the working example - JSON string directly, not an array
      payload.append('members_users', JSON.stringify(memberUser));
      payload.append('stringify', 'false');
      
      console.log('🗑️ Deleting contact with payload:', {
        inputParams: params,
        memberUser,
        jsonString: JSON.stringify(memberUser),
        formData: payload.toString(),
        decodedPayload: decodeURIComponent(payload.toString())
      })
      
      const data = await httpClient.post<DeleteContactResponse>(
        'admin/v4/members/',
        payload,
        { sAction: 'deleteUser' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.message || 'Failed to delete contact');
    }
  };
} 