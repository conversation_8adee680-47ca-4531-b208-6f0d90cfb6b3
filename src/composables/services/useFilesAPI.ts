import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface UploadFileParams {
  object: string;
  object_id: string;
  file_tag: string;
  file: File;
}

export interface UploadFileResponse {
  success: boolean;
  message?: string;
  uploads_files_ids?: string[];
  file_keys?: string[];
  file_tags?: string[];
  file_names?: string[];
  file_names2?: string[];
  file_types?: string[];
  link?: string;
  links?: string[];
  url_avatar?: string | null;
  current_server_time?: string;
}

export interface FileItem {
  id: string;
  object: string;
  object_id: string;
  uploader_object: string;
  uploader_id: string;
  hash: string;
  type: string;
  file: string;
  name: string;
  tag: string;
  notes: string;
  created: string;
  scanner_status: number;
  size: number;
  metadata: any;
  c__d__name: string;
  c__d__created: string;
  c__d__type: string;
  c__d__fullname: string | null;
  _highlighted: boolean;
  _highlightmap: Record<string, any>;
  thumbnail: string;
}

export interface FetchFilesResponse {
  success: boolean;
  upload_files: {
    results: FileItem[];
    totalCount: number;
  };
  current_server_time: string;
}

export interface DeleteFileResponse {
  success: boolean;
  message?: string;
  current_server_time?: string;
}

/**
 * Composable that provides access to Files API functions.
 */
export function useFilesAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async uploadFile(params: UploadFileParams, onProgress?: (progress: number) => void): Promise<UploadFileResponse> {
      try {
        // Create FormData object
        const formData = new FormData();

        // Add file metadata
        formData.append('object', params.object);
        formData.append('object_id', params.object_id);
        formData.append('file_tag', params.file_tag);

        // Add the file
        formData.append('fileAttachment[]', params.file, params.file.name);

        // Use httpClient for file upload with optional progress tracking
        // The httpClient will automatically handle CSRF tokens and headers
        const data = await httpClient.post<UploadFileResponse>(
          'admin/v4/files/',
          formData,
          { sAction: 'putFile' },
          undefined, // headers
          onProgress // progress callback
        );

        if (data.success) {
          return data;
        }

        throw new Error(data.message || 'Failed to upload file');
      } catch (error) {
        console.error('FilesAPI: Error uploading file:', error);
        throw error;
      }
    },

    async fetchFiles(objectType: string, objectId: string): Promise<{ files: FileItem[]; totalCount: number }> {
      const data = await httpClient.get<FetchFilesResponse>('admin/v4/files/', {
        'sAction': 'listing',
        'includeChecklistsFormUploads': 'true',
        'page': '1',
        'start': '0',
        'limit': '25',
        'filter': JSON.stringify([
          { property: 'object', value: objectType },
          { property: 'object_id', value: objectId },
          { property: 'tag', value: null }
        ])
      });
      
      if (data.success && data.upload_files?.results) {
        return {
          files: data.upload_files.results,
          totalCount: data.upload_files.totalCount || 0
        };
      }
      
      return {
        files: [],
        totalCount: 0
      };
    },

    async deleteFile(fileId: string): Promise<DeleteFileResponse> {
      try {
        // Create FormData object (same pattern as uploadFile)
        const formData = new FormData();
        formData.append('includeChecklistsFormUploads', 'true');
        formData.append('upload_files', JSON.stringify([{ id: fileId }]));

        const data = await httpClient.post<DeleteFileResponse>(
          'admin/v4/files/',
          formData,
          { sAction: 'delete' },
          undefined // No explicit headers - let httpClient handle it like uploadFile
        );

        if (data.success) {
          return data;
        }

        throw new Error(data.message || 'Failed to delete file');
      } catch (error) {
        console.error('FilesAPI: Error deleting file:', error);
        throw error;
      }
    }
  };
} 