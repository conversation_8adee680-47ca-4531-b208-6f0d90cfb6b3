// Get the summarizer service URL from environment variable, fallback to local proxy path
const SUMMARIZER_SERVICE_URL = import.meta.env.VITE_SUMMARIZER_SERVICE_URL || '/service-summarizer-stage'

export async function fetchCustomerSummary(customerId: string) {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/customer?customerId=${encodeURIComponent(customerId)}`, {
    headers: {
      'accept': 'application/json',
    },
    method: 'GET',
    credentials: 'omit',
  });
  if (!response.ok) throw new Error('Failed to fetch customer summary');
  return await response.json();
}

export interface CaseSummary {
  summary: string;
  status: string;
  source: string;
  created_date: string;
  completed_date: string;
  sentiment?: {
    emotion: string;
    score: number;
    confidence: number;
    key_indicators?: string[];
  };
  case_data: {
    case_metadata: {
      id: string;
      sponsor_partners_teams_id: string;
      owner_partners_teams_id: string;
      source: string;
      collaborator_partners_teams_ids: string[];
      display_name: string;
      idr_isq: string;
      idr_process: any[];
      idr_resolution: any[];
      dicts: string[];
      canned_responses: any[];
      kbs: any[];
      categories: string[];
      type: number;
      escalated: boolean;
      claimed: string;
      created: string;
      customer: {
        id: string;
        name: string | null;
        email: string;
        phone: string | null;
        created: string;
      };
      location: {
        id: string;
        zipcode: string;
        phone: string | null;
        site_name: string;
        created: string;
      };
      customerUser: {
        id: string;
        first_name: string;
        last_name: string | null;
        email: string;
        sms_number: string;
        created: string;
      };
    };
    case_transcript: any[];
    case_id: string;
    org_id: string;
    source: string;
    summary: string | null;
  };
}

export async function fetchCaseSummary(caseId: string): Promise<CaseSummary[]> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/case?caseId=${encodeURIComponent(caseId)}`, {
    headers: {
      'accept': 'application/json',
    },
    method: 'GET',
    credentials: 'omit',
  });
  
  if (!response.ok) {
    if (response.status === 404) {
      throw new Error('CASE_SUMMARY_NOT_FOUND');
    }
    throw new Error('Failed to fetch case summary');
  }
  
  return await response.json();
}

export interface CreateCustomerSummaryParams {
  orgId: string;
  customerId: string;
  customerName: string;
  startDate: string;
  endDate: string;
}

export async function createCustomerSummary(params: CreateCustomerSummaryParams) {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/customer`, {
    headers: {
      'accept': '*/*',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify({
      orgId: params.orgId,
      customerId: params.customerId,
      customerName: params.customerName,
      startDate: params.startDate,
      endDate: params.endDate,
    })
  });
  if (!response.ok) throw new Error('Failed to create customer summary');
  return await response.json();
}

// OpenAI API Types and Functions
export interface Message {
  senderId: string;
  content: string;
}

export interface OpenAIResponse {
  result: string;
  model_used: string;
  tokens_used?: number;
}

export interface SummarizeConversationRequest {
  messages: Message[];
}

export interface GenerateReplyRequest {
  messages: Message[];
}

export interface ReformatEmailRequest {
  content: string;
}

export interface ReformatChatRequest {
  content: string;
}

export interface ReformatKnowledgeBaseRequest {
  content: string;
}

export interface CleanupEmailRequest {
  content: string;
}

export interface GeneralOpenAIRequest {
  model: string;
  messages: Array<{
    role: string;
    content: string;
  }>;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
}

// OpenAI API Functions
export async function summarizeConversation(request: SummarizeConversationRequest): Promise<OpenAIResponse> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/summarize-conversation`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to summarize conversation');
  }
  
  return await response.json();
}

export async function generateReply(request: GenerateReplyRequest): Promise<OpenAIResponse> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/generate-reply`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to generate reply');
  }
  
  return await response.json();
}

export async function reformatEmail(request: ReformatEmailRequest): Promise<OpenAIResponse> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/reformat-email`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to reformat email');
  }
  
  return await response.json();
}

export async function reformatChat(request: ReformatChatRequest): Promise<OpenAIResponse> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/reformat-chat`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to reformat chat response');
  }
  
  return await response.json();
}

export async function reformatKnowledgeBase(request: ReformatKnowledgeBaseRequest): Promise<OpenAIResponse> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/reformat-knowledge-base`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to reformat knowledge base article');
  }
  
  return await response.json();
}

export async function cleanupEmail(request: CleanupEmailRequest): Promise<OpenAIResponse> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/cleanup-email`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to cleanup email');
  }
  
  return await response.json();
}

export async function chatCompletion(request: GeneralOpenAIRequest): Promise<OpenAIResponse> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/chat-completion`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to complete chat request');
  }
  
  return await response.json();
}

export async function passthroughOpenAI(request: any): Promise<any> {
  const response = await fetch(`${SUMMARIZER_SERVICE_URL}/openai/passthrough`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(request),
  });
  
  if (!response.ok) {
    throw new Error('Failed to process OpenAI passthrough request');
  }
  
  return await response.json();
} 