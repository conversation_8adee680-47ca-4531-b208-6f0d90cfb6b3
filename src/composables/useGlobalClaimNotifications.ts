import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { usePubsubEventsStore } from '@/stores/pubsubEvents'
import { useIssuesAPI } from '@/composables/services/useIssuesAPI'

export function useGlobalClaimNotifications() {
  const router = useRouter()
  const toast = useToast()
  const pubsubEventsStore = usePubsubEventsStore()
  const { fetchIssue } = useIssuesAPI()

  // Track processed claim events to avoid duplicates
  const processedClaimEvents = ref(new Set<string>())
  
  // Track which cases we've already shown notifications for to avoid duplicates
  const notifiedCaseIds = ref(new Set<string>())

  // Handle global claim status notifications
  const handleGlobalClaimStatusEvent = async (event: any) => {
    // Show global notifications on all screens EXCEPT Open Chats views
    const currentPath = window.location.pathname
    const currentSearch = window.location.search
    const urlParams = new URLSearchParams(currentSearch)
    const currentView = urlParams.get('view')
    
    // Check if user is on Open Chats view (view=HQGXM5 or view=chat)
    const isOnOpenChats = currentPath === '/inbox' && (currentView === 'HQGXM5' || currentView === 'chat')
    
    if (isOnOpenChats) {
      return
    }
    
    // Check if we've already processed this exact event
    if (processedClaimEvents.value.has(event.id)) {
      return
    }
    
    // Mark this event as processed
    processedClaimEvents.value.add(event.id)
    
    if (event.eventData && event.eventData.tiles) {
      const claimableCases = event.eventData.tiles.filter((tile: any) => 
        tile.ownerUserId === null && tile.canClaim === true
      )
        
      // Process ALL claimable cases, but only show notifications for ones we haven't alerted about
      for (const tile of claimableCases) {
        // Check if we've already shown a notification for this case
        if (notifiedCaseIds.value.has(tile.issueId)) {
          continue
        }
        
        // Mark this case as notified immediately to prevent duplicates
        notifiedCaseIds.value.add(tile.issueId)
        
        try {
          // Fetch case details from API
          const caseData = await fetchIssue(tile.issueId)
          const referenceNum = caseData.reference_num || tile.issueId
          const displayName = `Ref ID #${referenceNum}`
          const locationName = caseData.location?.name
          
          // Build notification detail - only include location if it exists
          const notificationDetail = locationName 
            ? `${displayName} at ${locationName}|/inbox/cases/${tile.issueId}`
            : `${displayName}|/inbox/cases/${tile.issueId}`
          
          // Show global toast notification with clickable link
          // Use group to identify claim notifications for custom template
          toast.add({
            severity: 'info',
            summary: 'New Chat Ready to Claim',
            detail: notificationDetail, // Include URL in detail
            life: 10000, // 10 seconds
            closable: true,
            group: 'claim-notifications' // Special group for custom template
          })
          
        } catch (error) {
          console.error('GlobalClaimNotifications: Failed to fetch case data for notification:', error)
          
          // Show fallback notification without case details
          toast.add({
            severity: 'info',
            summary: 'New Chat Ready to Claim',
            detail: `Ref ID #${tile.issueId} is available to claim|/inbox/cases/${tile.issueId}`,
            life: 10000,
            closable: true,
            group: 'claim-notifications' // Special group for custom template
          })
        }
      }
    }
  }

  // Initialize watcher for pubsub events
  const initializeWatcher = () => {
    // Watch for claim status events for global notifications
    watch(() => pubsubEventsStore.events, (newEvents, oldEvents) => {
      if (newEvents.length > 0) {
        // Check for any new claim_status events that we haven't processed yet
        const claimStatusEvents = newEvents.filter(e => 
          e.eventType === 'claim_status' && !processedClaimEvents.value.has(e.id)
        )
        
        if (claimStatusEvents.length > 0) {
          const latestClaimEvent = claimStatusEvents[claimStatusEvents.length - 1]
          handleGlobalClaimStatusEvent(latestClaimEvent)
        }
      }
    }, { deep: true })
  }

  // Method to clear notification tracking (useful for testing)
  const clearNotificationTracking = () => {
    processedClaimEvents.value.clear()
    notifiedCaseIds.value.clear()
  }

  return {
    initializeWatcher,
    clearNotificationTracking
  }
} 