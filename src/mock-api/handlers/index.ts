// Import handlers using static imports for initial load
import { knowledgeHandlers } from './knowledge.handlers';
import { issuesHandlers } from './issues.handlers';
import { settingsHandlers } from './settings.handlers';
import { authHandlers } from './auth.handlers';
import { profileHandlers } from './profile.handlers';
import { filesHandlers } from './files.handlers';
import { commHandlers } from './comm.handlers';
import { analyticsHandlers } from './analytics.handlers';
import { memberHandlers } from './member.handlers';

// Set a constant delay for all mock API responses
export const RESPONSE_DELAY_IN_MS = 0;

// Export all handlers
export const handlers = [
  ...authHandlers,
  ...knowledgeHandlers,
  ...issuesHandlers,
  ...settingsHandlers,
  ...profileHandlers,
  ...filesHandlers,
  ...commHandlers,
  ...analyticsHandlers,
  ...memberHandlers,
];

// Export a function to get fresh handlers
export const getHandlers = async () => {
    try {
        // Use dynamic imports to ensure we get fresh versions each time
        const [
            knowledgeModule,
            issuesModule,
            settingsModule,
            authModule,
            profileModule,
            filesModule,
            commModule,
            analyticsModule,
            memberModule
        ] = await Promise.all([
            import('./knowledge.handlers'),
            import('./issues.handlers'),
            import('./settings.handlers'),
            import('./auth.handlers'),
            import('./profile.handlers'),
            import('./files.handlers'),
            import('./comm.handlers'),
            import('./analytics.handlers'),
            import('./member.handlers')
        ]);
        
        return [
            ...authModule.authHandlers,
            ...profileModule.profileHandlers,
            ...knowledgeModule.knowledgeHandlers,
            ...issuesModule.issuesHandlers,
            ...settingsModule.settingsHandlers,
            ...filesModule.filesHandlers,
            ...commModule.commHandlers,
            ...analyticsModule.analyticsHandlers,
            ...memberModule.memberHandlers
        ];
    } catch (error) {
        console.error('Error getting fresh handlers:', error);
        // Fall back to the original handlers if there's an error
        return handlers;
    }
};
