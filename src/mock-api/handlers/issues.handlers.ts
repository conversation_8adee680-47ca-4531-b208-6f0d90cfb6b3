import { http, HttpResponse, delay } from 'msw';
import { getIssuesMock } from '../data/fixtures';
import { RESPONSE_DELAY_IN_MS } from '.';
import { FIXTURE_CHANGE_EVENT } from '../fixtures-manager';

// Define the structure of the issues mock data
interface IssuesMockData {
    mockGetIssueResponse?: any;
    mockGetIssuesResponse?: any;
    [key: string]: any;
}


// Track if data has been loaded
let dataLoaded = false;

// Initialize handlers with empty mock data
let mockGetIssueResponse: any = {};
let mockGetIssuesResponse: any = {};

// Function to load mock data
async function loadMockData() {
    try {
        const mockData: IssuesMockData = await getIssuesMock();
        
        mockGetIssueResponse = mockData.mockGetIssueResponse || {};
        mockGetIssuesResponse = mockData.mockGetIssuesResponse || {};
    } catch (error) {
        console.error('❌ Error loading issues mock data:', error);
    }
}

// Event handler for fixture changes
const handleFixtureChange = () => {
    loadMockData();
};

// Ensure data is loaded - only loads once if already loaded
export async function ensureMockDataLoaded(): Promise<void> {
    if (!dataLoaded) {
        await loadMockData();
        dataLoaded = true;
    }
}

// Initialize data loading
ensureMockDataLoaded()
    .catch(err => console.error('❌ Failed to load issues mock data:', err));

// Set up event listener for fixture changes
if (typeof window !== 'undefined') {
    // Only add listener in browser context
    window.removeEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
    window.addEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
}

export const issuesHandlers = [
    // Mock getting issues
    http.get('*/admin/v4/issues', async ({params}) => {
        await delay(RESPONSE_DELAY_IN_MS);

        return HttpResponse.json(mockGetIssuesResponse);
    }),

    // Mock getting a specific issue
    http.get('*/admin/v4/issues/:id', async ({ params }) => {
        await delay(RESPONSE_DELAY_IN_MS);
        const { id } = params;

        return HttpResponse.json(mockGetIssuesResponse);
    }),

    // Mock getting interaction events
    http.get('*/admin/v4/interaction_events/', async ({ request }) => {
        await delay(RESPONSE_DELAY_IN_MS);
        
        const url = new URL(request.url);
        const filterParam = url.searchParams.get('filter');
        let issueId = 'default';
        
        // Parse filter to extract issue ID
        if (filterParam) {
            try {
                const filters = JSON.parse(filterParam);
                const issueFilter = filters.find((f: any) => f.property === 'source_object_id');
                if (issueFilter) {
                    issueId = issueFilter.value;
                }
            } catch (error) {
                console.warn('Failed to parse interaction events filter:', error);
            }
        }

        // Generate mock interaction events
        const mockEvents = [
            {
                log_id: `event-1-${issueId}`,
                category: { id: 'case-update', label: 'Case Update' },
                type: { id: 'case-created', label: 'Case Created' },
                body: { type: 'text', data: 'Case was created and assigned to support team' },
                created: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
                refs: {
                    user: {
                        users_full_name: 'John Smith',
                        users_nickname: '@johnsmith',
                        user_avatar: 'https://via.placeholder.com/40'
                    }
                }
            },
            {
                log_id: `event-2-${issueId}`,
                category: { id: 'task-action', label: 'Task Action' },
                type: { id: 'task-completed', label: 'Task Completed' },
                body: { type: 'text', data: 'Initial diagnostic task completed successfully' },
                created: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
                refs: {
                    user: {
                        users_full_name: 'Sarah Johnson',
                        users_nickname: '@sarahjohnson',
                        user_avatar: 'https://via.placeholder.com/40'
                    }
                }
            },
            {
                log_id: `event-3-${issueId}`,
                category: { id: 'note-added', label: 'Note Added' },
                type: { id: 'note-added', label: 'Note Added' },
                body: { type: 'text', data: 'Customer contacted via phone to discuss the issue details' },
                created: new Date(Date.now() - 10800000).toISOString(), // 3 hours ago
                refs: {
                    user: {
                        users_full_name: 'Mike Wilson',
                        users_nickname: '@mikewilson',
                        user_avatar: 'https://via.placeholder.com/40'
                    }
                }
            },
            {
                log_id: `event-4-${issueId}`,
                category: { id: 'status-update', label: 'Status Update' },
                type: { id: 'status-changed', label: 'Status Changed' },
                body: { type: 'text', data: 'Status changed from New to In Progress' },
                created: new Date(Date.now() - 14400000).toISOString(), // 4 hours ago
                refs: {
                    user: {
                        users_full_name: 'Emily Davis',
                        users_nickname: '@emilydavis',
                        user_avatar: 'https://via.placeholder.com/40'
                    }
                }
            }
        ];

        return HttpResponse.json({
            success: true,
            log_feed: {
                results: mockEvents,
                total: mockEvents.length
            }
        });
    }),

    // Mock creating a new issue
    http.post('*/admin/v4/issues', async ({ request }) => {
        const body = await request.json() as Record<string, any>;
        await delay(RESPONSE_DELAY_IN_MS);

        return HttpResponse.json({
            success: true,
            issue: {
                id: Math.floor(Math.random() * 1000).toString(),
                ...body,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
            },
        });
    }),

    // Mock getting issue with schema (new API endpoint)
    http.get('*/api/issues/', async ({ request }) => {
        await delay(RESPONSE_DELAY_IN_MS);
        
        const url = new URL(request.url)
        const sAction = url.searchParams.get('sAction')
        
        if (sAction === 'listing') {
            const filterParam = url.searchParams.get('filter')
            
            if (filterParam) {
                try {
                    const filters = JSON.parse(filterParam)
                    const idFilter = filters.find((f: any) => f.property === 'id')
                    
                    if (idFilter && mockGetIssuesResponse?.issues?.results) {
                        const issue = mockGetIssuesResponse.issues.results.find((i: any) => i.id === idFilter.value)
                        
                        if (issue) {
                            return HttpResponse.json({
                                success: true,
                                issues: {
                                    totalCount: 1,
                                    results: [issue]
                                },
                                case_meta: {
                                    schema: [
                                        {
                                            layout_id: "96f47064-16b3-428d-969c-79d561a286f6",
                                            creator_user_id: null,
                                            position: 0,
                                            type: 1,
                                            field: "sponsor_partners_id",
                                            data: {},
                                            created: "2025-03-26 10:54:42",
                                            updated: "2025-03-26 10:54:42",
                                            metadata: null,
                                            c__creator_user_id: null,
                                            c__condition: "null",
                                            _highlighted: false,
                                            _highlightmap: {},
                                            conditions: [],
                                            object: "issues",
                                            fieldType: "picklist",
                                            required: false,
                                            requiredResolve: false,
                                            readOnly: false,
                                            lbl: "Sponsor Org",
                                            options: []
                                        },
                                        {
                                            layout_id: "96f47064-16b3-428d-969c-79d561a286f6",
                                            creator_user_id: null,
                                            position: 1,
                                            type: 1,
                                            field: "owner_partners_id",
                                            data: {},
                                            created: "2025-03-26 10:54:42",
                                            updated: "2025-03-26 10:54:42",
                                            metadata: null,
                                            c__creator_user_id: null,
                                            c__condition: "null",
                                            _highlighted: false,
                                            _highlightmap: {},
                                            conditions: [],
                                            object: "issues",
                                            fieldType: "picklist",
                                            required: false,
                                            requiredResolve: false,
                                            readOnly: false,
                                            lbl: "Owner Organization",
                                            options: []
                                        },
                                        {
                                            layout_id: "96f47064-16b3-428d-969c-79d561a286f6",
                                            creator_user_id: null,
                                            position: 4,
                                            type: 1,
                                            field: "display_name",
                                            data: {},
                                            created: "2025-03-26 10:54:42",
                                            updated: "2025-03-26 10:54:42",
                                            metadata: null,
                                            c__creator_user_id: null,
                                            c__condition: "null",
                                            _highlighted: false,
                                            _highlightmap: {},
                                            conditions: [],
                                            object: "issues",
                                            fieldType: "text",
                                            required: false,
                                            requiredResolve: false,
                                            readOnly: false,
                                            lbl: "Case Name"
                                        },
                                        {
                                            layout_id: "96f47064-16b3-428d-969c-79d561a286f6",
                                            creator_user_id: null,
                                            position: 5,
                                            type: 1,
                                            field: "idr_isq",
                                            data: {},
                                            created: "2025-03-26 10:54:42",
                                            updated: "2025-03-26 10:54:42",
                                            metadata: null,
                                            c__creator_user_id: null,
                                            c__condition: "null",
                                            _highlighted: false,
                                            _highlightmap: {},
                                            conditions: [],
                                            object: "issues",
                                            fieldType: "textarea",
                                            required: false,
                                            requiredResolve: false,
                                            readOnly: false,
                                            lbl: "Case Description"
                                        },
                                        {
                                            layout_id: "96f47064-16b3-428d-969c-79d561a286f6",
                                            creator_user_id: null,
                                            position: 19,
                                            type: 1,
                                            field: "req_multi_select_test",
                                            data: {
                                                required: true,
                                                requiredResolve: false
                                            },
                                            created: "2025-03-26 10:55:29",
                                            updated: "2025-03-26 11:44:51",
                                            metadata: null,
                                            c__creator_user_id: null,
                                            c__condition: "null",
                                            _highlighted: false,
                                            _highlightmap: {},
                                            conditions: [],
                                            object: "issues",
                                            fieldType: "picklist_multi",
                                            required: true,
                                            requiredResolve: false,
                                            readOnly: false,
                                            lbl: "req_multi_select_test",
                                            options: [
                                                {
                                                    id: "1742992590648",
                                                    lbl: "test1",
                                                    val: "test1",
                                                    default: false
                                                },
                                                {
                                                    id: "1742992597694",
                                                    lbl: "test2",
                                                    val: "test2",
                                                    default: false
                                                }
                                            ]
                                        },
                                        {
                                            layout_id: "96f47064-16b3-428d-969c-79d561a286f6",
                                            creator_user_id: null,
                                            position: 20,
                                            type: 1,
                                            field: "currency_test",
                                            data: {},
                                            created: "2025-03-27 10:23:20",
                                            updated: "2025-03-27 10:23:20",
                                            metadata: null,
                                            c__creator_user_id: null,
                                            c__condition: "null",
                                            _highlighted: false,
                                            _highlightmap: {},
                                            conditions: [],
                                            object: "issues",
                                            fieldType: "currency",
                                            decimalPlaces: 2,
                                            required: false,
                                            requiredResolve: false,
                                            readOnly: false,
                                            lbl: "Currency Test"
                                        }
                                    ],
                                    alt_schema: {
                                        // Default issues schema
                                        "issues": [
                                            {
                                                layout_id: "default-issues",
                                                creator_user_id: null,
                                                position: 0,
                                                type: 1,
                                                field: "display_name",
                                                data: {},
                                                created: "2025-03-26 10:54:42",
                                                updated: "2025-03-26 10:54:42",
                                                metadata: null,
                                                c__creator_user_id: null,
                                                c__condition: "null",
                                                _highlighted: false,
                                                _highlightmap: {},
                                                conditions: [],
                                                object: "issues",
                                                fieldType: "text",
                                                required: true,
                                                requiredResolve: false,
                                                readOnly: false,
                                                lbl: "Case Name",
                                                options: []
                                            },
                                            {
                                                layout_id: "default-issues",
                                                creator_user_id: null,
                                                position: 1,
                                                type: 1,
                                                field: "custom_field_1",
                                                data: {},
                                                created: "2025-03-26 10:54:42",
                                                updated: "2025-03-26 10:54:42",
                                                metadata: null,
                                                c__creator_user_id: null,
                                                c__condition: "null",
                                                _highlighted: false,
                                                _highlightmap: {},
                                                conditions: [],
                                                object: "issues",
                                                fieldType: "text",
                                                required: false,
                                                requiredResolve: false,
                                                readOnly: false,
                                                lbl: "Custom Field 1",
                                                options: []
                                            }
                                        ],
                                        // Organization-specific schema (H3F)
                                        "H3F-issues": [
                                            {
                                                layout_id: "h3f-custom",
                                                creator_user_id: null,
                                                position: 0,
                                                type: 1,
                                                field: "display_name",
                                                data: {},
                                                created: "2025-03-26 10:54:42",
                                                updated: "2025-03-26 10:54:42",
                                                metadata: null,
                                                c__creator_user_id: null,
                                                c__condition: "null",
                                                _highlighted: false,
                                                _highlightmap: {},
                                                conditions: [],
                                                object: "issues",
                                                fieldType: "text",
                                                required: true,
                                                requiredResolve: false,
                                                readOnly: false,
                                                lbl: "H3F Case Title",
                                                options: []
                                            },
                                            {
                                                layout_id: "h3f-custom",
                                                creator_user_id: null,
                                                position: 1,
                                                type: 1,
                                                field: "h3f_custom_field",
                                                data: {},
                                                created: "2025-03-26 10:54:42",
                                                updated: "2025-03-26 10:54:42",
                                                metadata: null,
                                                c__creator_user_id: null,
                                                c__condition: "null",
                                                _highlighted: false,
                                                _highlightmap: {},
                                                conditions: [],
                                                object: "issues",
                                                fieldType: "picklist",
                                                required: false,
                                                requiredResolve: false,
                                                readOnly: false,
                                                lbl: "H3F Department",
                                                options: [
                                                    { lbl: "IT", val: "it", id: "1" },
                                                    { lbl: "HR", val: "hr", id: "2" },
                                                    { lbl: "Finance", val: "finance", id: "3" }
                                                ]
                                            },
                                            {
                                                layout_id: "h3f-custom",
                                                creator_user_id: null,
                                                position: 2,
                                                type: 2,
                                                field: "resolution_section",
                                                data: {},
                                                created: "2025-03-26 10:54:42",
                                                updated: "2025-03-26 10:54:42",
                                                metadata: null,
                                                c__creator_user_id: null,
                                                c__condition: "null",
                                                _highlighted: false,
                                                _highlightmap: {},
                                                conditions: [],
                                                object: "issues",
                                                fieldType: null,
                                                required: false,
                                                requiredResolve: false,
                                                readOnly: false,
                                                lbl: "Resolution",
                                                options: []
                                            },
                                            {
                                                layout_id: "h3f-custom",
                                                creator_user_id: null,
                                                position: 3,
                                                type: 1,
                                                field: "c__d_resolution",
                                                data: {},
                                                created: "2025-03-26 10:54:42",
                                                updated: "2025-03-26 10:54:42",
                                                metadata: null,
                                                c__creator_user_id: null,
                                                c__condition: "null",
                                                _highlighted: false,
                                                _highlightmap: {},
                                                conditions: [],
                                                object: "issues",
                                                fieldType: "text",
                                                required: false,
                                                requiredResolve: true,
                                                readOnly: false,
                                                lbl: "Resolution Type",
                                                options: []
                                            }
                                        ]
                                    },
                                    control: {}
                                },
                                current_server_time: new Date().toISOString()
                            })
                        }
                    }
                } catch (error) {
                    console.error('Error parsing filter:', error)
                }
            }
            
            return HttpResponse.json({
                success: false,
                message: 'Issue not found'
            }, { status: 404 })
        }
        
        // Handle listingCases action (used for fetchCases and fetchMemberCases)
        if (sAction === 'listingCases') {
            const filterParam = url.searchParams.get('filter')
            const totalOpenCountParam = url.searchParams.get('totalOpenCount')
            let results = mockGetIssuesResponse?.issues?.results || []
            let totalCount = results.length
            let totalOpenCount = 0
            
            // If filters are provided, try to filter the results
            if (filterParam) {
                try {
                    const filters = JSON.parse(filterParam)
                    // For member cases, we might have member-specific filters
                    const memberFilter = filters.find((f: any) => f.property === 'members_id')
                    const locationFilter = filters.find((f: any) => f.property === 'members_locations_id')
                    
                    if (memberFilter || locationFilter) {
                        // For member cases, return a subset of cases
                        results = results.slice(0, 3) // Return up to 3 cases for member
                        totalCount = 5 // Mock total count
                        totalOpenCount = 1 // Mock open count
                    }
                } catch (error) {
                    console.warn('Failed to parse listingCases filter:', error)
                }
            }
            
            // If totalOpenCount is requested, include it in response
            if (totalOpenCountParam === '1') {
                return HttpResponse.json({
                    success: true,
                    issues: {
                        totalCount,
                        totalOpenCount,
                        results
                    },
                    current_server_time: new Date().toISOString()
                })
            }
            
            return HttpResponse.json({
                success: true,
                issues: {
                    totalCount,
                    results
                },
                current_server_time: new Date().toISOString()
            })
        }
        
        return HttpResponse.json({
            success: false,
            message: 'Invalid action'
        }, { status: 400 })
    }),
];
