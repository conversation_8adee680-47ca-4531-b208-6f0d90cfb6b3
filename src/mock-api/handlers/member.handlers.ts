import { http, HttpResponse } from 'msw'

// Mock member user data
const mockMemberUsers = [
  {
    id: '7WHUGG-ABC',
    members_id: '7WHUGG',
    members_locations_id: '7WHUGG-LAD',
    contact_id: '',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    avatar: '',
    nickname: '@JohnSmithXyZ',
    email: '<EMAIL>',
    perm_ids: [6, 8, 7, 11, 90, 17, 24, 74],
    confirmation_token: '',
    sms_number: '555-123-4567',
    lang: 'EN',
    last_provider_chatted: '',
    present: 0,
    status: 0,
    active: true,
    adhoc: false,
    alert_config: '{}',
    login_count: 0,
    session_data: {},
    created: '2024-01-01 12:00:00',
    updated: '2024-01-01 12:00:00',
    last_login: '0000-00-00 00:00:00',
    external_id: null,
    role: 4,
    perms: ['issue_view', 'issue_edit', 'issue_add', 'cust_view'],
    permSet: {
      issue_view: 'issue_view',
      issue_edit: 'issue_edit',
      issue_add: 'issue_add',
      cust_view: 'cust_view'
    },
    is_customer_admin: null,
    location: null,
    full_name: '<PERSON>',
    public_name: '<PERSON> S',
    issues_open_cnt: 0,
    issues_total_cnt: 0,
    full_name_exact: '<PERSON>',
    c__presence: 'Offline',
    test: [],
    salesforce_user_id: '',
    custom_contact_field: '',
    c__d_lang: 'English',
    c__d_present: 'Offline',
    c__d_status: '',
    c__d_test: '',
    object: 'members',
    object_id: '7WHUGG',
    image: 'https://api.stage.goboomtown.com/avatar/members/7WHUGG/100',
    badges: null,
    _perms: [74, 90, 17, 11, 7, 8, 6, 24],
    _access: {},
    m__google_calendar_id: null,
    m__google_access_token: null,
    m__availability_periods: null,
    _is_bt: false,
    _is_service_provider: false,
    _is_service_ecosystem: false,
    m__stateful_grid_storage: [],
    globalValues: {
      contact_id: '',
      sms_number: '555-123-4567'
    },
    context_org_id: 'H3F',
    members_locations_ids: ['7WHUGG-LAD'],
    is_sms_number_invalid: false,
    alert_config_fields: {},
    _has_detail: true,
    _canWrite: true,
    _uiAccess: {
      edit: true,
      delete: true,
      clone: true,
      merge: true,
      contextOrgIds: ['H3F'],
      contextOrgId: 'H3F'
    }
  }
]

export const memberHandlers = [
  // List member users
  http.get('*/admin/v4/members/', ({ request }) => {
    const url = new URL(request.url)
    const sAction = url.searchParams.get('sAction')
    
    if (sAction === 'listingUsers') {
      return HttpResponse.json({
        success: true,
        members_users: {
          totalCount: mockMemberUsers.length,
          results: mockMemberUsers
        }
      })
    }
    
    // Handle other member-related actions
    return HttpResponse.json({ success: false, message: 'Unknown action' })
  }),

  // Create member user (contact)
  http.post('*/admin/v4/members/', async ({ request }) => {
    const url = new URL(request.url)
    const sAction = url.searchParams.get('sAction')
    
    if (sAction === 'putUser') {
      const formData = await request.formData()
      const membersUsersData = formData.get('members_users') as string
      
      if (membersUsersData) {
        const memberUsersArray = JSON.parse(membersUsersData)
        const newMemberUser = memberUsersArray[0]
        
        // Create a new member user with a generated ID
        const createdUser = {
          ...newMemberUser,
          id: `${newMemberUser.members_id}-${Math.random().toString(36).substr(2, 3).toUpperCase()}`,
          avatar: '',
          nickname: `@${newMemberUser.first_name}${newMemberUser.last_name}XyZ`,
          perm_ids: [6, 8, 7, 11, 90, 17, 24, 74],
          confirmation_token: '',
          lang: 'EN',
          last_provider_chatted: '',
          present: 0,
          active: true,
          adhoc: false,
          alert_config: '{}',
          login_count: 0,
          session_data: {},
          created: new Date().toISOString().replace('T', ' ').split('.')[0],
          updated: new Date().toISOString().replace('T', ' ').split('.')[0],
          last_login: '0000-00-00 00:00:00',
          external_id: null,
          role: 4,
          perms: ['issue_view', 'issue_edit', 'issue_add', 'cust_view'],
          permSet: {
            issue_view: 'issue_view',
            issue_edit: 'issue_edit',
            issue_add: 'issue_add',
            cust_view: 'cust_view'
          },
          is_customer_admin: null,
          location: null,
          full_name: `${newMemberUser.first_name} ${newMemberUser.last_name}`,
          public_name: `${newMemberUser.first_name} ${newMemberUser.last_name.charAt(0)}`,
          issues_open_cnt: 0,
          issues_total_cnt: 0,
          full_name_exact: `${newMemberUser.first_name} ${newMemberUser.last_name}`,
          c__presence: 'Offline',
          test: [],
          salesforce_user_id: '',
          custom_contact_field: '',
          c__d_lang: 'English',
          c__d_present: 'Offline',
          c__d_status: '',
          c__d_test: '',
          object: 'members',
          object_id: newMemberUser.members_id,
          image: `https://api.stage.goboomtown.com/avatar/members/${newMemberUser.members_id}/100`,
          badges: null,
          _perms: [74, 90, 17, 11, 7, 8, 6, 24],
          _access: {},
          m__google_calendar_id: null,
          m__google_access_token: null,
          m__availability_periods: null,
          _is_bt: false,
          _is_service_provider: false,
          _is_service_ecosystem: false,
          m__stateful_grid_storage: [],
          globalValues: {
            contact_id: newMemberUser.contact_id,
            sms_number: newMemberUser.sms_number
          },
          members_locations_ids: [newMemberUser.members_locations_id],
          is_sms_number_invalid: !newMemberUser.sms_number,
          alert_config_fields: {},
          _has_detail: true,
          _canWrite: true,
          _uiAccess: {
            edit: true,
            delete: true,
            clone: true,
            merge: true,
            contextOrgIds: [newMemberUser.context_org_id],
            contextOrgId: newMemberUser.context_org_id
          }
        }
        
        // Add to mock data
        mockMemberUsers.push(createdUser)
        
        return HttpResponse.json({
          success: true,
          members_users: {
            totalCount: 1,
            results: [createdUser]
          },
          current_server_time: new Date().toISOString()
        })
      }
    }
    
    if (sAction === 'deleteUser') {
      const formData = await request.formData()
      const membersUsersData = formData.get('members_users') as string
      
      if (membersUsersData) {
        const memberUserData = JSON.parse(membersUsersData)
        const contactId = memberUserData.id
        
        // Find and remove the contact from mock data
        const contactIndex = mockMemberUsers.findIndex(user => user.id === contactId)
        if (contactIndex !== -1) {
          mockMemberUsers.splice(contactIndex, 1)
          
          return HttpResponse.json({
            success: true,
            message: 'Contact deleted successfully',
            current_server_time: new Date().toISOString()
          })
        } else {
          return HttpResponse.json({ 
            success: false, 
            message: 'Contact not found' 
          })
        }
      }
    }
    
    return HttpResponse.json({ success: false, message: 'Invalid request' })
  })
] 