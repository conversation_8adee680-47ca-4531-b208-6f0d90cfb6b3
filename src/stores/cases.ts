import { useFilesAP<PERSON>, type FileItem, type UploadFileResponse } from '@/composables/services/useFilesAPI';
import type { CreateCaseParams, DeEscalateCaseParams, DeleteCaseParams, EscalateCaseParams, UpdateCaseParams } from '@/composables/services/useIssuesAPI';
import { useIssuesAPI } from '@/composables/services/useIssuesAPI';
import type { IssuePartnerTeamsResponse } from '@/composables/services/useMetaAPI';
import { useMetaAPI } from '@/composables/services/useMetaAPI';
import type { CreateCaseSummaryResponse } from '@/composables/services/useMLAPI';
import { useMLAPI } from '@/composables/services/useMLAPI';
import type { View } from '@/composables/services/useSettingsAPI';
import { useSettingsAPI } from '@/composables/services/useSettingsAPI';
import { fetchCaseSummary, type CaseSummary } from '@/composables/services/useSummarizerApi';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import type { EventPreset, EventPresetGroup, SchemaField } from '../composables/services/useIssuesAPI';
import type { FetchCasesParams, Issue } from '../services/IssuesAPI';
import type { CaseMeta } from '../composables/services/useIssuesAPI';

export const useCasesStore = defineStore('cases', () => {
  const currentIssue = ref<Issue | null>(null);
  const currentIssueSchema = ref<SchemaField[]>([]);
  const currentIssueCaseMeta = ref<CaseMeta | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const issues = ref<Issue[]>([]);
  const totalCount = ref(0);
  const views = ref<View[]>([]);
  const myWorkIssues = ref<Issue[]>([]);
  const myWorkReadyTotalCount = ref(0);
  const memberCases = ref<Issue[]>([]);
  const memberCasesTotalCount = ref(0);
  const memberCasesOpenCount = ref(0);
  const loadingMemberCases = ref(false);
  const memberCasesError = ref<string | null>(null);
  const issuePartnerTeams = ref<IssuePartnerTeamsResponse | null>(null);
  const loadingIssuePartnerTeams = ref(false);
  const issuePartnerTeamsError = ref<string | null>(null);
  const files = ref<FileItem[]>([]);
  const filesTotalCount = ref(0);
  const loadingFiles = ref(false);
  const filesError = ref<string | null>(null);
  const uploadingFile = ref(false);
  const uploadProgress = ref(0);
  const creatingSummary = ref(false);
  const summaryError = ref<string | null>(null);
  const caseSummaries = ref<CaseSummary[]>([]);
  const loadingCaseSummaries = ref(false);
  const caseSummariesError = ref<string | null>(null);
  
  // Track the last URL the user was on in the inbox section
  const lastInboxUrl = ref<string | null>(null);

  // Get issue ID from URL or use default
  const issueId = computed(() => {
    const params = new URLSearchParams(window.location.search);
    return params.get('issueId');
  });

  // Check if current case is in an open/active state (not resolved, closed, canceled, or deleted)
  const isCurrentCaseOpen = computed(() => {
    if (!currentIssue.value) return false
    const status = String(currentIssue.value.status)
    // Open statuses: New (1), Ready (2), Scheduling (3), Scheduled (4), En Route (5), In Progress (6), Waiting (79)
    return status === '1' || status === '2' || status === '3' || status === '4' || 
           status === '5' || status === '6' || status === '79'
  })

  // Check if current case is in a resolved/closed state
  const isCurrentCaseResolvedOrClosed = computed(() => {
    if (!currentIssue.value) return false
    const status = String(currentIssue.value.status)
    // Resolved/closed statuses: Resolved (7), Pending Close (9), Closed (10)
    return status === '7' || status === '9' || status === '10'
  })

  // Check if current case is canceled or deleted
  const isCurrentCaseCanceledOrDeleted = computed(() => {
    if (!currentIssue.value) return false
    const status = String(currentIssue.value.status)
    // Canceled (89) or Deleted (99)
    return status === '89' || status === '99'
  })

  // Check if current case is closed (closed status only)
  const isCurrentCaseClosed = computed(() => {
    if (!currentIssue.value) return false
    const status = String(currentIssue.value.status)
    // Closed status only (10) - convert to string for comparison since API returns integers
    return status === '10'
  })

  // Get current case status label
  const currentCaseStatusLabel = computed(() => {
    if (!currentIssue.value || !currentIssue.value.status) return null
    const status = String(currentIssue.value.status)
    
    const statusMap: Record<string, string> = {
      '1': 'New',
      '2': 'Ready',
      '3': 'Scheduling',
      '4': 'Scheduled',
      '5': 'En Route',
      '6': 'In Progress',
      '7': 'Resolved',
      '9': 'Pending Close',
      '10': 'Closed',
      '79': 'Waiting',
      '89': 'Canceled',
      '99': 'Deleted'
    }
    
    return statusMap[status] || `Unknown (${status})`
  })

  /**
   * Refresh interaction events for a specific case
   */
  async function refreshInteractionEvents(caseId: string) {
    try {
      // We'll emit a custom event that components can listen to
      // This is better than directly calling the API from the store
      const event = new CustomEvent('case-activity-refresh', { 
        detail: { caseId } 
      });
      window.dispatchEvent(event);
      
    } catch (error) {
      console.error('📝 Cases Store: Error refreshing interaction events:', error);
    }
  }

  /**
   * Shared function to update local state after a case update
   */
  function updateLocalCaseState(caseId: string, updatedIssue: Issue) {
    // Helper function to safely merge issues while preserving essential fields
    function safelyMergeIssue(existingIssue: Issue, newIssue: Issue): Issue {
      const merged = { ...newIssue };
      
      // Preserve essential fields if they become empty/null in the update
      // This is important because API responses for case updates often don't include
      // all the original issue data, especially communication-related properties
      if (!merged.id && existingIssue.id) {
        merged.id = existingIssue.id;
      }
      if (!merged.display_name && existingIssue.display_name) {
        merged.display_name = existingIssue.display_name;
      }
      
      // Preserve availableComm if it's missing from the update but exists in the existing issue
      if (!merged.availableComm && existingIssue.availableComm) {
        merged.availableComm = existingIssue.availableComm;
      }
      
      // Preserve source if it's missing from the update but exists in the existing issue
      if (!merged.source && existingIssue.source) {
        merged.source = existingIssue.source;
      }
      
      // Preserve status-related properties that are critical for UI display
      // Only preserve if the property is completely missing from the new issue
      const coreStatusProperties = ['status', 'resolution']; // These should be updated from API
      const displayStatusProperties = ['c__d_status', 'c__d_resolution', 'escalated', 'c__d_escalation_reasons']; // These might be missing from API responses
      const preservedStatusProps: string[] = [];
      
      // For display properties, preserve if missing from API response
      displayStatusProperties.forEach(prop => {
        if (!(prop in newIssue) && (existingIssue as any)[prop] !== undefined) {
          (merged as any)[prop] = (existingIssue as any)[prop];
          preservedStatusProps.push(prop);
        }
      });
      
      // For core status properties, only preserve if completely missing AND we don't have a new value
      coreStatusProperties.forEach(prop => {
        if (!(prop in newIssue) && (existingIssue as any)[prop] !== undefined) {
          (merged as any)[prop] = (existingIssue as any)[prop];
          preservedStatusProps.push(prop);
        }
      });
      

      
      // Log if we had to preserve fields
      if (merged.id !== newIssue.id || merged.display_name !== newIssue.display_name || (!newIssue.availableComm && existingIssue.availableComm) || (!newIssue.source && existingIssue.source)) {
        console.warn('📝 Cases Store: Preserved essential fields during case update:', {
          caseId: merged.id || existingIssue.id,
          preservedId: merged.id !== newIssue.id,
          preservedDisplayName: merged.display_name !== newIssue.display_name,
          preservedAvailableComm: !newIssue.availableComm && !!existingIssue.availableComm,
          preservedSource: !newIssue.source && !!existingIssue.source,
          originalId: newIssue.id,
          preservedIdValue: merged.id,
          originalDisplayName: newIssue.display_name,
          preservedDisplayNameValue: merged.display_name,
          availableCommCount: merged.availableComm?.length || 0,
          source: merged.source
        });
      }
      
      return merged;
    }
    
    // Update the current issue if it matches
    if (currentIssue.value && currentIssue.value.id === caseId) {
      currentIssue.value = safelyMergeIssue(currentIssue.value, updatedIssue);
    }
    
    // Update the issue in the issues array if it exists
    const issueIndex = issues.value.findIndex(issue => issue.id === caseId);
    if (issueIndex !== -1) {
      issues.value[issueIndex] = safelyMergeIssue(issues.value[issueIndex], updatedIssue);
    }
    
    // Update in myWorkIssues if it exists there
    const myWorkIndex = myWorkIssues.value.findIndex(issue => issue.id === caseId);
    if (myWorkIndex !== -1) {
      myWorkIssues.value[myWorkIndex] = safelyMergeIssue(myWorkIssues.value[myWorkIndex], updatedIssue);
    }
    
    // Update in memberCases if it exists there
    const memberCaseIndex = memberCases.value.findIndex(issue => issue.id === caseId);
    if (memberCaseIndex !== -1) {
      memberCases.value[memberCaseIndex] = safelyMergeIssue(memberCases.value[memberCaseIndex], updatedIssue);
    }
    
    // Trigger activity log refresh for this case
    refreshInteractionEvents(caseId);
  }

  async function fetchCurrentIssue(issueId: string) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const result = await issuesAPI.fetchIssueWithSchema(issueId);
      currentIssue.value = result.issue;
      currentIssueSchema.value = result.schema;
      currentIssueCaseMeta.value = result.caseMeta || null;
    } catch (err) {
      console.error('Cases Store: Error in fetchCurrentIssue:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchViews() {
    loading.value = true;
    error.value = null;

    try {
      const settingsAPI = useSettingsAPI();
      const fetchedViews = await settingsAPI.fetchViews();
      
      // Create the hard-coded "Open Chats" view
      const openChatsView = {
        "id": "chat",
        "team_ids": [],
        "label": "Open Chats",
        "type": 2,
        "status": 1,
        "object": "issues",
        "sort_object": "updated_desc",
        "sort_dir": "",
        "filters": [
        {
            "filter_field": "source",
            "filter_operator": "eq",
            "filter_field_lbl": "Case Source",
            "filter_operator_lbl": "is equal to",
            "filter_compare_field": "web_connect",
            "filter_compare_field_lbl": "Web Chat"
        },
        {
            "filter_field": "status",
            "filter_operator": "in",
            "filter_field_lbl": "Status",
            "filter_operator_lbl": "is in",
            "filter_compare_field": [
                "1",
                "ready"
            ],
            "filter_compare_field_lbl": [
                "New",
                "Ready"
            ]
        }
    ],
        "layout": "card"
      };
      
      // Inject the "Open Chats" view into the views array
      views.value = [...fetchedViews, openChatsView];
    } catch (err) {
      console.error('Cases Store: Error in fetchViews:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchCases(params: FetchCasesParams) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const result = await issuesAPI.fetchCases(params);
      issues.value = result.results;
      totalCount.value = result.totalCount;
    } catch (err) {
      console.error('Cases Store: Error in fetchCases:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  function clearCurrentIssue() {
    currentIssue.value = null;
  }

  async function fetchMyWorkIssues() {
    loading.value = true;
    error.value = null;
    try {
      const issuesAPI = useIssuesAPI();
      const data = await issuesAPI.fetchMyWorkIssues(true); // pass flag to get full response
      myWorkIssues.value = data.results;
      myWorkReadyTotalCount.value = data.totalCount;
    } catch (err) {
      console.error('Cases Store: Error in fetchMyWorkIssues:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function addNote(issueId: string, note: string) {
    try {
      const issuesAPI = useIssuesAPI();
      const result = await issuesAPI.addNote(issueId, note);
      
      return result;
    } catch (err) {
      console.error('Cases Store: Error in addNote:', err);
      throw err;
    }
  }

  async function fetchMemberCases(memberId: string, locationId: string, orgId: string) {
    loadingMemberCases.value = true;
    memberCasesError.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const result = await issuesAPI.fetchMemberCases(memberId, locationId, orgId);
      memberCases.value = result.cases;
      memberCasesTotalCount.value = result.totalCount;
      memberCasesOpenCount.value = result.totalOpenCount;
    } catch (err) {
      console.error('Cases Store: Error in fetchMemberCases:', err);
      memberCasesError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingMemberCases.value = false;
    }
  }

  async function updateCase(params: UpdateCaseParams) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const updatedIssue = await issuesAPI.updateCase(params);
      
      // Always refresh the full case data after any update to ensure we have complete data
      // This is important because API update responses often don't include all fields
      if (currentIssue.value?.id === params.id) {
        try {
          await fetchCurrentIssue(params.id);
        } catch (refreshError) {
          console.warn('📝 Cases Store: Failed to refresh case data after update, falling back to merge strategy:', refreshError);
          // Fall back to the merge strategy if refresh fails
          updateLocalCaseState(params.id, updatedIssue);
        }
      } else {
        // If it's not the current issue, just update the local state
        updateLocalCaseState(params.id, updatedIssue);
      }
      
      return updatedIssue;
    } catch (err) {
      console.error('Cases Store: Error in updateCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function createCase(params: CreateCaseParams) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const newCase = await issuesAPI.createCase(params);
      
      // Add the new case to our local cases array if we have one
      if (issues.value) {
        issues.value.unshift(newCase);
        // Update totalCount if we have it
        if (totalCount.value !== null) {
          totalCount.value += 1;
        }
      }
      
      return newCase;
    } catch (err) {
      console.error('Cases Store: Error in createCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function reopenCase(caseId: string) {
    const result = await updateCase({
      id: caseId,
      resolution: "0",
      isResolved: 0
    });
    
    // The updateCase function will handle the refresh automatically since this is a status change
    return result;
  }

  async function setToReady(caseId: string, readyNotes?: string) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const updatedIssue = await issuesAPI.updateCase({
        id: caseId,
        waiting_reason: "",
        waiting_notes: readyNotes || "",
        wait_until: "",
        waiting_return_to: null,
        wait_until_preset: null,
        snooze_until_preset_group: null
      } as any);
      
      // Add 1-second delay to allow interaction event to be created before updating local state
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateLocalCaseState(caseId, updatedIssue);
      
      // Refresh the full case data to ensure we have the latest computed display fields
      if (currentIssue.value?.id === caseId) {
        try {
          await fetchCurrentIssue(caseId);
        } catch (refreshError) {
          console.warn('📝 Cases Store: Failed to refresh case data after waiting status change:', refreshError);
          // Don't throw here - the update was successful, we just couldn't refresh
        }
      }
      
      return updatedIssue;
    } catch (err) {
      console.error('Cases Store: Error in setToReady:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function deleteCase(params: DeleteCaseParams) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const result = await issuesAPI.deleteCase(params);
      
      // Clear the current issue if it matches the deleted case
      if (currentIssue.value && currentIssue.value.id === params.id) {
        currentIssue.value = null;
      }
      
      // Remove the issue from the issues array if it exists
      const issueIndex = issues.value.findIndex(issue => issue.id === params.id);
      if (issueIndex !== -1) {
        issues.value.splice(issueIndex, 1);
        // Update total count
        if (totalCount.value > 0) {
          totalCount.value--;
        }
      }
      
      // Remove from myWorkIssues if it exists there
      const myWorkIndex = myWorkIssues.value.findIndex(issue => issue.id === params.id);
      if (myWorkIndex !== -1) {
        myWorkIssues.value.splice(myWorkIndex, 1);
        if (myWorkReadyTotalCount.value > 0) {
          myWorkReadyTotalCount.value--;
        }
      }
      
      // Remove from memberCases if it exists there
      const memberCaseIndex = memberCases.value.findIndex(issue => issue.id === params.id);
      if (memberCaseIndex !== -1) {
        memberCases.value.splice(memberCaseIndex, 1);
      }
      
      return result;
    } catch (err) {
      console.error('Cases Store: Error in deleteCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function escalateCase(params: EscalateCaseParams) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const result = await issuesAPI.escalateCase(params);
      
      // Add 1-second delay to allow interaction event to be created before updating local state
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the current issue if it matches
      if (result.issues) {
        updateLocalCaseState(params.id, result.issues);
      }
      
      return result;
    } catch (err) {
      console.error('Cases Store: Error in escalateCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function deEscalateCase(params: DeEscalateCaseParams) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const result = await issuesAPI.deEscalateCase(params);
      
      // Add 1-second delay to allow interaction event to be created before updating local state
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the current issue if it matches
      if (result.issues) {
        updateLocalCaseState(params.id, result.issues);
      }
      
      return result;
    } catch (err) {
      console.error('Cases Store: Error in deEscalateCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchIssuePartnerTeams(membersLocationId: string, issuesId: string) {
    loadingIssuePartnerTeams.value = true;
    issuePartnerTeamsError.value = null;

    try {
      const metaAPI = useMetaAPI();
      issuePartnerTeams.value = await metaAPI.fetchIssuePartnerTeams(membersLocationId, issuesId);
    } catch (err) {
      console.error('Cases Store: Error in fetchIssuePartnerTeams:', err);
      issuePartnerTeamsError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingIssuePartnerTeams.value = false;
    }
  }

  async function uploadFile(file: File, issueId: string, fileTag: string = 'attachment'): Promise<UploadFileResponse> {
    uploadingFile.value = true;
    uploadProgress.value = 0;
    filesError.value = null;

    try {
      const filesAPI = useFilesAPI();
      const result = await filesAPI.uploadFile(
        {
          object: 'issues',
          object_id: issueId,
          file_tag: fileTag,
          file: file
        },
        (progress) => {
          uploadProgress.value = progress;
        }
      );

      
      // Refresh the files list after successful upload
      await fetchFiles(issueId);
      
      return result;
    } catch (err) {
      console.error('Cases Store: Error in uploadFile:', err);
      filesError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      uploadingFile.value = false;
      uploadProgress.value = 0;
    }
  }

  async function fetchFiles(issueId: string) {
    loadingFiles.value = true;
    filesError.value = null;

    try {
      const filesAPI = useFilesAPI();
      const result = await filesAPI.fetchFiles('issues', issueId);
      files.value = result.files;
      filesTotalCount.value = result.totalCount;
    } catch (err) {
      console.error('Cases Store: Error in fetchFiles:', err);
      filesError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingFiles.value = false;
    }
  }

  async function createCaseSummary(caseId: string): Promise<CreateCaseSummaryResponse> {
    creatingSummary.value = true;
    summaryError.value = null;

    try {
      const mlAPI = useMLAPI();
      const result = await mlAPI.createCaseSummary({ case_id: caseId });
      
      return result;
    } catch (err) {
      console.error('Cases Store: Error in createCaseSummary:', err);
      summaryError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      creatingSummary.value = false;
    }
  }

  async function fetchCaseSummaries(caseId: string): Promise<CaseSummary[]> {
    loadingCaseSummaries.value = true;
    caseSummariesError.value = null;

    try {
      const maxRetries = 5;
      let attempt = 0;
      
      while (attempt < maxRetries) {
        attempt++;
        
        const result = await fetchCaseSummary(caseId);
        
        // Check if any summary is still pending
        const hasPendingSummary = result.some(summary => summary.status === 'PENDING');
        
        if (!hasPendingSummary || attempt >= maxRetries) {
          // Either all summaries are complete, or we've reached max retries
          caseSummaries.value = result;
          return result;
        }
        
        // Summary is pending, wait 10 seconds before retrying
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
      
      // This shouldn't be reached, but just in case
      caseSummaries.value = [];
      return [];
    } catch (err) {
      console.error('Cases Store: Error in fetchCaseSummaries:', err);
      
      // Handle 404 case specifically - don't set as error, just return empty array
      if (err instanceof Error && err.message === 'CASE_SUMMARY_NOT_FOUND') {
        caseSummaries.value = [];
        return [];
      }
      
      caseSummariesError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingCaseSummaries.value = false;
    }
  }

  async function assignJourneyToCase(caseId: string, journeyId: string) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const updatedIssue = await issuesAPI.updateCase({
        id: caseId,
        journey_id: journeyId
      });
      
      // Add delay to allow interaction event to be created before updating local state
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      updateLocalCaseState(caseId, updatedIssue);
      
      return updatedIssue;
    } catch (err) {
      console.error('Cases Store: Error in assignJourneyToCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function changeJourneyForCase(caseId: string, journeyId: string) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const updatedIssue = await issuesAPI.updateCase({
        id: caseId,
        journey_id: journeyId
      });
      
      // Add delay to allow interaction event to be created before updating local state
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      updateLocalCaseState(caseId, updatedIssue);
      
      return updatedIssue;
    } catch (err) {
      console.error('Cases Store: Error in changeJourneyForCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function removeJourneyFromCase(caseId: string) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const updatedIssue = await issuesAPI.updateCase({
        id: caseId,
        journey_id: null,
        journey_workflow_id: null
      });
      
      // Add delay to allow interaction event to be created before updating local state
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      updateLocalCaseState(caseId, updatedIssue);
      
      return updatedIssue;
    } catch (err) {
      console.error('Cases Store: Error in removeJourneyFromCase:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function changeCustomer(caseId: string, customer: { members_id: string; members_locations_id: string; c__name: string }) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      const updatedIssue = await issuesAPI.changeCustomer(caseId, customer);
      
      // Add delay to allow interaction event to be created before updating local state
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateLocalCaseState(caseId, updatedIssue);
      
      return updatedIssue;
    } catch (err) {
      console.error('Cases Store: Error in changeCustomer:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get return options for waiting modal based on issue ID
   */
  async function getReturnOptions(issueId: string): Promise<EventPreset[]> {
    try {
      const issuesAPI = useIssuesAPI();
      const response = await issuesAPI.fetchEventPresets({
        filter: [
          { property: 'id', value: issueId }
        ],
        page: 1,
        start: 0,
        limit: 100
      });
      
      // Filter presets that are relevant for return options (you may need to adjust this logic)
      return response.pl__issues_events_presets.filter(preset => 
        preset.reference === 'now' || preset.reference === 'scheduled'
      );
    } catch (error) {
      console.error('Error fetching return options:', error);
      throw error;
    }
  }

  /**
   * Get duration options for waiting modal
   */
  async function getDurationOptions(): Promise<{ groups: EventPresetGroup[], presets: EventPreset[] }> {
    try {
      const issuesAPI = useIssuesAPI();
      const response = await issuesAPI.fetchEventPresets({
        page: 1,
        start: 0,
        limit: 100
      });
      
      return {
        groups: response.pl__issues_events_preset_groups,
        presets: response.pl__issues_events_presets
      };
    } catch (error) {
      console.error('Error fetching duration options:', error);
      throw error;
    }
  }

  /**
   * Save the current URL as the last inbox URL
   */
  function saveLastInboxUrl(url: string) {
    console.log('📥 Cases Store: Saving last inbox URL:', url);
    lastInboxUrl.value = url;
  }

  /**
   * Get the last inbox URL and clear it
   */
  function getAndClearLastInboxUrl(): string | null {
    const url = lastInboxUrl.value;
    console.log('📥 Cases Store: Getting and clearing last inbox URL:', url);
    lastInboxUrl.value = null;
    return url;
  }

  /**
   * Get the last inbox URL without clearing it
   */
  function getLastInboxUrl(): string | null {
    return lastInboxUrl.value;
  }

  return {
    currentIssue,
    currentIssueSchema,
    currentIssueCaseMeta,
    issues,
    totalCount,
    loading,
    error,
    issueId,
    isCurrentCaseOpen,
    isCurrentCaseResolvedOrClosed,
    isCurrentCaseCanceledOrDeleted,
    isCurrentCaseClosed,
    currentCaseStatusLabel,
    views,
    fetchCurrentIssue,
    fetchViews,
    fetchCases,
    clearCurrentIssue,
    myWorkIssues,
    myWorkReadyTotalCount,
    fetchMyWorkIssues,
    addNote,
    memberCases,
    memberCasesTotalCount,
    memberCasesOpenCount,
    loadingMemberCases,
    memberCasesError,
    fetchMemberCases,
    updateCase,
    createCase,
    reopenCase,
    setToReady,
    deleteCase,
    escalateCase,
    deEscalateCase,
    issuePartnerTeams,
    loadingIssuePartnerTeams,
    issuePartnerTeamsError,
    fetchIssuePartnerTeams,
    files,
    filesTotalCount,
    loadingFiles,
    filesError,
    uploadingFile,
    uploadProgress,
    uploadFile,
    fetchFiles,
    creatingSummary,
    summaryError,
    createCaseSummary,
    caseSummaries,
    loadingCaseSummaries,
    caseSummariesError,
    fetchCaseSummaries,
    assignJourneyToCase,
    changeJourneyForCase,
    removeJourneyFromCase,
    getReturnOptions,
    getDurationOptions,
    changeCustomer,
    lastInboxUrl,
    saveLastInboxUrl,
    getAndClearLastInboxUrl,
    getLastInboxUrl,
  };
}); 