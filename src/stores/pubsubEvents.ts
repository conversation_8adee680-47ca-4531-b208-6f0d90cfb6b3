import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface PubsubEventData {
  id: string
  timestamp: Date
  eventType: string // The actual event type from the JSON payload (e.g., 'incoming_chat', 'claim_status', etc.)
  caseId?: string
  eventData?: any // The parsed event_data from the JSON payload for easy access
  node: string
  rawEvent: any // The original raw XMPP event for debugging
}

export const usePubsubEventsStore = defineStore('pubsubEvents', () => {
  // State
  const events = ref<PubsubEventData[]>([])
  const maxEvents = ref(100) // Keep last 100 events

  // Actions
  const addEvent = (eventData: PubsubEventData) => {
    // Add to beginning of array
    events.value.unshift(eventData)
    
    // Keep only the most recent events
    if (events.value.length > maxEvents.value) {
      events.value = events.value.slice(0, maxEvents.value)
    }
  }

  const clearEvents = () => {
    events.value = []
  }

  // Getters
  const getEventsByCaseId = (caseId: string) => {
    return events.value.filter(event => event.caseId === caseId)
  }

  const getLatestEventForCase = (caseId: string) => {
    return events.value.find(event => event.caseId === caseId)
  }

  return {
    // State
    events,
    maxEvents,

    // Actions
    addEvent,
    clearEvents,

    // Getters
    getEventsByCaseId,
    getLatestEventForCase
  }
}) 