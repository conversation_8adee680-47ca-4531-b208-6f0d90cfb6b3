import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { useRouter, type RouteLocationNormalized } from 'vue-router'

// Base page history item
export interface BasePageHistoryItem {
  id: string
  path: string
  query?: Record<string, string>
  timestamp: Date
  replaced: boolean
  pageType: PageType
}

// Page type enum
export enum PageType {
  CASE = 'case',
  TASK = 'task',
  KB_ARTICLE = 'kb_article',
  CUSTOMER = 'customer',
  LIST_VIEW = 'list_view',
  GENERAL = 'general'
}

// Specific page types
export interface CasePageItem extends BasePageHistoryItem {
  pageType: PageType.CASE
  caseData: {
    refId: string
    name: string
    status: string | null
    location?: string
  }
}

export interface TaskPageItem extends BasePageHistoryItem {
  pageType: PageType.TASK
  taskData: {
    name: string
    status: string
  }
}

export interface KBArticlePageItem extends BasePageHistoryItem {
  pageType: PageType.KB_ARTICLE
  articleData: {
    title: string
    status: string
    subtitle?: string
  }
}

export interface CustomerPageItem extends BasePageHistoryItem {
  pageType: PageType.CUSTOMER
  customerData: {
    name: string
    status: string
  }
}

export interface ListViewPageItem extends BasePageHistoryItem {
  pageType: PageType.LIST_VIEW
  listData: {
    filters?: Record<string, any>
    query?: string
    page?: number
    totalResults?: number
    viewType?: string // cases, tasks, etc.
  }
}

export interface GeneralPageItem extends BasePageHistoryItem {
  pageType: PageType.GENERAL
  generalData: {
    title?: string
    section?: string
  }
}

// Union type for all page history items
export type PageHistoryItem = 
  | CasePageItem 
  | TaskPageItem 
  | KBArticlePageItem 
  | CustomerPageItem 
  | ListViewPageItem 
  | GeneralPageItem

export const usePageHistoryStore = defineStore('pageHistory', () => {
  // State
  const pageHistory = ref<PageHistoryItem[]>([])
  const maxHistoryItems = ref(50)
  const isInitialized = ref(false)
  const lastAddedPath = ref<string>('')
  const lastAddedTime = ref<number>(0)

  // Actions
  function addPageHistoryItem(item: PageHistoryItem) {
    // Prevent rapid duplicate additions (within 1 second)
    const now = Date.now()
    if (item.path === lastAddedPath.value && (now - lastAddedTime.value) < 1000) {
      return
    }
    
    // Check if we already have this exact page (same path and query) in recent history
    const existingIndex = pageHistory.value.findIndex(
      existing => existing.path === item.path && 
                  JSON.stringify(existing.query) === JSON.stringify(item.query)
    )
    
    if (existingIndex !== -1) {
      // Remove the existing item so we can add the new one at the top
      pageHistory.value.splice(existingIndex, 1)
    }
    
    // Add new item to the beginning
    pageHistory.value.unshift(item)

    // Keep only the max number of items
    if (pageHistory.value.length > maxHistoryItems.value) {
      pageHistory.value = pageHistory.value.slice(0, maxHistoryItems.value)
    }

    // Update tracking variables
    lastAddedPath.value = item.path
    lastAddedTime.value = now

    // Save to localStorage
    saveToStorage()
  }

  function createCasePageItem(
    route: RouteLocationNormalized, 
    caseData: CasePageItem['caseData'],
    replaced = false
  ): CasePageItem {
    return {
      id: generateId(),
      path: route.path,
      query: route.query as Record<string, string>,
      timestamp: new Date(),
      replaced,
      pageType: PageType.CASE,
      caseData
    }
  }

  function createTaskPageItem(
    route: RouteLocationNormalized,
    taskData: TaskPageItem['taskData'],
    replaced = false
  ): TaskPageItem {
    return {
      id: generateId(),
      path: route.path,
      query: route.query as Record<string, string>,
      timestamp: new Date(),
      replaced,
      pageType: PageType.TASK,
      taskData
    }
  }

  function createKBArticlePageItem(
    route: RouteLocationNormalized,
    articleData: KBArticlePageItem['articleData'],
    replaced = false
  ): KBArticlePageItem {
    return {
      id: generateId(),
      path: route.path,
      query: route.query as Record<string, string>,
      timestamp: new Date(),
      replaced,
      pageType: PageType.KB_ARTICLE,
      articleData
    }
  }

  function createCustomerPageItem(
    route: RouteLocationNormalized,
    customerData: CustomerPageItem['customerData'],
    replaced = false
  ): CustomerPageItem {
    return {
      id: generateId(),
      path: route.path,
      query: route.query as Record<string, string>,
      timestamp: new Date(),
      replaced,
      pageType: PageType.CUSTOMER,
      customerData
    }
  }

  function createListViewPageItem(
    route: RouteLocationNormalized,
    listData: ListViewPageItem['listData'],
    replaced = false
  ): ListViewPageItem {
    return {
      id: generateId(),
      path: route.path,
      query: route.query as Record<string, string>,
      timestamp: new Date(),
      replaced,
      pageType: PageType.LIST_VIEW,
      listData
    }
  }

  function createGeneralPageItem(
    route: RouteLocationNormalized,
    generalData: GeneralPageItem['generalData'] = {},
    replaced = false
  ): GeneralPageItem {
    return {
      id: generateId(),
      path: route.path,
      query: route.query as Record<string, string>,
      timestamp: new Date(),
      replaced,
      pageType: PageType.GENERAL,
      generalData
    }
  }

  function clearHistory() {
    pageHistory.value = []
    localStorage.removeItem('pageHistory')
  }

  function getRecentPages(limit = 10): PageHistoryItem[] {
    return pageHistory.value.slice(0, limit)
  }

  function getPagesByType(pageType: PageType, limit = 10): PageHistoryItem[] {
    return pageHistory.value
      .filter(item => item.pageType === pageType)
      .slice(0, limit)
  }

  // Helper functions
  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  function saveToStorage() {
    try {
      localStorage.setItem('pageHistory', JSON.stringify(pageHistory.value))
    } catch (error) {
      console.warn('Failed to save page history to localStorage:', error)
    }
  }

  function loadFromStorage() {
    try {
      const stored = localStorage.getItem('pageHistory')
      if (stored) {
        const parsed = JSON.parse(stored)
        pageHistory.value = parsed.map((item: any) => {
          const processedItem = {
            ...item,
            timestamp: new Date(item.timestamp)
          }
          return processedItem
        })
      }
    } catch (error) {
      console.error('Failed to load page history from localStorage:', error)
      // Reset to empty array on error
      pageHistory.value = []
    }
  }

  // Initialize from storage
  function initialize() {
    if (!isInitialized.value) {
      loadFromStorage()
      isInitialized.value = true
    }
  }

  return {
    // State
    pageHistory,
    maxHistoryItems,
    isInitialized,

    // Actions
    addPageHistoryItem,
    createCasePageItem,
    createTaskPageItem,
    createKBArticlePageItem,
    createCustomerPageItem,
    createListViewPageItem,
    createGeneralPageItem,
    clearHistory,
    getRecentPages,
    getPagesByType,
    initialize,

    // Computed
    recentPages: () => getRecentPages(),
    totalPages: () => pageHistory.value.length
  }
}) 