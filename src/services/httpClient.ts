import { HttpBaseClient } from '@/services/httpBaseClient';
import type { AuthStoreInterface } from '@/stores/auth';
import { recordApiResponse } from '@/utils/apiRecorder';
import { v4 as uuidv4 } from 'uuid';

interface RequestOptions {
	params?: Record<string, string>;
	body?: any;
	headers?: Record<string, string>;
	onProgress?: (progress: number) => void;
}

class HttpClient extends HttpBaseClient {
	private clientInstanceId: string;
	private onAuthChange?: (isAuthenticated: boolean) => void;
	private initialized: boolean = false;
	private initializing: Promise<void> | null = null;
	private isPartnerDataInitialized: boolean = false;
	private authStore: AuthStoreInterface | null = null;

	constructor(baseUrl: string, clientInstanceId: string, onAuthChange?: (isAuthenticated: boolean) => void) {
		super(baseUrl);
		this.clientInstanceId = clientInstanceId;
		this.onAuthChange = onAuthChange;
	}

	// Method to set auth store reference
	public setAuthStore(authStore: AuthStoreInterface): void {
		this.authStore = authStore;
	}

	private getCommonHeaders(): Record<string, string> {
		const headers: Record<string, string> = {
			'x-boomtown-client-instance-id': this.clientInstanceId,
			'x-request-id': uuidv4(),
			'x-boomtown-app': 'cxme',
			'content-type': 'application/json',
		};

		// Get CSRF token from auth store if available
		if (this.authStore && this.authStore.csrfToken) {
			headers['x-boomtown-csrf-token'] = this.authStore.csrfToken;
		}

		return headers;
	}

	private hasRelayCookie(): boolean {
		return document.cookie.split(';').some((cookie) => cookie.trim().startsWith('relay='));
	}

	async initialize(): Promise<void> {
		// If already initialized or initializing, return existing promise
		if (this.initialized) return Promise.resolve();
		if (this.initializing) return this.initializing;

		this.initializing = new Promise<void>(async (resolve) => {
			try {
				// Just mark as initialized, auth should be handled by auth store
				this.initialized = true;
				resolve();
			} catch (error) {
				console.debug('Error during initialization:', error);
				this.initialized = true;
				resolve(); // Resolve anyway to allow the app to continue
			} finally {
				this.initializing = null;
			}
		});

		return this.initializing;
	}

	protected async request<T>(method: string, path: string, options: RequestOptions = {}): Promise<T> {
		// Build the query string if we have params
		let queryString = '';
		if (options.params) {
			const searchParams = new URLSearchParams();
			Object.entries(options.params).forEach(([key, value]) => {
				searchParams.append(key, value);
			});
			queryString = `?${searchParams.toString()}`;
		}

		let reqBody = options.body || undefined;
		const isFormData = false;

		const headers = { ...this.getCommonHeaders(), ...options.headers };

		if (reqBody) {
			// Check if body is FormData (for file uploads)
			if (reqBody instanceof FormData) {
				// Remove content-type header to let browser set it with boundary
				delete headers['content-type'];
			} else {
				// For non-FormData, check stringify flag
				let ignoreStringify = reqBody.stringify === false;

				if (reqBody instanceof URLSearchParams) {
					ignoreStringify = reqBody.get('stringify') === 'false';
				}

				delete reqBody.stringify;

				reqBody = ignoreStringify ? reqBody : JSON.stringify(reqBody);
			}
		}

		// Combine base URL (if relative path) or use absolute path
		const fullUrl = `${this.baseUrl}/${path.replace(/^\//, '')}${queryString}`;

		// Use XMLHttpRequest for progress tracking or FormData uploads
		if (options.onProgress || isFormData) {
			return this.requestWithProgress<T>(method, fullUrl, reqBody, options);
		}

		// Don't set content-type for FormData, let browser set it with boundary
		if (isFormData) {
			delete headers['content-type'];
		}

		const response = await fetch(fullUrl, {
			method,
			headers,
			body: reqBody,
			credentials: 'include',
			mode: 'cors',
		});

		if (!response.ok) {
			if (response.status === 401) {
				// Clear auth state and trigger redirect only on actual 401s from API calls
				console.debug('Received 401 from API, triggering logout');
				this.onAuthChange?.(false);
			}
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const responseData = await response.json();

		// Record the API response
		const functionName = new Error().stack?.split('\n')[2]?.match(/at\s+(\w+)/)?.[1] || 'unknown';
		recordApiResponse(functionName, fullUrl, method, responseData);

		return responseData;
	}

	private async requestWithProgress<T>(
		method: string,
		fullUrl: string,
		body: any,
		options: RequestOptions
	): Promise<T> {
		return new Promise((resolve, reject) => {
			const xhr = new XMLHttpRequest();

			// Set up progress tracking for uploads
			if (options.onProgress && xhr.upload) {
				xhr.upload.addEventListener('progress', (event) => {
					if (event.lengthComputable) {
						const percentComplete = Math.round((event.loaded / event.total) * 100);
						options.onProgress!(percentComplete);
					}
				});
			}

			xhr.addEventListener('load', () => {
				if (xhr.status >= 200 && xhr.status < 300) {
					try {
						const responseData = JSON.parse(xhr.responseText);

						// Record the API response
						const functionName = new Error().stack?.split('\n')[3]?.match(/at\s+(\w+)/)?.[1] || 'unknown';
						recordApiResponse(functionName, fullUrl, method, responseData);

						resolve(responseData);
					} catch (error) {
						reject(new Error('Failed to parse response JSON'));
					}
				} else {
					if (xhr.status === 401) {
						// Clear auth state and trigger redirect only on actual 401s from API calls
						console.debug('Received 401 from API, triggering logout');
						this.onAuthChange?.(false);
					}
					reject(new Error(`HTTP error! status: ${xhr.status}`));
				}
			});

			xhr.addEventListener('error', () => {
				reject(new Error('Network error occurred during request'));
			});

			xhr.addEventListener('abort', () => {
				reject(new Error('Request aborted'));
			});

			// Open the request
			xhr.open(method, fullUrl);

			// Set headers
			const headers = {
				...this.getCommonHeaders(),
				...options.headers,
			};

			// Don't set content-type for FormData, let browser set it with boundary
			if (body instanceof FormData) {
				delete headers['content-type'];
			}

			Object.entries(headers).forEach(([key, value]) => {
				xhr.setRequestHeader(key, value);
			});

			// Set credentials
			xhr.withCredentials = true;

			// Send the request
			xhr.send(body);
		});
	}

	getCookieDSToken(): string {
		// Look for the dstoken cookie in the document cookies
		const cookies = document.cookie.split(';');
		for (const cookie of cookies) {
			const [name, value] = cookie.trim().split('=');
			if (name === 'dstoken') {
				return value;
			}
		}
		return '';
	}

	get url(): string {
		return this.baseUrl;
	}

	// Getter for client instance ID
	get instanceId(): string {
		return this.clientInstanceId;
	}

	// Getter for CSRF token from auth store
	get csrf(): string {
		return this.authStore?.csrfToken || '';
	}

	// Add isInitialized getter
	get isInitialized(): boolean {
		return this.initialized;
	}

	// Add getter for authentication status from auth store
	get isAuthenticated(): boolean {
		return Boolean(this.authStore?.isAuthenticated);
	}

	// Override HTTP method helpers to support progress tracking
	async get<T>(path: string, params?: Record<string, string>, headers?: Record<string, string>): Promise<T> {
		return this.request<T>('GET', path, { params, headers });
	}

	async post<T>(
		path: string,
		body: any,
		params?: Record<string, string>,
		headers?: Record<string, string>,
		onProgress?: (progress: number) => void
	): Promise<T> {
		return this.request<T>('POST', path, { body, params, headers, onProgress });
	}

	async put<T>(
		path: string,
		body: any,
		params?: Record<string, string>,
		headers?: Record<string, string>,
		onProgress?: (progress: number) => void
	): Promise<T> {
		return this.request<T>('PUT', path, { body, params, headers, onProgress });
	}

	async patch<T>(
		path: string,
		body: any,
		params?: Record<string, string>,
		headers?: Record<string, string>,
		onProgress?: (progress: number) => void
	): Promise<T> {
		return this.request<T>('PATCH', path, { body, params, headers, onProgress });
	}

	async delete<T>(path: string, params?: Record<string, string>, headers?: Record<string, string>): Promise<T> {
		return this.request<T>('DELETE', path, { params, headers });
	}
}

// Export the class for creating instances
export { HttpClient };
