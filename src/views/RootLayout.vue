<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed, nextTick, type Ref } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useUserStore } from '../stores/user'
import { useCasesStore } from '../stores/cases'
import BravoMainNavigation from '@services/ui-component-library/components/BravoMainNavigation.vue'
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import { useI18n } from 'vue-i18n'
import LogoSvg from '../assets/logo.svg'
import ProfileMenu from '../components/ProfileMenu.vue'
import AnnouncekitWidget from '../components/AnnouncekitWidget.vue'
import CommunicationsPanelWrapper from '../components/CommunicationsPanelWrapper.vue'
import { usePermissions } from '@/composables/usePermissions'
import { useMessenger } from '@/composables/useMessenger'
import { useCrossTabIdle } from '@/composables/useCrossTabIdle'
import { useGlobalSearchStore } from '@/stores/globalSearch'
import GlobalSearch from '@/components/GlobalSearch.vue'
import XmppPubsubModal from '@/components/XmppPubsubModal.vue'
import { useXmppPubsub } from '@/composables/useXmppPubsub'
import { useGlobalClaimNotifications } from '@/composables/useGlobalClaimNotifications'
import BravoToast from '@services/ui-component-library/components/BravoToast.vue'

// Define navigation item interface
interface NavItem {
    id: string;
    icon?: string;
    label: string;
    active: boolean;
}

const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const globalSearchStore = useGlobalSearchStore();
const casesStore = useCasesStore();
const { t } = useI18n();
const profileMenu = ref();

// Initialize the permissions composable
const { can } = usePermissions()

// Initialize messenger integration
const { initializeMessenger } = useMessenger()
const loggingOut = ref(false)

const showLoggedOutPopup = ref(false)
const showContinuePopup = ref(false)
const continueCountdown = ref(60)
let continueTimer: ReturnType<typeof setInterval> | null = null
let lastActiveRef: Ref<number> | null = null

// Debug modal state
const showDebugModal = ref(false)
const DEBUG_ISSUE_ID = 'f71a5b63-2ad7-473a-b2e0-551cae70a48d'

// XMPP PubSub modal state
const showPubsubModal = ref(false)

// Initialize XMPP PubSub and global claim notifications
const { connect: connectPubsub, disconnect: disconnectPubsub } = useXmppPubsub()
const { initializeWatcher: initializeClaimNotifications } = useGlobalClaimNotifications()

// Show debug button only in development
const isDev = import.meta.env.DEV

const idleTimeout =  userStore.passwordPolicy?.idle_timeout * 1000; //1.2 * 60 * 1000;

const warningDuration = 1 * 60; // 1 minute
const { 
    lastActive, 
    isTimedOut, 
    lastActivityGap,
    showContinuePopup: crossTabShowContinuePopup,
    continueCountdown: crossTabContinueCountdown,
    showLoggedOutPopup: crossTabShowLoggedOutPopup,
    post,
    reloadPage
} = useCrossTabIdle({
    idleTimeout,
    backend: 'broadcastChannel',
});

// Sync local state with cross-tab state
watch(crossTabShowContinuePopup, (value) => {
    showContinuePopup.value = value;
    if (value) {
        localStorage.setItem('showContinuePopup', 'true');
    } else {
        localStorage.removeItem('showContinuePopup');
    }
});

watch(crossTabContinueCountdown, (value) => {
    continueCountdown.value = value;
    localStorage.setItem('continueCountdown', String(value));
});

watch(crossTabShowLoggedOutPopup, (value) => {
    console.log('Local showLoggedOutPopup updated:', value);
    showLoggedOutPopup.value = value;

    if (value) {
        localStorage.setItem('showLoggedOutPopup', 'true');
    } else {
        localStorage.removeItem('showLoggedOutPopup');
    }
});

watch(reloadPage, (val) => {
    if (val) {
        window.location.reload();
    }
});

lastActiveRef = lastActive;

watch(lastActivityGap, (gap) => {
    if (gap > 0 && gap <= warningDuration && !showContinuePopup.value && !showLoggedOutPopup.value) {
        console.log('Showing continue popup');
        showContinuePopup.value = true;
        continueCountdown.value = 60;
        localStorage.setItem('showContinuePopup', 'true');
        localStorage.setItem('continueCountdown', '60');
        post({ 
            showContinuePopup: true,
            continueCountdown: 60
        });

        if (continueTimer) {
            clearInterval(continueTimer)
        }
        continueTimer = setInterval(() => {
            continueCountdown.value--;
            localStorage.setItem('continueCountdown', String(continueCountdown.value));
            post({ 
                continueCountdown: continueCountdown.value
            });
            
            if (continueCountdown.value <= 0) {
                console.log('Continue countdown reached 0, showing logout popup');
                if (continueTimer) {
                    clearInterval(continueTimer);
                    continueTimer = null;
                }
                showContinuePopup.value = false;
                localStorage.removeItem('showContinuePopup');
                post({ showContinuePopup: false });
                showLoggedOutPopup.value = true;
                localStorage.setItem('showLoggedOutPopup', 'true');
                post({ showLoggedOutPopup: true });
            }
        }, 1000)
    }
})

watch(isTimedOut, (timedOut) => {
    console.log('isTimedOut changed:', timedOut);
    console.log('Current popup states:', { 
        showContinuePopup: showContinuePopup.value, 
        showLoggedOutPopup: showLoggedOutPopup.value 
    });
    
    if (timedOut) {
        console.log('Showing logged out popup');
        showContinuePopup.value = false;
        post({ showContinuePopup: false });

        if (continueTimer) {
            clearInterval(continueTimer)
        }

        continueCountdown.value = 60;
        showLoggedOutPopup.value = true;
        localStorage.setItem('showLoggedOutPopup', 'true');
        post({ showLoggedOutPopup: true });
    }
});

const handleLogout = async () => {
    if (continueTimer) {
        clearInterval(continueTimer);
        continueTimer = null;
    }
    localStorage.removeItem('showLoggedOutPopup')
    localStorage.removeItem('showContinuePopup')
    localStorage.removeItem('continueCountdown')
    post({ 
        showLoggedOutPopup: false,
        showContinuePopup: false
    });
    try {
        console.log('Logging out...');
        loggingOut.value = true;
        await authStore.logout();
        authStore.isAuthenticated = false;
        loggingOut.value = false;
        post({ reloadPage: true });
        await router.push('/login');
    } catch (error) {
        console.error('Logout failed:', error);
        authStore.isAuthenticated = false;
        await router.push('/login');
    }
};

const handleContinueSession = () => {
    showContinuePopup.value = false;
    localStorage.removeItem('showContinuePopup');
    post({ showContinuePopup: false });

    if (continueTimer) {
        clearInterval(continueTimer);
        continueTimer = null;
    }

    if (lastActiveRef) {
        lastActiveRef.value = Date.now();
        post({ lastActive: Date.now() });
    }
}



onMounted(() => {
    console.log('🔧 RootLayout: Component mounted, checking initial state:', {
        isAuthenticated: authStore.isAuthenticated,
        hasXmppData: !!userStore.xmppData,
        showLoggedOutPopup: localStorage.getItem('showLoggedOutPopup')
    })

    if (localStorage.getItem('showLoggedOutPopup') === 'true') {
        const stop = watch(
            () => authStore.isInitialized,
            async (initialized) => {
                if (initialized) {
                    // showLoggedOutPopup.value = true;
                    // post({ showLoggedOutPopup: true });
                    await handleLogout();
                    post({ reloadPage: true });
                    stop(); // Stop watching after first run
                    window.location.reload();
                }
            },
            { immediate: true }
        );
        return;
    }
    // Restore warning modal state if present
    if (localStorage.getItem('showContinuePopup') === 'true') {
        showContinuePopup.value = true;
        const storedCountdown = localStorage.getItem('continueCountdown');
        if (storedCountdown) {
            continueCountdown.value = Number(storedCountdown);
        } else {
            continueCountdown.value = 60;
        }
        post({ showContinuePopup: true, continueCountdown: continueCountdown.value });
    }

  // Initialize messenger integration once for the entire app
  initializeMessenger()
  
  // Add keyboard shortcuts
  const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl+Shift+X to open XMPP PubSub modal
    if (event.ctrlKey && event.shiftKey && event.key === 'X') {
      event.preventDefault()
      showPubsubModal.value = !showPubsubModal.value
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  // Initialize XMPP PubSub connection when user is authenticated and main XMPP is ready
  const initializePubsub = async () => {
    console.log('🔍 RootLayout: Checking pubsub initialization conditions:', {
      isAuthenticated: authStore.isAuthenticated,
      hasXmppData: !!userStore.xmppData,
      xmppDataKeys: userStore.xmppData ? Object.keys(userStore.xmppData) : []
    })
    
    if (authStore.isAuthenticated && userStore.xmppData) {
      try {
        console.log('🚀 RootLayout: Initializing XMPP PubSub connection')
        // Add a small delay to ensure main XMPP service has time to connect
        setTimeout(async () => {
          try {
            console.log('🚀 RootLayout: Attempting to connect PubSub after delay')
            await connectPubsub()
          } catch (error) {
            console.error('🚫 RootLayout: Failed to initialize XMPP PubSub:', error)
          }
        }, 3000) // 3 second delay to allow main XMPP service to connect first
      } catch (error) {
        console.error('🚫 RootLayout: Failed to initialize XMPP PubSub:', error)
      }
    } else {
      console.log('⚠️ RootLayout: Skipping pubsub initialization - conditions not met')
    }
  }
  
  // Initialize immediately if already authenticated
  if (authStore.isAuthenticated && userStore.xmppData) {
    console.log('🚀 RootLayout: User already authenticated, initializing PubSub')
    initializePubsub()
  }
  
  // Watch for authentication changes to initialize/cleanup pubsub
  watch(() => authStore.isAuthenticated, async (isAuth) => {
    console.log('🔍 RootLayout: Auth state changed:', isAuth, 'xmppData:', !!userStore.xmppData)
    if (isAuth && userStore.xmppData) {
      await initializePubsub()
    } else {
      await disconnectPubsub()
    }
  })
  
  // Also watch for xmppData to become available
  watch(() => userStore.xmppData, async (xmppData) => {
    console.log('🔍 RootLayout: XMPP data changed:', !!xmppData, 'isAuth:', authStore.isAuthenticated)
    if (authStore.isAuthenticated && xmppData) {
      await initializePubsub()
    }
  })
  
  // Initialize global claim notifications watcher
  initializeClaimNotifications()
  
  // Cleanup on unmount
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
    disconnectPubsub()
  })
})

// Use a special icon class name that you can style
const navigationItemsRaw = ref<any[]>([
  {
    id: 'ovation',
        icon: 'custom-ovation-logo', // This can be styled with CSS
    label: 'Ovation',
    active: false,
    isLogo: true,
  },
  {
    id: 'search',
    icon: 'pi pi-search',
    label: 'Search',
    active: false,
    tooltip: t('nav.search'),
    isAction: true, // This indicates it's an action, not a route
  },
  {
    id: 'inbox',
    icon: 'pi pi-inbox',
    label: 'Inbox',
    active: false,
    tooltip: t('nav.inbox'),
  },
  {
    id: 'journeys',
    icon: 'pi pi-map-marker',
    label: 'Journeys',
    active: false,
    tooltip: t('nav.journeys'),
  },
  {
    id: 'customers',
    icon: 'pi pi-users',
    label: 'Customers',
    active: false,
    tooltip: t('nav.customers'),
  },
  {
    id: 'knowledge',
    icon: 'pi pi-book',
    label: 'Knowledge',
    active: false,
    tooltip: t('nav.knowledge'),
  },
  {
    id: 'aistudio',
    icon: 'pi pi-sparkles',
    label: 'AI Studio',
    active: false,
    tooltip: t('nav.aistudio'),
  },
  {
    id: 'analytics',
    icon: 'pi pi-chart-bar',
    label: 'Analytics',
    active: false,
    tooltip: t('nav.analytics'),
  },
]);

const navigationItems = computed(() =>
  navigationItemsRaw.value.filter(item => {
    if (item.id === 'knowledge') {
      return can.viewArticle() || can.manageTemplates();
    }
    if (item.id === 'analytics') {
      return can.viewDashboards();
    }
    return true;
  })
);

const bottomItemsBase = ref<any[]>([
  {
    id: 'settings',
    icon: 'pi pi-cog',
    label: 'Settings',
    active: false,
    tooltip: t('nav.settings')
  },
  {
    id: 'profile',
    label: 'Profile',
    active: false,
    tooltip: t('nav.profile'),
  },
])

// Make bottomItems computed so avatar is reactive
const bottomItems = computed(() => 
  bottomItemsBase.value.map(item => 
    item.id === 'profile' 
      ? {
          ...item,
          avatar: {
            ...userStore.getUserAvatarProps(),
            backgroundColor: '#FFFFFF',
          }
        }
      : item
  )
)

// Watch for changes in user data and update the profile avatar
watch(() => userStore.userData, () => {
  const profileItem = bottomItems.value.find(item => item.id === 'profile');
  if (profileItem) {
    profileItem.avatar = {
      ...userStore.getUserAvatarProps(),
      backgroundColor: '#FFFFFF',
    };
  }
}, { deep: true })

// Create a computed property for navigation items that filters based on permissions
// FIXME refactor and delete me - see https://boomtown.atlassian.net/browse/CXM-81?focusedCommentId=57676
/**const filteredNavigationItems = computed(() => {
  return navigationItems.value.filter(item => {
    // Always show non-knowledge items
    if (item.id !== 'knowledge') return true;
    // Only show knowledge item if user has viewArticle permission
    return can.viewArticle();
  });
});**/

// Update active states based on current route
const updateActiveStates = () => {
    const currentPath = router.currentRoute.value.path;
    navigationItemsRaw.value = (navigationItemsRaw.value as any[]).map((item) => ({
        ...item,
        active: currentPath.startsWith(`/${item.id}`) && item.id !== 'ovation' && !item.isAction,
    }));
    bottomItemsBase.value = (bottomItemsBase.value as any[]).map((item) => ({
        ...item,
        active: currentPath.startsWith(`/${item.id}`),
    }));
};

// Watch for route changes
watch(() => router.currentRoute.value.path, updateActiveStates, { immediate: true });

// Watch for route changes to save last inbox URL when leaving
watch(() => router.currentRoute.value, (newRoute, oldRoute) => {
    if (oldRoute && oldRoute.path.startsWith('/inbox') && !newRoute.path.startsWith('/inbox')) {
        // User is leaving inbox, save the current URL
        const fullUrl = oldRoute.path + (oldRoute.query ? '?' + new URLSearchParams(oldRoute.query as Record<string, string>).toString() : '');
        console.log('👀 RootLayout Watcher: Leaving inbox via route change, saving URL:', fullUrl);
        casesStore.saveLastInboxUrl(fullUrl);
    }
}, { deep: true });

// Use any type for the handler param
const handleNavClick = (item: any) => {
    // Get the mouse event
    const event = window.event as MouseEvent;

    if (item.id === 'profile' && profileMenu.value) {
        // Show profile menu when profile avatar is clicked
        profileMenu.value.toggle(event);
    } else if (item.id === 'search') {
        // Open global search modal
        globalSearchStore.openModal();
        // Explicitly ensure search item never stays active
        // Use nextTick to ensure this runs after the navigation component's update
        nextTick(() => {
            const searchItem = navigationItemsRaw.value.find(navItem => navItem.id === 'search');
            if (searchItem) {
                searchItem.active = false;
            }
            // Also trigger the active state update to ensure current route stays highlighted
            updateActiveStates();
        });
    } else if (item.id === 'ovation' || item.isLogo) {
        // Logo item - trigger animation and navigate to inbox home
        // Find and animate the logo element
        const logoElement = document.querySelector('.ovation-logo');
        if (logoElement) {
            // Remove the class first to reset animation
            logoElement.classList.remove('spin-animation');
            // Force a reflow to restart animation
            void (logoElement as HTMLElement).offsetWidth;
            // Add the class back to trigger animation
            logoElement.classList.add('spin-animation');
        }

        // Also try to animate the icon if it's rendered differently
        const iconElement = document.querySelector('.custom-ovation-logo');
        if (iconElement) {
            iconElement.classList.remove('spin-animation');
            void (iconElement as HTMLElement).offsetWidth;
            iconElement.classList.add('spin-animation');
        }

        // Navigate to inbox home
        router.push('/inbox?view=home');
    } else {
        // Check if Ctrl key was pressed during the click
        if (event.ctrlKey) {
            // Open in a new tab
            window.open(`/${item.id}`, '_blank');
        } else {
            // Handle navigation with inbox last URL tracking
            const currentRoute = router.currentRoute.value;
            const currentPath = currentRoute.path;
            const isLeavingInbox = currentPath.startsWith('/inbox');
            const isGoingToInbox = item.id === 'inbox';

            // Save current URL if leaving inbox
            if (isLeavingInbox && !isGoingToInbox) {
                const fullUrl = currentPath + (currentRoute.query ? '?' + new URLSearchParams(currentRoute.query as Record<string, string>).toString() : '');
                console.log('🚪 RootLayout: Leaving inbox, saving URL:', fullUrl);
                casesStore.saveLastInboxUrl(fullUrl);
            }

            // If going to inbox, check for saved URL
            if (isGoingToInbox) {
                const lastUrl = casesStore.getAndClearLastInboxUrl();
                console.log('🏠 RootLayout: Going to inbox, last URL:', lastUrl);
                if (lastUrl) {
                    // Navigate to the saved URL
                    console.log('🏠 RootLayout: Navigating to saved URL:', lastUrl);
                    router.push(lastUrl);
                } else {
                    // Default to inbox home
                    console.log('🏠 RootLayout: No saved URL, going to inbox home');
                    router.push('/inbox?view=home');
                }
            } else {
                // Regular navigation within the app
                router.push(`/${item.id}`);
            }
        }
    }
};
</script>

<template>
    <div class="root-layout" v-if="!showLoggedOutPopup">
        <div class="content-wrapper">
            <BravoMainNavigation
                :items="navigationItems"
                :bottomItems="bottomItems"
                @update:items="(val: any[]) => { navigationItemsRaw = val }"
                @update:bottomItems="(val: any[]) => { bottomItemsBase = val }"
                @nav-click="handleNavClick"
            >
                <!-- Use the imported SVG -->
                <template #customIcon-ovation>
                    <img :src="LogoSvg" alt="Ovation Logo" class="ovation-logo" />
                </template>
            </BravoMainNavigation>

            <main class="main-content">
                <RouterView />
            </main>

            <!-- Use the new ProfileMenu component -->
            <ProfileMenu ref="profileMenu" :userStore="userStore" @logout="handleLogout" />

            <!-- Add AnnouncekitWidget component -->
            <AnnouncekitWidget />
        </div>

        <!-- Global Search Modal -->
        <GlobalSearch />

        <!-- Debug button for testing CommChannel -->
        <BravoButton
        v-if="false"
            class="debug-button"
            icon="pi pi-bug"
            severity="secondary"
            @click="showDebugModal = true"
            aria-label="Debug CommChannel"
            title="Test CommChannel Component"
        />

        <!-- Debug modal -->
        <BravoDialog
            v-model:visible="showDebugModal"
            modal
            :header="`Debug: CommunicationPanel - Issue: ${DEBUG_ISSUE_ID}`"
            :style="{ width: '85vw', height: '90vh' }"
            :maximizable="true"
            :closable="true"
            class="debug-modal-content"
        >

                <!-- Content when ready -->
                <div class="debug-comm-container relative">
                    <CommunicationsPanelWrapper 
                        :issue-id="DEBUG_ISSUE_ID" 
                        :show-participants="false"
                    />
                </div>
        </BravoDialog>
    </div>
    <BravoDialog v-if="showLoggedOutPopup"
            :visible="showLoggedOutPopup"
            modal 
            :header="`You've Been Logged Out`"
            :style="{ width: '400px' }"
            :closable="false"
            class="logged-out-dialog"
            :loading="loggingOut"
        >
        <div class="logged-out-content">
            <p class="confirmation-message">
                    {{ t('Your session has been expired due to inactivity.') }}
                </p>
            <p class="sub-message">
                {{ t('Please login again to continue.') }}
            </p>
        </div>
    
        <template #footer>
            <div class="dialog-footer">
                <BravoButton 
                class="p-button" 
                @click="handleLogout"
                severity="danger"
                :label="t('nav.logout')"
                />
            </div>
        </template>
    </BravoDialog>

    <BravoDialog
        v-if="showContinuePopup"
        :visible="showContinuePopup"
        modal
        :header="'Session Timeout Warning'"
        :closable="false"
        class="continue-session-dialog"
    >
        <div>
        <p>Warning. Your session is expiring due to inactivity.<br/><br/><strong>If you want to continue working, click the "Continue" button.</strong><br/><br/></p>
        <p>Your session will expire in <strong>{{ continueCountdown }} </strong> seconds.</p>
        </div>
        <template #footer>
        <BravoButton
            class="p-button"
            @click="handleLogout"
            :label="t('nav.logout')"
            severity="danger"
            :loading="loggingOut"
        />
        <BravoButton
            class="p-button"
            @click="handleContinueSession"
            :label="t('Continue')"
            severity="primary"
        />
        </template>
    </BravoDialog>

    <!-- XMPP PubSub Modal -->
    <XmppPubsubModal 
        v-model:visible="showPubsubModal"
    />

    <!-- Floating PubSub Debug Button -->
    <BravoButton
        @click="showPubsubModal = true"
        icon="pi pi-broadcast"
        class="pubsub-debug-button"
        severity="info"
        rounded
        :tooltip="'XMPP PubSub Monitor (Ctrl+Shift+X)'"
        tooltipOptions="{ position: 'left' }"
    />
</template>

<style scoped>
.root-layout {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevent scrolling on the root container */
}

.content-wrapper {
    flex: 1;
    display: flex;
    height: 100vh; /* Use full viewport height now that header is gone */
    min-height: 0; /* Important for nested flex containers */
}

.main-content {
    flex: 1;
    padding: 0;
    background: var(--surface-0);
    overflow-y: auto;
}

:deep(.bravo-main-navigation) {
    height: 100%;
    flex-shrink: 0; /* Prevent navigation from shrinking */
}

.ovation-logo {
    width: 24px;
    height: 24px;
    /* Center it within the navigation icon space */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Animation classes that will be applied to icons */
:deep(.custom-ovation-logo.spin-animation),
.ovation-logo.spin-animation {
    animation: bounce-scale 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes bounce-scale {
    0% {
        transform: scale(1);
    }
    30% {
        transform: scale(1.2);
    }
    60% {
        transform: scale(0.8);
    }
    80% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

:deep(.custom-ovation-logo) {
    background-image: url('../assets/logo.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 24px;
    height: 24px;
    /* Hide any text inside */
    font-size: 0;
    color: transparent;
}

/* Hide the AnnouncekitWidget div */
:deep(#announcekit-widget) {
    display: none !important;
}

/* Debug button styles */
.debug-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: var(--primary-color, #007bff) !important;
    border: none;
}

.debug-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* PubSub debug button styles */
.pubsub-debug-button {
    position: fixed;
    bottom: 80px;
    right: 20px;
    z-index: 9999;
    width: 50px;
    height: 50px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
}

.pubsub-debug-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}



/* Debug modal styles */
.debug-modal-content {
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
}

.debug-modal-content :deep(.p-dialog-content) {
    height: 100%;
    min-height: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
}

.debug-controls {
    padding: 1rem;
    border-bottom: 1px solid var(--surface-300);
    background: var(--surface-50);
}

.debug-comm-container {
    flex: 1;
    height: 100%;
    min-height: 0; /* Important for flex children */
    border: 1px solid var(--surface-300);
    border-radius: 8px;
    overflow: hidden;
    background: white;
    display: flex;
    flex-direction: column;
}

:deep(.p-dialog .p-dialog-header) {
    background: var(--surface-50);
    border-bottom: 1px solid var(--surface-200);
    padding: 1rem 1.5rem;
    flex-shrink: 0;
}
</style>
