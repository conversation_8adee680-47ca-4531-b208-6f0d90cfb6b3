import type { MemberUser } from '@/composables/services/useMemberAPI'

/**
 * Generate a display name for a contact following priority logic:
 * 1. If first or last name have information, show that
 * 2. else if the contact has an email address, show that
 * 3. else if the contact has a phone number, show that
 * 4. else if the contact is totally blank, show Unknown Contact
 */
export function getContactDisplayName(user: MemberUser | any): string {
  // 1. If first or last name have information, show that
  const firstName = user.first_name || ''
  const lastName = user.last_name || ''
  
  if (firstName || lastName) {
    return `${firstName} ${lastName}`.trim()
  }
  
  // 2. else if the contact has an email address, show that
  if (user.email) {
    return user.email
  }
  
  // 3. else if the contact has a phone number, show that
  if (user.sms_number) {
    return user.sms_number
  }
  
  // 4. else if the contact is totally blank, show Unknown Contact
  return 'Unknown Contact'
} 