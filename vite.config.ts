import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vueDevTools from 'vite-plugin-vue-devtools';
import packageJson from './package.json';

const branchName = process.env.BRANCH_NAME || ''
// Fix base path to be consistent - use relative paths for preview builds
const base = branchName ? `/preview_build/${branchName}/` : '/'

// https://vite.dev/config/
export default defineConfig({
  base,
  define: {
    'process.env': JSON.stringify({}),
    'development': JSON.stringify(process.env.NODE_ENV === 'development'),
    'import.meta.env.PACKAGE_VERSION': JSON.stringify(packageJson.version),
    'import.meta.env.DATADOG_APP_ID': JSON.stringify(process.env.DATADOG_APP_ID || ''),
    'import.meta.env.DATADOG_ENV': JSON.stringify(process.env.DATADOG_ENV || process.env.NODE_ENV || 'dev'),
    'import.meta.env.DATADOG_CLIENT_TOKEN': JSON.stringify(process.env.DATADOG_CLIENT_TOKEN || ''),
    'import.meta.env.VITE_TASKS_SERVICE_URL': JSON.stringify(process.env.VITE_TASKS_SERVICE_URL || '/service-tasks-stage'),
    'import.meta.env.VITE_SUMMARIZER_SERVICE_URL': JSON.stringify(process.env.VITE_SUMMARIZER_SERVICE_URL || '/service-summarizer-stage'),
    'import.meta.env.VITE_CONNECTORS_SERVICE_URL': JSON.stringify(process.env.VITE_CONNECTORS_SERVICE_URL || '/service-connectors-stage'),
    'import.meta.env.VITE_JOURNEYS_SERVICE_URL': JSON.stringify(process.env.VITE_JOURNEYS_SERVICE_URL || '/service-journeys-stage'),
    'import.meta.env.VITE_NOTIFICATIONS_SERVICE_URL': JSON.stringify(process.env.VITE_NOTIFICATIONS_SERVICE_URL || '/service-notifications-stage'),
    // Vue I18n feature flags to eliminate eval() usage
    '__INTLIFY_JIT_COMPILATION__': true,
    // '__INTLIFY_DROP_MESSAGE_COMPILER__': process.env.NODE_ENV === 'production',
  },
  plugins: [
    vue({
      include: [/\.vue$/, /node_modules\/@services\/.*\.vue$/]
    }),
    // Only enable devtools in development to reduce build overhead
    ...(process.env.CI ? [] : [vueDevTools()]),
    {
      name: 'preserve-init-js-path',
      transformIndexHtml(html) {
        // Ensure consistent path handling for init.js and messenger-init.js
        if (branchName) {
          // For preview builds, use relative paths
          return html
            .replace('/init.js', './init.js')
            .replace('/messenger-init.js', './messenger-init.js');
        } else {
          // For production builds, use absolute paths
          return html
            .replace('./init.js', '/init.js')
            .replace('./messenger-init.js', '/messenger-init.js');
        }
      },
    },
  ],
  build: {
    outDir: './dist',
    // Disable source maps in CI to save memory during chunk rendering
    sourcemap: process.env.CI ? false : (process.env.NODE_ENV === 'production' ? 'hidden' : true),
    // Memory optimizations for CI builds
    minify: 'esbuild', // esbuild is more memory-efficient than terser
    chunkSizeWarningLimit: process.env.CI ? 2000 : 1000,
    // Optimize for CI builds
    rollupOptions: {
      // Reduce memory usage during chunk rendering
      maxParallelFileOps: process.env.CI ? 1 : 5,
      output: {
        // Use consistent naming for assets
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        // Suppress chunk size warnings in CI to reduce memory usage
        ...(process.env.CI && {
          experimentalMinChunkSize: 1000,
        }),
        manualChunks: {
          // Split into smaller, more focused chunks
          'vue-vendor': ['vue', 'vue-router', 'vue-i18n'],
          'ui-vendor': ['primevue'],
          'draggable': ['vuedraggable'],
          'services': [
            'src/composables/services/useKnowledgeAPI.ts',
            'src/composables/services/useIssuesAPI.ts',
            'src/composables/services/useSettingsAPI.ts'
          ],
          'services-extended': [
            'src/composables/services/useMetaAPI.ts',
            'src/composables/services/useMLAPI.ts',
            'src/composables/services/useFilesAPI.ts',
            'src/composables/services/useInteractionEventsAPI.ts'
          ],
          'services-misc': [
            'src/composables/services/useUserAPI.ts',
            'src/composables/services/usePartnerAPI.ts',
            'src/composables/services/useCMSAPI.ts'
          ],
          'stores': [
            'src/stores/cases.ts',
            'src/stores/auth.ts',
            'src/stores/user.ts',
            'src/services/httpClientProvider.ts'
          ]
        }
      },
      external: ['axios'],
      // Additional memory optimizations for chunk rendering
      onwarn(warning, warn) {
        // Suppress chunk size warnings in CI to reduce memory usage
        if (warning.code === 'LARGE_CHUNK' && process.env.CI) return;
        warn(warning);
      },
    },
    // Reduce memory pressure during asset processing in CI
    assetsInlineLimit: process.env.CI ? 0 : 4096,
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/unit/setup.ts'],
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      // Use runtime-only build in production to avoid CSP eval issues
      ...(process.env.NODE_ENV === 'production' && {
        'vue-i18n': 'vue-i18n/dist/vue-i18n.runtime.esm-bundler.js'
      }),
      // Use source file for vuedraggable to avoid UMD eval issues
      'vuedraggable': 'vuedraggable/src/vuedraggable.js'
    },
  },
  server: {
    port: 5173,
    // Improve development server stability
    hmr: {
      overlay: true,
      // Add timeout to prevent connection issues
      timeout: 60000,
    },
    // Increase watch options for better file monitoring
    watch: {
      // Ignore node_modules to improve performance
      ignored: ['**/node_modules/**', '**/dist/**'],
      // Use polling if file watching is unreliable
      usePolling: false,
    },
    cors: {
      origin: [
        'https://appv5.stage-ovationcxm.com'
      ],
      credentials: true
    },
    proxy: {
      '/admin/v4': {
        target: 'https://api.stage.goboomtown.com',
        changeOrigin: true,
        secure: true,
        ws: true,
        cookieDomainRewrite: { '*': '' },
        cookiePathRewrite: { '*': '/' }
      },
      '^/journeys/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/tasksmywork/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/tasks-vite/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/connectors/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/generative-summarizer/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '/service-tasks-stage': {
        target: 'https://us-central1-stage-microservices-7845.cloudfunctions.net',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-tasks-stage/, '/service-tasks-stage')
      },
      '/service-connectors-stage': {
        target: 'https://s-connregistry-851510a1-5xeedb3yvq-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-connectors-stage/, '')
      },
      '/service-summarizer-stage': {
        target: 'https://s-summarizer-f497a979-c7ca7opsia-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-summarizer-stage/, '')
      },
      '/api': {
        target: 'https://app.stage.goboomtown.com/',
        changeOrigin: true,
        // rewrite: path => path.replace(/^\/admin-api/, ''),
        secure: false,
      },
      '/service-journeys-stage': {
        target: 'https://journeys-5xeedb3yvq-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-journeys-stage/, '')
      },
      '/service-notifications-stage': {
        target: 'https://notify-svc-5xeedb3yvq-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-notifications-stage/, '')
      },
    }
  }
})
