import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import AddContact from '../../../src/modules/inbox/components/AddContact.vue'

// Mock PrimeVue components
vi.mock('@services/ui-component-library/components/BravoDialog.vue', () => ({
  default: {
    name: 'BravoDialog',
    template: '<div data-testid="bravo-dialog"><slot></slot><slot name="footer"></slot></div>',
    props: ['visible', 'header', 'modal', 'style', 'closable', 'closeOnEscape', 'dismissableMask'],
    emits: ['update:visible']
  }
}))

vi.mock('@services/ui-component-library/components/BravoButton.vue', () => ({
  default: {
    name: 'BravoButton',
    template: '<button :disabled="disabled" :data-testid="dataTestid" @click="$emit(\'click\')">{{ label }}</button>',
    props: ['label', 'severity', 'loading', 'disabled', 'dataTestid'],
    emits: ['click']
  }
}))

vi.mock('@services/ui-component-library/components/BravoInputText.vue', () => ({
  default: {
    name: 'BravoInputText',
    template: '<input :value="modelValue" :placeholder="placeholder" :disabled="disabled" :data-testid="dataTestid" @input="$emit(\'update:modelValue\', $event.target.value)" />',
    props: ['modelValue', 'placeholder', 'disabled', 'invalid', 'dataTestid', 'type', 'id'],
    emits: ['update:modelValue']
  }
}))

vi.mock('@services/ui-component-library/components/BravoLabel.vue', () => ({
  default: {
    name: 'BravoLabel',
    template: '<label>{{ text }} <span v-if="isRequired">*</span></label>',
    props: ['text', 'isRequired']
  }
}))

vi.mock('@services/ui-component-library/components/BravoInputGroup.vue', () => ({
  default: {
    name: 'BravoInputGroup',
    template: '<div class="input-group"><slot></slot></div>',
    props: ['class']
  }
}))

vi.mock('@services/ui-component-library/components/BravoInputMask.vue', () => ({
  default: {
    name: 'BravoInputMask',
    template: '<input :value="modelValue" :placeholder="placeholder" :disabled="disabled" :data-testid="dataTestid" @input="$emit(\'update:modelValue\', $event.target.value)" />',
    props: ['modelValue', 'mask', 'placeholder', 'disabled', 'dataTestid', 'id'],
    emits: ['update:modelValue']
  }
}))

vi.mock('primevue/inputgroupaddon', () => ({
  default: {
    name: 'InputGroupAddon',
    template: '<div class="input-group-addon"><slot></slot></div>'
  }
}))

describe('AddContact', () => {
  let wrapper: any

  const defaultProps = {
    visible: true,
    onSubmit: vi.fn(),
    onCancel: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the form fields correctly', () => {
    wrapper = mount(AddContact, {
      props: defaultProps
    })

    expect(wrapper.find('[data-testid="first-name-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="last-name-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="email-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="phone-input"]').exists()).toBe(true)
  })

  it('validates required fields', async () => {
    wrapper = mount(AddContact, {
      props: defaultProps
    })

    const submitButton = wrapper.find('[data-testid="create-contact-btn"]')
    expect(submitButton.attributes('disabled')).toBeDefined()

    // Fill in required fields
    await wrapper.find('[data-testid="first-name-input"]').setValue('John')
    await wrapper.find('[data-testid="last-name-input"]').setValue('Doe')
    await wrapper.find('[data-testid="email-input"]').setValue('<EMAIL>')

    await nextTick()

    expect(submitButton.attributes('disabled')).toBeUndefined()
  })

  it('calls onSubmit with correct data when form is submitted', async () => {
    const mockOnSubmit = vi.fn().mockResolvedValue(undefined)
    wrapper = mount(AddContact, {
      props: {
        ...defaultProps,
        onSubmit: mockOnSubmit
      }
    })

    // Fill in form data
    await wrapper.find('[data-testid="first-name-input"]').setValue('John')
    await wrapper.find('[data-testid="last-name-input"]').setValue('Doe')
    await wrapper.find('[data-testid="email-input"]').setValue('<EMAIL>')
    await wrapper.find('[data-testid="phone-input"]').setValue('(*************')

    await nextTick()

    // Submit form
    await wrapper.find('[data-testid="create-contact-btn"]').trigger('click')

    expect(mockOnSubmit).toHaveBeenCalledWith({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '(*************'
    })
  })

  it('calls onCancel when cancel button is clicked', async () => {
    const mockOnCancel = vi.fn()
    wrapper = mount(AddContact, {
      props: {
        ...defaultProps,
        onCancel: mockOnCancel
      }
    })

    await wrapper.find('[data-testid="cancel-contact-btn"]').trigger('click')

    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('shows validation errors for empty required fields', async () => {
    wrapper = mount(AddContact, {
      props: defaultProps
    })

    // Try to submit empty form
    await wrapper.find('[data-testid="create-contact-btn"]').trigger('click')

    // Form should not submit with empty required fields
    expect(defaultProps.onSubmit).not.toHaveBeenCalled()
  })
}) 