import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import CustomerContactForm<PERSON>ield from '@/modules/inbox/components/CustomerContactFormField.vue'
import { createTestingPinia } from '@pinia/testing'
import PrimeVue from 'primevue/config'
import ToastService from 'primevue/toastservice'

// Mock the BravoFormField component with display slot support
vi.mock('@/modules/knowledge/components/BravoFormField.vue', () => ({
  default: {
    name: 'BravoFormField',
    template: `
      <div data-testid="bravo-form-field" ref="bravoFormFieldElement">
        <div v-if="$slots.display" class="display-slot">
          <slot name="display" :displayValue="displayValue" :value="value"></slot>
        </div>
        <div v-else class="default-display">{{ displayValue }}</div>
        <slot name="footer"></slot>
      </div>
    `,
    props: [
      'label', 'fieldName', 'value', 'displayValue', 'inputType', 'displayType',
      'options', 'optionLabel', 'optionValue', 'isLoading', 'isHorizontal',
      'isEditing', 'isSaving', 'noValueText', 'dataTestId'
    ],
    emits: ['update', 'save', 'cancel'],
    setup() {
      const handleSaveComplete = vi.fn()
      const startEditing = vi.fn()
      
      return {
        handleSaveComplete,
        startEditing
      }
    },
    expose: ['handleSaveComplete', 'startEditing']
  }
}))

// Mock PrimeVue Dialog component
vi.mock('primevue/dialog', () => ({
  default: {
    name: 'Dialog',
    props: {
      visible: Boolean,
      modal: Boolean,
      header: String,
      style: Object,
      class: [String, Object, Array]
    },
    template: `
      <div v-if="visible" data-testid="dialog">
        <div class="p-dialog-header">{{ header }}</div>
        <div class="p-dialog-content"><slot /></div>
        <div class="p-dialog-footer"><slot name="footer" /></div>
      </div>
    `,
    emits: ['update:visible']
  }
}))

// Mock PrimeVue Button component
vi.mock('primevue/button', () => ({
  default: {
    name: 'Button',
    props: {
      label: String,
      icon: String,
      severity: String,
      text: Boolean,
      size: String,
      class: [String, Object, Array]
    },
    template: `
      <button 
        :class="['p-button', class]" 
        :data-testid="$attrs['data-testid']"
        @click="$emit('click', $event)"
      >
        <i v-if="icon" :class="icon"></i>
        {{ label }}
      </button>
    `,
    emits: ['click']
  }
}))

// Mock BravoDialog component
vi.mock('@services/ui-component-library/components/BravoDialog.vue', () => ({
  default: {
    name: 'BravoDialog',
    props: {
      visible: Boolean,
      modal: Boolean,
      header: String,
      style: Object,
      class: [String, Object, Array]
    },
    template: `
      <div v-if="visible" data-testid="bravo-dialog">
        <div class="dialog-header">{{ header }}</div>
        <div class="dialog-content"><slot /></div>
        <div class="dialog-footer"><slot name="footer" /></div>
      </div>
    `,
    emits: ['update:visible']
  }
}))

// Mock the member store
const mockMemberStore = {
  memberUsers: [
    { id: '1', full_name: 'John Doe', first_name: 'John', last_name: 'Doe', email: '<EMAIL>', sms_number: '1234567890' },
    { id: '2', full_name: 'Jane Smith', first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>', sms_number: '0987654321' }
  ],
  loadingMemberUsers: false,
  memberUsersError: null,
  fetchMemberUsers: vi.fn().mockResolvedValue([
    { id: '1', full_name: 'John Doe', first_name: 'John', last_name: 'Doe', email: '<EMAIL>', sms_number: '1234567890' },
    { id: '2', full_name: 'Jane Smith', first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>', sms_number: '0987654321' }
  ]),
  clearMemberUsers: vi.fn()
}

vi.mock('@/stores/member', () => ({
  useMemberStore: () => mockMemberStore
}))

// Mock the member API
const mockMemberAPI = {
  createContact: vi.fn().mockResolvedValue({ id: 'new-contact-id' }),
  updateContact: vi.fn().mockResolvedValue({}),
  deleteContact: vi.fn().mockResolvedValue({})
}

vi.mock('@/composables/services/useMemberAPI', () => ({
  useMemberAPI: () => mockMemberAPI
}))

// Mock the contact helpers utility
vi.mock('@/utils/contactHelpers', () => ({
  getContactDisplayName: vi.fn((user) => {
    if (user.first_name || user.last_name) {
      return `${user.first_name || ''} ${user.last_name || ''}`.trim()
    }
    if (user.email) {
      return user.email
    }
    if (user.sms_number) {
      return user.sms_number
    }
    return 'Unknown Contact'
  })
}))

describe('CustomerContactFormField', () => {
  let wrapper: any

  const defaultProps = {
    label: 'Customer Contact',
    fieldName: 'members_users_id',
    value: '1',
    displayValue: 'John Doe',
    issue: {
      id: 'test-issue',
      members_id: 'member1',
      members_locations_id: 'location1',
      sponsor_partners_id: 'partner1',
      owner_partners_id: 'partner1',
      member: {
        id: 'member1',
        context_org_id: 'partner1'
      },
      location: {
        id: 'location1'
      }
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock store state
    mockMemberStore.memberUsers = [
      { id: '1', full_name: 'John Doe', first_name: 'John', last_name: 'Doe', email: '<EMAIL>', sms_number: '1234567890' },
      { id: '2', full_name: 'Jane Smith', first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>', sms_number: '0987654321' }
    ]
    mockMemberStore.loadingMemberUsers = false
    mockMemberStore.memberUsersError = null
  })

  const createWrapper = (props = {}) => {
    return mount(CustomerContactFormField, {
      props: { ...defaultProps, ...props },
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false
          }),
          PrimeVue,
          ToastService
        ],
        directives: {
          tooltip: {
            beforeMount() {},
            updated() {},
            unmounted() {}
          }
        },
        stubs: {
          BravoDialog: true,
          AddContact: true,
          Dialog: true,
          Button: true
        }
      }
    })
  }

  it('renders correctly', () => {
    wrapper = createWrapper()
    expect(wrapper.find('[data-testid="bravo-form-field"]').exists()).toBe(true)
  })

  it('has footer slot with Add Contact functionality', () => {
    wrapper = createWrapper()
    // Since the BravoFormField mock renders the footer slot, we can check for its presence
    const bravoFormField = wrapper.find('[data-testid="bravo-form-field"]')
    expect(bravoFormField.exists()).toBe(true)
    // The actual button rendering depends on the dropdown being open, which is complex to test
    // in a unit test environment. We'll verify the component structure instead.
  })

  it('handles add contact functionality', async () => {
    wrapper = createWrapper()
    
    // Test the handleAddContact method directly since the button is in a complex dropdown
    await wrapper.vm.handleAddContact()
    
    expect(wrapper.emitted('add-contact')).toBeTruthy()
    expect(wrapper.emitted('add-contact')).toHaveLength(1)
  })

  it('fetches members users on mount when issue is provided', async () => {
    wrapper = createWrapper()
    
    // Wait for the component to mount and fetch data
    await wrapper.vm.$nextTick()
    
    expect(mockMemberStore.fetchMemberUsers).toHaveBeenCalledWith({
      members_id: 'member1',
      members_locations_id: 'location1',
      context_org_id: 'partner1'
    })
  })

  it('computes display value correctly when user is found', async () => {
    wrapper = createWrapper({ value: '1' })
    
    // Wait for data to be fetched
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(wrapper.vm.computedDisplayValue).toBe('John Doe')
  })

  it('shows empty state when user is not found', async () => {
    wrapper = createWrapper({ value: 'unknown-user' })
    
    // Wait for data to be fetched
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(wrapper.vm.computedDisplayValue).toBe('—')
  })

  it('shows loading state when fetching data', () => {
    // Mock loading state
    mockMemberStore.loadingMemberUsers = true
    wrapper = createWrapper()
    
    expect(wrapper.vm.computedDisplayValue).toBe('Loading...')
    
    // Reset loading state
    mockMemberStore.loadingMemberUsers = false
  })

  it('forwards update event correctly', () => {
    wrapper = createWrapper()
    
    wrapper.vm.handleFieldUpdate('members_users_id', 'user2')
    
    expect(wrapper.emitted('update')).toBeTruthy()
    expect(wrapper.emitted('update')[0]).toEqual(['members_users_id', 'user2'])
  })

  it('forwards save event correctly', () => {
    wrapper = createWrapper()
    
    wrapper.vm.handleFieldSave('members_users_id', 'user2')
    
    expect(wrapper.emitted('save')).toBeTruthy()
    expect(wrapper.emitted('save')[0]).toEqual(['members_users_id', 'user2'])
  })

  it('forwards cancel event correctly', () => {
    wrapper = createWrapper()
    
    wrapper.vm.handleCancel()
    
    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('exposes handleSaveComplete method', () => {
    wrapper = createWrapper()
    
    expect(typeof wrapper.vm.handleSaveComplete).toBe('function')
  })

  it('exposes startEditing method', () => {
    wrapper = createWrapper()
    
    expect(typeof wrapper.vm.startEditing).toBe('function')
  })

  // New tests for contact click and edit functionality
  describe('Contact Click and Edit Functionality', () => {
    it('calls startEditing when edit button is clicked', async () => {
      wrapper = createWrapper({ 
        value: '1',
        isEditing: true 
      })
      
      // Wait for component to mount and data to load
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))
      
      // Find the edit button in the display slot
      const editButton = wrapper.find('[data-testid="members_users_id-edit-contact-button"]')
      expect(editButton.exists()).toBe(true)
      
      // Check that edit button is visible and clickable
      expect(editButton.isVisible()).toBe(true)
      expect(editButton.attributes('disabled')).toBeUndefined()
      
      // Click the edit button and verify the BravoFormField startEditing would be called
      await editButton.trigger('click')
      
      // Since we can't easily test the internal method call to BravoFormField,
      // we'll just verify the button exists and is clickable
      expect(editButton.exists()).toBe(true)
    })

    it('shows contact link when contact is selected and editable', async () => {
      wrapper = createWrapper({ 
        value: '1',
        isEditing: true 
      })
      
      // Wait for component to mount and data to load
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))
      
      // Check that contact display container exists
      const contactContainer = wrapper.find('.contact-display-container')
      expect(contactContainer.exists()).toBe(true)
      
      // Check that contact link exists and has correct text
      const contactLink = wrapper.find('.contact-link')
      expect(contactLink.exists()).toBe(true)
      expect(contactLink.text()).toBe('John Doe')
      
      // Check that edit button exists
      const editButton = wrapper.find('.edit-contact-button')
      expect(editButton.exists()).toBe(true)
    })

    it('shows empty state when no contact is selected', async () => {
      wrapper = createWrapper({ 
        value: null,
        isEditing: true 
      })
      
      // Wait for component to mount
      await wrapper.vm.$nextTick()
      
      // Check that empty value span is shown instead of contact container
      const emptyValue = wrapper.find('.empty-value')
      expect(emptyValue.exists()).toBe(true)
      
      // Check that contact container is not shown
      const contactContainer = wrapper.find('.contact-display-container')
      expect(contactContainer.exists()).toBe(false)
    })

    it('does not show edit button when field is not editable', async () => {
      wrapper = createWrapper({ 
        value: '1',
        isEditing: false 
      })
      
      // Wait for component to mount and data to load
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))
      
      // Edit button should not exist when isEditing is false
      const editButton = wrapper.find('.edit-contact-button')
      expect(editButton.exists()).toBe(false)
    })

    it('emits contact-click event when contact link is clicked', async () => {
      wrapper = createWrapper({ 
        value: '1',
        isEditing: true 
      })
      
      // Wait for component to mount and data to load
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))
      
      // Find the contact link
      const contactLink = wrapper.find('.contact-link')
      expect(contactLink.exists()).toBe(true)
      
      // Click the contact link
      await contactLink.trigger('click')
      
      // Check that contact-click event was emitted
      expect(wrapper.emitted('contact-click')).toBeTruthy()
      expect(wrapper.emitted('contact-click')).toHaveLength(1)
      
      // Check that the emitted event contains the correct contact data (match mock data)
      const emittedContact = wrapper.emitted('contact-click')?.[0]?.[0]
      expect(emittedContact).toEqual({
        id: '1',
        full_name: 'John Doe',
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        sms_number: '1234567890'
      })
    })

    it('edit button triggers the correct behavior', async () => {
      wrapper = createWrapper({ 
        value: '1',
        isEditing: true 
      })
      
      // Wait for component to mount and data to load
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))
      
      // Find the edit button
      const editButton = wrapper.find('[data-testid="members_users_id-edit-contact-button"]')
      expect(editButton.exists()).toBe(true)
      
      // Verify the button is properly configured
      expect(editButton.attributes('class')).toContain('edit-contact-button')
      
      // Verify the button has the user-edit icon attribute (PrimeVue Button handles rendering)
      expect(editButton.attributes('icon')).toBe('pi pi-user-edit')
      
      // Click the edit button - this should trigger the field to enter edit mode
      await editButton.trigger('click')
      
      // The button should still exist after click (since it's in display mode)
      expect(editButton.exists()).toBe(true)
    })
  })
}) 