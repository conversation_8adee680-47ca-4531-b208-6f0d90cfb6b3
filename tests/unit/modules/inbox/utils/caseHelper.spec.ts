import { describe, it, expect } from 'vitest'
import { isCaseComposerDisabled, getStatusInfo, isResolvedStatus, isClosedStatus } from '@/modules/inbox/utils/caseHelper'

describe('caseHelper', () => {
  describe('isCaseComposerDisabled', () => {
    it('should return false for undefined status', () => {
      expect(isCaseComposerDisabled(undefined)).toBe(false)
    })

    it('should return false for active statuses', () => {
      // New, Ready, Scheduling, Scheduled, En Route, In Progress, Waiting
      expect(isCaseComposerDisabled('1')).toBe(false) // New
      expect(isCaseComposerDisabled('2')).toBe(false) // Ready
      expect(isCaseComposerDisabled('3')).toBe(false) // Scheduling
      expect(isCaseComposerDisabled('4')).toBe(false) // Scheduled
      expect(isCaseComposerDisabled('5')).toBe(false) // En Route
      expect(isCaseComposerDisabled('6')).toBe(false) // In Progress
      expect(isCaseComposerDisabled('79')).toBe(false) // Waiting
    })

    it('should return true for resolved statuses', () => {
      expect(isCaseComposerDisabled('7')).toBe(true) // Resolved
      expect(isCaseComposerDisabled('9')).toBe(true) // Pending Close
    })

    it('should return true for closed statuses', () => {
      expect(isCaseComposerDisabled('10')).toBe(true) // Closed
      expect(isCaseComposerDisabled('89')).toBe(true) // Canceled
      expect(isCaseComposerDisabled('99')).toBe(true) // Deleted
    })

    it('should return false for unknown status codes', () => {
      expect(isCaseComposerDisabled('999')).toBe(false) // Unknown status defaults to 'new' state
    })
  })

  describe('getStatusInfo', () => {
    it('should return correct status info for known statuses', () => {
      expect(getStatusInfo('1')).toEqual({ label: 'New', state: 'new' })
      expect(getStatusInfo('7')).toEqual({ label: 'Resolved', state: 'resolved' })
      expect(getStatusInfo('10')).toEqual({ label: 'Closed', state: 'closed' })
      expect(getStatusInfo('89')).toEqual({ label: 'Canceled', state: 'closed' })
      expect(getStatusInfo('99')).toEqual({ label: 'Deleted', state: 'closed' })
    })

    it('should return default for undefined status', () => {
      expect(getStatusInfo(undefined)).toEqual({ label: 'Unknown', state: 'new' })
    })
  })

  describe('isResolvedStatus', () => {
    it('should return true for resolved statuses', () => {
      expect(isResolvedStatus('7')).toBe(true) // Resolved
      expect(isResolvedStatus('9')).toBe(true) // Pending Close
    })

    it('should return false for non-resolved statuses', () => {
      expect(isResolvedStatus('1')).toBe(false) // New
      expect(isResolvedStatus('10')).toBe(false) // Closed
      expect(isResolvedStatus('89')).toBe(false) // Canceled
    })
  })

  describe('isClosedStatus', () => {
    it('should return true for closed statuses', () => {
      expect(isClosedStatus('10')).toBe(true) // Closed
      expect(isClosedStatus('89')).toBe(true) // Canceled
      expect(isClosedStatus('99')).toBe(true) // Deleted
    })

    it('should return false for non-closed statuses', () => {
      expect(isClosedStatus('1')).toBe(false) // New
      expect(isClosedStatus('7')).toBe(false) // Resolved
    })
  })
}) 